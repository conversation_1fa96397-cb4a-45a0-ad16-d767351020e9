//! 规则检查辅助方法
//! 
//! 包含规则检查所需的各种计算和查询方法

use crate::unified_engine::{UnifiedRiskEngine, RuleExecutionContext};
use sigmax_core::SigmaXResult;
use chrono::{DateTime, Utc, Timelike, Datelike};
use rust_decimal::prelude::ToPrimitive;
use tracing::debug;

impl UnifiedRiskEngine {
    /// 计算当前回撤
    pub async fn calculate_current_drawdown(&self, context: &RuleExecutionContext) -> SigmaXResult<f64> {
        debug!("计算当前回撤 - 策略: {:?}", context.strategy_type);

        // 1. 根据策略类型获取策略ID列表
        let strategy_ids = if let Some(strategy_type) = &context.strategy_type {
            self.repository().get_strategy_ids_by_type(strategy_type).await?
        } else {
            // 如果没有指定策略类型，无法计算回撤
            debug!("未指定策略类型，无法计算回撤");
            return Ok(0.0);
        };

        if strategy_ids.is_empty() {
            debug!("未找到匹配的策略，回撤为0");
            return Ok(0.0);
        }

        // 2. 获取投资组合回撤数据
        let (initial_capital, current_capital, peak_capital) =
            self.repository().get_portfolio_drawdown_data(&strategy_ids).await?;

        debug!("回撤计算数据 - 初始: {}, 当前: {}, 峰值: {}",
               initial_capital, current_capital, peak_capital);

        // 3. 计算回撤百分比
        let drawdown = if peak_capital > 0.0 {
            ((peak_capital - current_capital) / peak_capital) * 100.0
        } else {
            0.0
        };

        // 4. 如果有具体的策略ID，创建投资组合快照记录
        if let Some(strategy_id) = strategy_ids.first() {
            if let Some(portfolio_id) = self.repository().get_portfolio_id_by_strategy(*strategy_id).await? {
                // 创建快照记录当前状态
                let position_value = self.repository().get_position_values(&strategy_ids).await?;
                let cash_balance = current_capital - position_value;

                if let Err(e) = self.repository().create_portfolio_snapshot(
                    portfolio_id,
                    Some(*strategy_id),
                    current_capital,
                    cash_balance,
                    position_value
                ).await {
                    debug!("创建投资组合快照失败: {}", e);
                }

                // 更新投资组合的回撤信息
                if let Err(e) = self.repository().update_portfolio_drawdown(
                    portfolio_id,
                    current_capital,
                    peak_capital,
                    drawdown / 100.0 // 转换为小数形式
                ).await {
                    debug!("更新投资组合回撤失败: {}", e);
                }
            }
        }

        debug!("计算得到回撤: {:.2}%", drawdown);
        Ok(drawdown)
    }
    
    /// 获取今日交易次数
    pub async fn get_today_trade_count(&self, context: &RuleExecutionContext, exclude_cancelled: bool) -> SigmaXResult<u64> {
        debug!("查询今日交易次数 - 策略: {:?}, 排除已取消: {}", context.strategy_type, exclude_cancelled);

        let Some(strategy_type) = &context.strategy_type else {
            // 如果没有指定策略类型，无法查询，返回0
            return Ok(0);
        };

        // 1. 根据策略类型获取所有策略ID
        let strategy_ids = self.repository().get_strategy_ids_by_type(strategy_type).await?;
        if strategy_ids.is_empty() {
            return Ok(0);
        }

        // 2. 根据策略ID列表查询今日的订单总数（支持排除已取消订单）
        self.repository().get_trade_count_for_strategies_today_with_filter(&strategy_ids, exclude_cancelled).await
    }
    
    /// 获取滑动窗口小时交易次数
    pub async fn get_sliding_hour_trade_count(&self, context: &RuleExecutionContext) -> SigmaXResult<u64> {
        debug!("查询最近一小时交易次数 - 策略: {:?}", context.strategy_type);

        self.repository().get_sliding_hour_trade_count(context.strategy_type.as_deref()).await
    }
    
    /// 获取当前小时交易次数
    pub async fn get_current_hour_trade_count(&self, context: &RuleExecutionContext) -> SigmaXResult<u64> {
        debug!("查询当前小时交易次数 - 策略: {:?}", context.strategy_type);

        self.repository().get_current_hour_trade_count(context.strategy_type.as_deref()).await
    }
    
    /// 获取指定交易对的最后订单时间
    pub async fn get_last_order_time_for_pair(&self, context: &RuleExecutionContext) -> SigmaXResult<Option<DateTime<Utc>>> {
        let trading_pair = context.trading_pair.as_ref();
        debug!("查询交易对最后订单时间 - 交易对: {:?}, 策略: {:?}",
               trading_pair, context.strategy_type);

        if let Some(pair) = trading_pair {
            self.repository().get_last_order_time_for_pair(pair, context.strategy_type.as_deref()).await
        } else {
            Ok(None)
        }
    }
    
    /// 获取全局最后订单时间
    pub async fn get_last_order_time_global(&self, context: &RuleExecutionContext) -> SigmaXResult<Option<DateTime<Utc>>> {
        debug!("查询全局最后订单时间 - 策略: {:?}", context.strategy_type);

        self.repository().get_last_order_time_global(context.strategy_type.as_deref()).await
    }
    
    /// 计算当前杠杆
    pub async fn calculate_current_leverage(&self, context: &RuleExecutionContext) -> SigmaXResult<f64> {
        debug!("计算当前杠杆 - 策略: {:?}", context.strategy_type);

        // 1. 根据策略类型获取策略ID列表
        let strategy_ids = if let Some(strategy_type) = &context.strategy_type {
            self.repository().get_strategy_ids_by_type(strategy_type).await?
        } else {
            debug!("未指定策略类型，无法计算杠杆");
            return Ok(1.0);
        };

        if strategy_ids.is_empty() {
            debug!("未找到匹配的策略，杠杆为1.0");
            return Ok(1.0);
        }

        // 2. 获取总持仓价值
        let total_position_value = self.repository().get_position_values(&strategy_ids).await?;

        // 3. 获取净资产价值（从投资组合数据获取）
        let (_, current_capital, _) = self.repository().get_portfolio_drawdown_data(&strategy_ids).await?;

        // 4. 如果有余额信息，优先使用余额计算净资产
        let net_asset_value = if let Some(balances) = &context.balances {
            // 从余额信息计算总资产价值
            let total_balance_value: f64 = balances.iter()
                .map(|b| b.total().to_f64().unwrap_or(0.0))
                .sum();

            // 净资产 = 总余额价值 + 持仓价值
            total_balance_value + total_position_value
        } else {
            // 使用投资组合的当前资金作为净资产
            current_capital
        };

        debug!("杠杆计算 - 持仓价值: {}, 净资产价值: {}", total_position_value, net_asset_value);

        // 5. 计算杠杆比率
        let leverage = if net_asset_value > 0.0 {
            total_position_value / net_asset_value
        } else {
            1.0 // 如果净资产为0，返回1.0避免除零错误
        };

        debug!("计算得到杠杆: {:.4}", leverage);
        Ok(leverage)
    }
    
    /// 计算市场波动率
    pub async fn calculate_market_volatility(&self, context: &RuleExecutionContext) -> SigmaXResult<f64> {
        let trading_pair = context.trading_pair.as_ref();
        debug!("计算市场波动率 - 交易对: {:?}", trading_pair);

        let Some(pair) = trading_pair else {
            debug!("未指定交易对，返回默认波动率");
            return Ok(18.0);
        };

        // 1. 获取最近N个周期的价格数据（默认30个周期）
        let periods = 30;
        let prices = match self.get_historical_prices(pair, periods).await {
            Ok(prices) if !prices.is_empty() => prices,
            Ok(_) => {
                debug!("未获取到历史价格数据，使用默认波动率");
                return Ok(self.get_default_volatility(pair));
            }
            Err(e) => {
                debug!("获取历史价格数据失败: {}, 使用默认波动率", e);
                return Ok(self.get_default_volatility(pair));
            }
        };

        // 2. 计算收益率序列
        let mut returns = Vec::new();
        for i in 1..prices.len() {
            let current_price = prices[i - 1]; // 最新价格在前
            let previous_price = prices[i];

            if previous_price > 0.0 {
                let return_rate = (current_price - previous_price) / previous_price;
                returns.push(return_rate);
            }
        }

        if returns.is_empty() {
            debug!("无法计算收益率，使用默认波动率");
            return Ok(self.get_default_volatility(pair));
        }

        // 3. 计算标准差（波动率）
        let volatility = self.calculate_standard_deviation(&returns);

        // 4. 年化波动率（假设每日数据，年化系数为sqrt(365)）
        let annualized_volatility = volatility * (365.0_f64).sqrt() * 100.0; // 转换为百分比

        debug!("计算得到波动率: {:.2}% (基于{}个数据点)", annualized_volatility, returns.len());
        Ok(annualized_volatility)
    }

    /// 获取默认波动率
    fn get_default_volatility(&self, trading_pair: &str) -> f64 {
        match trading_pair {
            pair if pair.contains("BTC") => 25.5, // BTC波动率较高
            pair if pair.contains("ETH") => 20.3, // ETH波动率中等
            pair if pair.contains("BNB") => 22.1, // BNB波动率
            pair if pair.contains("ADA") => 28.7, // ADA波动率较高
            pair if pair.contains("DOT") => 26.4, // DOT波动率
            pair if pair.contains("USDT") || pair.contains("USDC") => 1.2, // 稳定币波动率很低
            _ => 18.0, // 其他币种默认波动率
        }
    }
    
    /// 获取市场流动性
    pub async fn get_market_liquidity(&self, context: &RuleExecutionContext) -> SigmaXResult<f64> {
        let trading_pair = context.trading_pair.as_ref();
        debug!("获取市场流动性 - 交易对: {:?}", trading_pair);

        let Some(pair) = trading_pair else {
            debug!("未指定交易对，返回默认流动性");
            return Ok(25000.0);
        };

        // 1. 尝试从数据库获取最新的流动性数据
        match self.repository().get_market_liquidity_data(pair, None).await {
            Ok(Some((liquidity_score, spread_percentage, volume_24h))) => {
                debug!("获取到流动性数据 - 评分: {}, 价差: {}%, 24h交易量: {}",
                       liquidity_score, spread_percentage, volume_24h);

                // 2. 基于流动性评分和交易量计算综合流动性指标
                // 流动性指标 = 基础分数 * 交易量权重 * 价差调整
                let base_score = liquidity_score * 1000.0; // 基础分数
                let volume_weight = (volume_24h / 1000000.0).min(2.0).max(0.1); // 交易量权重，限制在0.1-2.0之间
                let spread_adjustment = if spread_percentage > 0.0 {
                    (1.0 / (1.0 + spread_percentage)).max(0.1) // 价差越大，流动性越差
                } else {
                    1.0
                };

                let liquidity_indicator = base_score * volume_weight * spread_adjustment;

                debug!("计算得到流动性指标: {:.2} (基础: {}, 交易量权重: {:.2}, 价差调整: {:.2})",
                       liquidity_indicator, base_score, volume_weight, spread_adjustment);

                Ok(liquidity_indicator)
            }
            Ok(None) => {
                debug!("未找到流动性数据，使用默认值");
                Ok(self.get_default_liquidity_value(pair))
            }
            Err(e) => {
                debug!("查询流动性数据失败: {}, 使用默认值", e);
                Ok(self.get_default_liquidity_value(pair))
            }
        }
    }

    /// 获取默认流动性值
    fn get_default_liquidity_value(&self, trading_pair: &str) -> f64 {
        match trading_pair {
            pair if pair.contains("BTC") => 85000.0, // BTC流动性很好
            pair if pair.contains("ETH") => 65000.0, // ETH流动性好
            pair if pair.contains("BNB") => 45000.0, // BNB流动性较好
            pair if pair.contains("USDT") || pair.contains("USDC") => 95000.0, // 稳定币流动性最好
            pair if pair.contains("ADA") || pair.contains("DOT") => 35000.0, // 主流币流动性中等
            _ => 20000.0, // 其他币种流动性一般
        }
    }
    
    /// 获取历史价格数据
    pub async fn get_historical_prices(&self, trading_pair: &str, periods: usize) -> SigmaXResult<Vec<f64>> {
        debug!("获取历史价格数据 - 交易对: {}, 周期数: {}", trading_pair, periods);

        self.repository().get_historical_prices(trading_pair, periods).await
    }
    
    /// 计算价格变化率
    pub fn calculate_price_change_rate(&self, prices: &[f64]) -> f64 {
        if prices.len() < 2 {
            return 0.0;
        }
        
        let latest = prices[0];
        let previous = prices[1];
        
        if previous > 0.0 {
            ((latest - previous) / previous) * 100.0
        } else {
            0.0
        }
    }
    
    /// 计算移动平均
    pub fn calculate_moving_average(&self, prices: &[f64], window: usize) -> Option<f64> {
        if prices.len() < window {
            return None;
        }
        
        let sum: f64 = prices.iter().take(window).sum();
        Some(sum / window as f64)
    }
    
    /// 计算标准差
    pub fn calculate_standard_deviation(&self, values: &[f64]) -> f64 {
        if values.is_empty() {
            return 0.0;
        }
        
        let mean = values.iter().sum::<f64>() / values.len() as f64;
        let variance = values.iter()
            .map(|x| (x - mean).powi(2))
            .sum::<f64>() / values.len() as f64;
            
        variance.sqrt()
    }
    
    /// 检查市场开放状态
    pub async fn is_market_open(&self, trading_pair: &str, exchange_name: Option<&str>) -> SigmaXResult<bool> {
        debug!("检查市场开放状态 - 交易对: {}, 交易所: {:?}", trading_pair, exchange_name);

        // 1. 从数据库查询交易时间配置
        match self.repository().is_trading_pair_open(trading_pair, exchange_name).await {
            Ok(is_open) => {
                debug!("数据库查询结果 - 市场开放: {}", is_open);
                Ok(is_open)
            }
            Err(e) => {
                debug!("查询交易时间配置失败: {}, 使用默认逻辑", e);

                // 2. 如果查询失败，使用默认逻辑
                Ok(self.is_market_open_default(trading_pair, exchange_name))
            }
        }
    }

    /// 默认的市场开放状态检查逻辑
    fn is_market_open_default(&self, trading_pair: &str, exchange_name: Option<&str>) -> bool {
        // 1. 加密货币交易所通常24/7开放
        if let Some(exchange) = exchange_name {
            match exchange.to_lowercase().as_str() {
                "binance" | "coinbase" | "kraken" | "simulator" => {
                    debug!("加密货币交易所 {} 24/7开放", exchange);
                    return true;
                }
                _ => {
                    // 其他交易所，继续检查交易对
                }
            }
        }

        // 2. 根据交易对判断
        if trading_pair.contains("BTC") || trading_pair.contains("ETH") ||
           trading_pair.contains("BNB") || trading_pair.contains("ADA") ||
           trading_pair.contains("DOT") || trading_pair.contains("USDT") ||
           trading_pair.contains("USDC") {
            debug!("加密货币交易对 {} 24/7开放", trading_pair);
            return true;
        }

        // 3. 传统金融市场的交易时间检查
        let now = Utc::now();
        let hour = now.hour();
        let weekday = now.weekday();

        use chrono::Weekday;

        // 检查是否为工作日
        let is_weekday = matches!(weekday, Weekday::Mon | Weekday::Tue | Weekday::Wed | Weekday::Thu | Weekday::Fri);

        if !is_weekday {
            debug!("非工作日，传统市场关闭");
            return false;
        }

        // 模拟不同地区的股票市场交易时间（UTC时间）
        let is_open = match trading_pair {
            // 美股市场 (UTC 14:30-21:00, 对应EST 9:30-16:00)
            pair if pair.contains("USD") && !pair.contains("USDT") && !pair.contains("USDC") => {
                hour >= 14 && hour < 21
            }
            // 欧洲市场 (UTC 8:00-16:30)
            pair if pair.contains("EUR") => {
                hour >= 8 && hour < 17
            }
            // 亚洲市场 (UTC 1:00-7:00, 对应JST 10:00-16:00)
            pair if pair.contains("JPY") => {
                hour >= 1 && hour < 7
            }
            // 其他传统市场，使用通用时间
            _ => {
                hour >= 9 && hour < 16
            }
        };

        debug!("传统市场 {} 开放状态: {} (当前UTC时间: {}:00)", trading_pair, is_open, hour);
        is_open
    }
    
    /// 获取风险预算使用情况
    pub async fn get_risk_budget_usage(&self, context: &RuleExecutionContext) -> SigmaXResult<f64> {
        debug!("获取风险预算使用情况 - 策略: {:?}", context.strategy_type);

        // 1. 从数据库获取风险预算配置
        match self.repository().get_risk_budget_config(context.strategy_type.as_deref()).await {
            Ok(Some((total_budget, used_budget, usage_percentage))) => {
                debug!("风险预算数据 - 总预算: {}, 已使用: {}, 使用率: {:.2}%",
                       total_budget, used_budget, usage_percentage);

                // 2. 如果有策略类型，创建使用记录
                if let Some(strategy_type) = &context.strategy_type {
                    // 获取策略ID列表
                    let strategy_ids = self.repository().get_strategy_ids_by_type(strategy_type).await?;
                    let strategy_id = strategy_ids.first().copied();

                    // 创建风险预算使用记录
                    if let Err(e) = self.repository().create_risk_budget_usage_record(
                        Some(strategy_type),
                        strategy_id,
                        total_budget,
                        used_budget,
                        usage_percentage
                    ).await {
                        debug!("创建风险预算使用记录失败: {}", e);
                    }
                }

                Ok(usage_percentage)
            }
            Ok(None) => {
                debug!("未找到风险预算配置，使用默认计算");

                // 3. 如果没有配置，尝试基于策略计算
                if let Some(strategy_type) = &context.strategy_type {
                    match self.calculate_strategy_risk_usage(strategy_type).await {
                        Ok(usage) => {
                            debug!("基于策略计算的风险使用率: {:.2}%", usage);
                            Ok(usage)
                        }
                        Err(e) => {
                            debug!("计算策略风险使用率失败: {}, 返回默认值", e);
                            Ok(self.get_default_risk_usage(strategy_type))
                        }
                    }
                } else {
                    debug!("未指定策略类型，返回默认风险使用率");
                    Ok(50.0) // 默认50%使用率
                }
            }
            Err(e) => {
                debug!("查询风险预算配置失败: {}, 使用默认值", e);

                let default_usage = if let Some(strategy_type) = &context.strategy_type {
                    self.get_default_risk_usage(strategy_type)
                } else {
                    50.0
                };

                Ok(default_usage)
            }
        }
    }

    /// 计算策略的风险使用率
    async fn calculate_strategy_risk_usage(&self, strategy_type: &str) -> SigmaXResult<f64> {
        // 1. 获取策略的风险预算分配
        let allocated_budget = self.repository().get_strategy_risk_allocation(strategy_type).await?
            .unwrap_or(100000.0); // 默认10万预算

        // 2. 获取策略ID列表
        let strategy_ids = self.repository().get_strategy_ids_by_type(strategy_type).await?;

        if strategy_ids.is_empty() {
            return Ok(0.0);
        }

        // 3. 计算当前风险使用情况
        let current_usage = self.repository().calculate_current_risk_usage(&strategy_ids).await?;

        // 4. 计算使用百分比
        let usage_percentage = if allocated_budget > 0.0 {
            (current_usage / allocated_budget) * 100.0
        } else {
            0.0
        };

        debug!("策略 {} 风险使用情况 - 分配预算: {}, 当前使用: {}, 使用率: {:.2}%",
               strategy_type, allocated_budget, current_usage, usage_percentage);

        Ok(usage_percentage)
    }

    /// 获取默认风险使用率
    fn get_default_risk_usage(&self, strategy_type: &str) -> f64 {
        match strategy_type {
            "grid_trading" => 65.5,      // 网格交易策略风险使用率较高
            "arbitrage" => 45.2,         // 套利策略风险使用率中等
            "market_making" => 72.8,     // 做市策略风险使用率高
            "trend_following" => 58.3,   // 趋势跟踪策略风险使用率中等偏高
            "mean_reversion" => 52.1,    // 均值回归策略风险使用率中等
            _ => 50.0,                   // 其他策略默认50%
        }
    }
}
