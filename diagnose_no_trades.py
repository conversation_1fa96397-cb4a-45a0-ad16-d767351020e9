#!/usr/bin/env python3
"""
诊断为什么回测没有生成任何交易
"""

import requests
import json
import os
from datetime import datetime

def diagnose_no_trades():
    """诊断没有交易的原因"""
    engine_id = "9c503833-d30f-4e7d-bdba-6d46107f7801"
    base_url = "http://127.0.0.1:8080/api/v1"
    
    print("🔍 诊断回测没有生成交易的原因")
    print("=" * 70)
    
    # 1. 检查数据文件内容
    print("\n📊 1. 检查数据文件内容")
    data_file = "bt_klines/BNB_USDT_1d_60.json"
    try:
        with open(data_file, 'r') as f:
            data = json.load(f)
            print(f"✅ 数据文件包含 {len(data)} 条K线")
            
            if len(data) >= 5:
                print("📈 前5条K线数据:")
                for i, candle in enumerate(data[:5]):
                    timestamp = candle[0]
                    open_price = candle[1]
                    high = candle[2]
                    low = candle[3]
                    close = candle[4]
                    
                    # 转换时间戳
                    dt = datetime.fromtimestamp(timestamp / 1000)
                    print(f"  {i+1}. {dt.strftime('%Y-%m-%d')} O:{open_price} H:{high} L:{low} C:{close}")
                
                # 计算价格波动
                prices = [float(candle[4]) for candle in data]  # 收盘价
                min_price = min(prices)
                max_price = max(prices)
                price_range = ((max_price - min_price) / min_price) * 100
                
                print(f"\n📊 价格分析:")
                print(f"  最低价: {min_price:.2f}")
                print(f"  最高价: {max_price:.2f}")
                print(f"  价格波动范围: {price_range:.2f}%")
                
                # 检查是否有足够的波动
                if price_range < 5:
                    print("⚠️ 价格波动较小，可能不足以触发网格交易")
                elif price_range > 100:
                    print("📈 价格波动很大，应该能触发网格交易")
                else:
                    print("✅ 价格波动适中，应该能触发一些交易")
                    
    except Exception as e:
        print(f"❌ 读取数据文件失败: {e}")
    
    # 2. 检查回测配置
    print("\n⚙️ 2. 检查回测配置")
    try:
        config_response = requests.get(f"{base_url}/engines/{engine_id}/backtest/config")
        if config_response.status_code == 200:
            config_data = config_response.json()["data"]
            print("✅ 获取配置成功")
            print("📋 当前配置:")
            print(json.dumps(config_data, indent=2, ensure_ascii=False))
            
            # 分析配置问题
            strategy_config = config_data.get("strategy_config", {}).get("config", {})
            
            print("\n🔍 配置分析:")
            grid_count = strategy_config.get("grid_count", 0)
            price_range_percent = strategy_config.get("price_range_percent", 0)
            base_order_amount = strategy_config.get("base_order_amount", 0)
            
            print(f"  网格数量: {grid_count}")
            print(f"  价格范围: {price_range_percent}%")
            print(f"  基础订单金额: {base_order_amount}")
            
            if price_range_percent > 50:
                print("⚠️ 价格范围设置过大，可能导致网格无法触发")
            if base_order_amount > 1000:
                print("⚠️ 基础订单金额过大，可能超出资金限制")
            if grid_count < 3:
                print("⚠️ 网格数量过少，可能错过交易机会")
                
        else:
            print(f"❌ 获取配置失败: {config_response.status_code}")
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
    
    # 3. 检查引擎状态和日志
    print("\n🔧 3. 检查引擎状态")
    try:
        status_response = requests.get(f"{base_url}/engines/{engine_id}/status")
        if status_response.status_code == 200:
            status_data = status_response.json()["data"]
            print("✅ 引擎状态:")
            print(f"  状态: {status_data.get('status', 'Unknown')}")
            print(f"  类型: {status_data.get('engine_type', 'Unknown')}")
            
            # 检查是否有错误信息
            if 'error' in status_data:
                print(f"❌ 引擎错误: {status_data['error']}")
        else:
            print(f"❌ 获取引擎状态失败: {status_response.status_code}")
    except Exception as e:
        print(f"❌ 检查引擎状态失败: {e}")
    
    # 4. 检查策略是否正确加载
    print("\n📈 4. 检查策略信息")
    try:
        # 获取支持的策略列表
        strategies_response = requests.get(f"{base_url}/strategies/supported")
        if strategies_response.status_code == 200:
            strategies = strategies_response.json()["data"]
            print("✅ 支持的策略:")
            for strategy in strategies:
                print(f"  - {strategy}")
                
            # 检查我们使用的策略是否在列表中
            if "adaptive_volatility_grid" in strategies:
                print("✅ adaptive_volatility_grid 策略已支持")
            else:
                print("❌ adaptive_volatility_grid 策略未找到")
                print("💡 建议使用其他策略，如 'grid' 或 'simple_grid'")
        else:
            print(f"❌ 获取策略列表失败: {strategies_response.status_code}")
    except Exception as e:
        print(f"❌ 检查策略失败: {e}")
    
    # 5. 检查风险控制
    print("\n🛡️ 5. 检查风险控制")
    try:
        risk_response = requests.get(f"{base_url}/risk/rules")
        if risk_response.status_code == 200:
            rules = risk_response.json()["data"]
            print(f"✅ 找到 {len(rules)} 条风险规则")
            
            active_rules = [rule for rule in rules if rule.get("enabled", False)]
            print(f"📋 启用的风险规则: {len(active_rules)} 条")
            
            for rule in active_rules:
                print(f"  - {rule['name']}: {rule['rule_type']}")
                
            if len(active_rules) > 5:
                print("⚠️ 启用的风险规则较多，可能阻止交易")
        else:
            print(f"❌ 获取风险规则失败: {risk_response.status_code}")
    except Exception as e:
        print(f"❌ 检查风险控制失败: {e}")
    
    # 6. 生成诊断报告
    print("\n📋 6. 诊断报告和建议")
    print("=" * 70)
    print("🔍 可能的原因:")
    print("1. 策略类型问题 - adaptive_volatility_grid 可能未正确实现")
    print("2. 参数设置问题 - 价格范围或网格参数不合适")
    print("3. 风险控制过严 - 某些风险规则阻止了交易")
    print("4. 数据格式问题 - 策略可能无法正确解析数据")
    print("5. 初始化问题 - 策略可能没有正确初始化")
    
    print("\n💡 建议的解决方案:")
    print("1. 尝试使用简单的 'grid' 策略替代 'adaptive_volatility_grid'")
    print("2. 减小价格范围参数 (如改为 5-10%)")
    print("3. 增加网格数量 (如改为 15-20)")
    print("4. 减小基础订单金额 (如改为 50 USDT)")
    print("5. 检查服务器日志文件中的详细错误信息")
    
    return True

def suggest_new_config():
    """建议新的配置"""
    print("\n🔧 建议的新配置:")
    
    suggested_config = {
        "data_file": "BNB_USDT_1d_60.json",
        "start_time": "2021-01-01T00:00:00Z",
        "end_time": "2021-02-28T23:59:59Z",
        "initial_capital": "5000.00",
        "trading_pairs": ["BNB_USDT"],
        "timeframe": "1d",
        "strategy_config": {
            "type": "grid",  # 改为简单网格策略
            "config": {
                "grid_count": 15,                    # 增加网格数量
                "price_range_percent": 8.0,          # 减小价格范围
                "base_order_amount": 50.0,           # 减小基础订单
                "safety_order_amount": 75.0,
                "take_profit_percent": 2.0,
                "stop_loss_percent": 5.0,
                "max_safety_orders": 3
            }
        }
    }
    
    print(json.dumps(suggested_config, indent=2, ensure_ascii=False))
    
    return suggested_config

if __name__ == "__main__":
    diagnose_no_trades()
    suggest_new_config()
