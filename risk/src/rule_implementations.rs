//! 具体风控规则实现
//! 
//! 包含所有风控规则类型的具体检查逻辑

use crate::unified_engine::{UnifiedRiskEngine, UnifiedRiskRule, RuleExecutionContext, RuleResult};
use sigmax_core::{Order, Balance};
use chrono::{Utc, Timelike, Datelike};
use rust_decimal::prelude::ToPrimitive;
use tracing::warn;

impl UnifiedRiskEngine {
    /// 检查订单金额限制
    pub async fn check_order_amount_limit(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        let Some(order) = &context.order else {
            return RuleResult::Skip("无订单信息".to_string());
        };
        
        let max_amount = rule.parameters["max_amount"]
            .as_f64()
            .unwrap_or(f64::MAX);
            
        let include_fees = rule.parameters["include_fees"]
            .as_bool()
            .unwrap_or(true);
            
        let order_value = order.quantity.to_f64().unwrap_or(0.0) * 
                         order.price.unwrap_or_default().to_f64().unwrap_or(0.0);
                         
        let final_amount = if include_fees {
            order_value * 1.001 // 假设0.1%手续费
        } else {
            order_value
        };
        
        if final_amount > max_amount {
            RuleResult::Fail(format!(
                "订单金额 {:.2} 超过限制 {:.2}", 
                final_amount, max_amount
            ))
        } else {
            RuleResult::Pass
        }
    }
    
    /// 检查订单数量限制
    pub async fn check_order_quantity_limit(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        let Some(order) = &context.order else {
            return RuleResult::Skip("无订单信息".to_string());
        };
        
        let max_quantity = rule.parameters["max_quantity"]
            .as_f64()
            .unwrap_or(f64::MAX);
            
        let min_quantity = rule.parameters["min_quantity"]
            .as_f64()
            .unwrap_or(0.0);
            
        let quantity = order.quantity.to_f64().unwrap_or(0.0);
        
        if quantity > max_quantity {
            RuleResult::Fail(format!(
                "订单数量 {} 超过最大限制 {}", 
                quantity, max_quantity
            ))
        } else if quantity < min_quantity {
            RuleResult::Fail(format!(
                "订单数量 {} 低于最小限制 {}", 
                quantity, min_quantity
            ))
        } else {
            RuleResult::Pass
        }
    }
    
    /// 检查最大回撤保护
    pub async fn check_max_drawdown_protection(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        let max_drawdown_percent = rule.parameters["max_drawdown_percent"]
            .as_f64()
            .unwrap_or(100.0);
            
        // 使用辅助函数计算当前回撤
        let current_drawdown = self.calculate_current_drawdown(context).await;
        
        match current_drawdown {
            Ok(drawdown) => {
                if drawdown > max_drawdown_percent {
                    RuleResult::Fail(format!(
                        "当前回撤 {:.2}% 超过最大限制 {:.2}%", 
                        drawdown, max_drawdown_percent
                    ))
                } else {
                    RuleResult::Pass
                }
            }
            Err(e) => {
                warn!("计算回撤失败: {}", e);
                RuleResult::Error(format!("无法计算当前回撤: {}", e))
            }
        }
    }
    
    /// 检查日交易频率
    pub async fn check_daily_trading_frequency(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        let max_daily_trades = rule.parameters["max_daily_trades"]
            .as_u64()
            .unwrap_or(u64::MAX);
            
        let exclude_cancelled = rule.parameters["exclude_cancelled"]
            .as_bool()
            .unwrap_or(true);
            
        // 使用辅助函数获取今日交易次数
        let today_trades = self.get_today_trade_count(context, exclude_cancelled).await;
        
        match today_trades {
            Ok(count) => {
                if count >= max_daily_trades {
                    RuleResult::Fail(format!(
                        "今日交易次数 {} 已达到限制 {}", 
                        count, max_daily_trades
                    ))
                } else {
                    RuleResult::Pass
                }
            }
            Err(e) => {
                warn!("查询今日交易次数失败: {}", e);
                RuleResult::Error(format!("无法查询交易频率: {}", e))
            }
        }
    }
    
    /// 检查小时交易频率
    pub async fn check_hourly_trading_frequency(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        let max_hourly_trades = rule.parameters["max_hourly_trades"]
            .as_u64()
            .unwrap_or(u64::MAX);
            
        let sliding_window = rule.parameters["sliding_window"]
            .as_bool()
            .unwrap_or(true);
            
        // 使用辅助函数获取小时交易次数
        let recent_trades = if sliding_window {
            self.get_sliding_hour_trade_count(context).await
        } else {
            self.get_current_hour_trade_count(context).await
        };
        
        match recent_trades {
            Ok(count) => {
                if count >= max_hourly_trades {
                    RuleResult::Fail(format!(
                        "小时交易次数 {} 已达到限制 {}", 
                        count, max_hourly_trades
                    ))
                } else {
                    RuleResult::Pass
                }
            }
            Err(e) => {
                warn!("查询小时交易次数失败: {}", e);
                RuleResult::Error(format!("无法查询交易频率: {}", e))
            }
        }
    }
    
    /// 检查最小订单间隔
    pub async fn check_min_order_interval(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        let min_interval_seconds = rule.parameters["min_interval_seconds"]
            .as_u64()
            .unwrap_or(0);
            
        let per_trading_pair = rule.parameters["per_trading_pair"]
            .as_bool()
            .unwrap_or(true);
            
        // 使用辅助函数获取最后一笔订单时间
        let last_order_time = if per_trading_pair {
            self.get_last_order_time_for_pair(context).await
        } else {
            self.get_last_order_time_global(context).await
        };
        
        match last_order_time {
            Ok(Some(last_time)) => {
                let now = Utc::now();
                let elapsed = now.signed_duration_since(last_time).num_seconds() as u64;
                
                if elapsed < min_interval_seconds {
                    RuleResult::Fail(format!(
                        "距离上次订单仅 {} 秒，未达到最小间隔 {} 秒", 
                        elapsed, min_interval_seconds
                    ))
                } else {
                    RuleResult::Pass
                }
            }
            Ok(None) => {
                // 没有历史订单，允许通过
                RuleResult::Pass
            }
            Err(e) => {
                warn!("查询最后订单时间失败: {}", e);
                RuleResult::Error(format!("无法查询订单间隔: {}", e))
            }
        }
    }
    
    /// 检查交易时间窗口
    pub async fn check_trading_time_window(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        let default_hours = vec![];
        let trading_hours = rule.parameters["trading_hours"]
            .as_array()
            .unwrap_or(&default_hours);
            
        let now = Utc::now();
        let current_weekday = now.weekday().num_days_from_monday() as u8;
        let current_time = format!("{:02}:{:02}", now.hour(), now.minute());
        
        // 检查是否在允许的交易时间内
        for time_window in trading_hours {
            let day_of_week = time_window["day_of_week"].as_u64().unwrap_or(7) as u8;
            let start_time = time_window["start_time"].as_str().unwrap_or("00:00");
            let end_time = time_window["end_time"].as_str().unwrap_or("23:59");
            
            // 检查星期几匹配
            if day_of_week == current_weekday || day_of_week == 7 { // 7表示每天
                // 检查时间范围
                if current_time.as_str() >= start_time && current_time.as_str() <= end_time {
                    return RuleResult::Pass;
                }
            }
        }
        
        RuleResult::Fail(format!(
            "当前时间 {} (星期{}) 不在允许的交易时间窗口内", 
            current_time, current_weekday + 1
        ))
    }
    
    /// 检查单个持仓限制
    pub async fn check_single_position_limit(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        let max_position_percent = rule.parameters["max_position_percent"]
            .as_f64()
            .unwrap_or(100.0);
            
        let Some(balances) = &context.balances else {
            return RuleResult::Skip("无持仓信息".to_string());
        };
        
        // 计算总资产价值
        let total_value: f64 = balances.iter()
            .map(|b| b.total().to_f64().unwrap_or(0.0))
            .sum();
            
        if total_value == 0.0 {
            return RuleResult::Pass; // 无持仓时通过
        }
        
        // 检查每个持仓是否超过限制
        for balance in balances {
            let position_value = balance.total().to_f64().unwrap_or(0.0);
            let position_percent = (position_value / total_value) * 100.0;
            
            if position_percent > max_position_percent {
                return RuleResult::Fail(format!(
                    "{}持仓占比 {:.2}% 超过限制 {:.2}%", 
                    balance.asset, position_percent, max_position_percent
                ));
            }
        }
        
        RuleResult::Pass
    }
    
    /// 检查最大杠杆限制
    pub async fn check_max_leverage_limit(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        let max_leverage = rule.parameters["max_leverage"]
            .as_f64()
            .unwrap_or(1.0);
            
        // 使用辅助函数计算当前杠杆
        let current_leverage = self.calculate_current_leverage(context).await;
        
        match current_leverage {
            Ok(leverage) => {
                if leverage > max_leverage {
                    RuleResult::Fail(format!(
                        "当前杠杆 {:.2}x 超过最大限制 {:.2}x", 
                        leverage, max_leverage
                    ))
                } else {
                    RuleResult::Pass
                }
            }
            Err(e) => {
                warn!("计算杠杆失败: {}", e);
                RuleResult::Error(format!("无法计算当前杠杆: {}", e))
            }
        }
    }
    
    /// 检查波动率阈值
    pub async fn check_volatility_threshold(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        let max_volatility = rule.parameters["max_volatility_percent"]
            .as_f64()
            .unwrap_or(100.0);
            
        // 使用辅助函数计算市场波动率
        let current_volatility = self.calculate_market_volatility(context).await;
        
        match current_volatility {
            Ok(volatility) => {
                if volatility > max_volatility {
                    RuleResult::Fail(format!(
                        "市场波动率 {:.2}% 超过阈值 {:.2}%", 
                        volatility, max_volatility
                    ))
                } else {
                    RuleResult::Pass
                }
            }
            Err(e) => {
                warn!("计算波动率失败: {}", e);
                RuleResult::Error(format!("无法计算市场波动率: {}", e))
            }
        }
    }
    
    /// 检查最小流动性要求
    pub async fn check_min_liquidity_requirement(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        let min_liquidity = rule.parameters["min_liquidity_amount"]
            .as_f64()
            .unwrap_or(0.0);
            
        // 使用辅助函数获取市场流动性
        let current_liquidity = self.get_market_liquidity(context).await;
        
        match current_liquidity {
            Ok(liquidity) => {
                if liquidity < min_liquidity {
                    RuleResult::Fail(format!(
                        "市场流动性 {:.2} 低于最小要求 {:.2}", 
                        liquidity, min_liquidity
                    ))
                } else {
                    RuleResult::Pass
                }
            }
            Err(e) => {
                warn!("获取流动性数据失败: {}", e);
                RuleResult::Error(format!("无法获取市场流动性: {}", e))
            }
        }
    }
}
