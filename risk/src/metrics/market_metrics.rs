//! 市场相关指标计算模块
//! 
//! 实现与市场基准相关的风险指标

use sigmax_core::SigmaXResult;
use rust_decimal::{Decimal, MathematicalOps};
use std::collections::VecDeque;
use serde::{Serialize, Deserialize};

/// 市场相关指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketMetrics {
    /// Beta系数
    pub beta: Decimal,
    /// Alpha系数
    pub alpha: Decimal,
    /// 跟踪误差
    pub tracking_error: Decimal,
    /// 相关系数
    pub correlation: Decimal,
}

/// 市场指标计算器
pub struct MarketCalculator<'a> {
    returns_history: &'a VecDeque<Decimal>,
    benchmark_returns: &'a VecDeque<Decimal>,
    risk_free_rate: Decimal,
}

impl<'a> MarketCalculator<'a> {
    pub fn new(
        returns_history: &'a VecDeque<Decimal>,
        benchmark_returns: &'a VecDeque<Decimal>,
        risk_free_rate: Decimal,
    ) -> Self {
        Self {
            returns_history,
            benchmark_returns,
            risk_free_rate,
        }
    }

    /// 计算所有市场相关指标
    pub fn calculate(&self) -> SigmaXResult<MarketMetrics> {
        Ok(MarketMetrics {
            beta: self.calculate_beta()?,
            alpha: self.calculate_alpha()?,
            tracking_error: self.calculate_tracking_error()?,
            correlation: self.calculate_correlation()?,
        })
    }

    /// 计算Beta系数
    pub fn calculate_beta(&self) -> SigmaXResult<Decimal> {
        if self.benchmark_returns.is_empty() || self.returns_history.len() != self.benchmark_returns.len() {
            return Ok(Decimal::ONE);
        }

        if self.returns_history.len() < 2 {
            return Ok(Decimal::ONE);
        }

        let portfolio_mean = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let benchmark_mean = self.benchmark_returns.iter().sum::<Decimal>() / Decimal::from(self.benchmark_returns.len());

        let covariance = self.returns_history
            .iter()
            .zip(self.benchmark_returns.iter())
            .map(|(p, b)| (p - portfolio_mean) * (b - benchmark_mean))
            .sum::<Decimal>() / Decimal::from(self.returns_history.len() - 1);

        let benchmark_variance = self.benchmark_returns
            .iter()
            .map(|b| (b - benchmark_mean).powi(2))
            .sum::<Decimal>() / Decimal::from(self.benchmark_returns.len() - 1);

        if benchmark_variance == Decimal::ZERO {
            return Ok(Decimal::ONE);
        }

        Ok(covariance / benchmark_variance)
    }

    /// 计算Alpha系数
    pub fn calculate_alpha(&self) -> SigmaXResult<Decimal> {
        if self.benchmark_returns.is_empty() || self.returns_history.len() != self.benchmark_returns.len() {
            return Ok(Decimal::ZERO);
        }

        let portfolio_mean = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let benchmark_mean = self.benchmark_returns.iter().sum::<Decimal>() / Decimal::from(self.benchmark_returns.len());
        let beta = self.calculate_beta()?;

        // Alpha = Portfolio Return - (Risk Free Rate + Beta * (Benchmark Return - Risk Free Rate))
        let daily_risk_free = self.risk_free_rate / Decimal::from(252);
        let portfolio_excess = portfolio_mean - daily_risk_free;
        let benchmark_excess = benchmark_mean - daily_risk_free;
        let expected_excess = beta * benchmark_excess;

        // 年化Alpha
        Ok((portfolio_excess - expected_excess) * Decimal::from(252))
    }

    /// 计算跟踪误差
    pub fn calculate_tracking_error(&self) -> SigmaXResult<Decimal> {
        if self.benchmark_returns.is_empty() || self.returns_history.len() != self.benchmark_returns.len() {
            return Ok(Decimal::ZERO);
        }

        if self.returns_history.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        // 计算超额收益
        let excess_returns: Vec<Decimal> = self.returns_history
            .iter()
            .zip(self.benchmark_returns.iter())
            .map(|(r, b)| r - b)
            .collect();

        if excess_returns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let mean_excess = excess_returns.iter().sum::<Decimal>() / Decimal::from(excess_returns.len());
        
        // 计算超额收益的标准差
        let variance = excess_returns
            .iter()
            .map(|r| (r - mean_excess).powi(2))
            .sum::<Decimal>() / Decimal::from(excess_returns.len() - 1);
        
        let tracking_error = variance.sqrt().unwrap_or(Decimal::ZERO);
        
        // 年化跟踪误差
        Ok(tracking_error * Decimal::from(252).sqrt().unwrap_or(Decimal::from(16)))
    }

    /// 计算相关系数
    pub fn calculate_correlation(&self) -> SigmaXResult<Decimal> {
        if self.benchmark_returns.is_empty() || self.returns_history.len() != self.benchmark_returns.len() {
            return Ok(Decimal::ZERO);
        }

        if self.returns_history.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let portfolio_mean = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let benchmark_mean = self.benchmark_returns.iter().sum::<Decimal>() / Decimal::from(self.benchmark_returns.len());

        let covariance = self.returns_history
            .iter()
            .zip(self.benchmark_returns.iter())
            .map(|(p, b)| (p - portfolio_mean) * (b - benchmark_mean))
            .sum::<Decimal>() / Decimal::from(self.returns_history.len() - 1);

        let portfolio_variance = self.returns_history
            .iter()
            .map(|p| (p - portfolio_mean).powi(2))
            .sum::<Decimal>() / Decimal::from(self.returns_history.len() - 1);

        let benchmark_variance = self.benchmark_returns
            .iter()
            .map(|b| (b - benchmark_mean).powi(2))
            .sum::<Decimal>() / Decimal::from(self.benchmark_returns.len() - 1);

        let portfolio_std = portfolio_variance.sqrt().unwrap_or(Decimal::ZERO);
        let benchmark_std = benchmark_variance.sqrt().unwrap_or(Decimal::ZERO);

        if portfolio_std == Decimal::ZERO || benchmark_std == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }

        Ok(covariance / (portfolio_std * benchmark_std))
    }

    /// 计算R平方（决定系数）
    pub fn calculate_r_squared(&self) -> SigmaXResult<Decimal> {
        let correlation = self.calculate_correlation()?;
        Ok(correlation.powi(2))
    }

    /// 计算信息系数（IC）
    pub fn calculate_information_coefficient(&self) -> SigmaXResult<Decimal> {
        // 信息系数等于相关系数（在这个简化实现中）
        self.calculate_correlation()
    }

    /// 计算向上捕获率
    pub fn calculate_up_capture(&self) -> SigmaXResult<Decimal> {
        if self.benchmark_returns.is_empty() || self.returns_history.len() != self.benchmark_returns.len() {
            return Ok(Decimal::ZERO);
        }

        let up_periods: Vec<(Decimal, Decimal)> = self.returns_history
            .iter()
            .zip(self.benchmark_returns.iter())
            .filter(|(_, &b)| b > Decimal::ZERO)
            .map(|(&p, &b)| (p, b))
            .collect();

        if up_periods.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let portfolio_up_return: Decimal = up_periods.iter().map(|(p, _)| p).sum();
        let benchmark_up_return: Decimal = up_periods.iter().map(|(_, b)| b).sum();

        if benchmark_up_return == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }

        Ok(portfolio_up_return / benchmark_up_return * Decimal::from(100))
    }

    /// 计算向下捕获率
    pub fn calculate_down_capture(&self) -> SigmaXResult<Decimal> {
        if self.benchmark_returns.is_empty() || self.returns_history.len() != self.benchmark_returns.len() {
            return Ok(Decimal::ZERO);
        }

        let down_periods: Vec<(Decimal, Decimal)> = self.returns_history
            .iter()
            .zip(self.benchmark_returns.iter())
            .filter(|(_, &b)| b < Decimal::ZERO)
            .map(|(&p, &b)| (p, b))
            .collect();

        if down_periods.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let portfolio_down_return: Decimal = down_periods.iter().map(|(p, _)| p).sum();
        let benchmark_down_return: Decimal = down_periods.iter().map(|(_, b)| b).sum();

        if benchmark_down_return == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }

        Ok(portfolio_down_return / benchmark_down_return * Decimal::from(100))
    }
}
