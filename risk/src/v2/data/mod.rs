//! V2风控数据访问层
//!
//! 统一的数据访问接口，支持缓存和批量操作

mod cache;
mod repository;
mod optimized_repository;

pub use cache::{
    RiskCache, CacheManager, CacheConfig, CacheStats,
    RedisCacheManager, MemoryCacheManager
};
pub use repository::{
    RiskRepository, TradingLimits, RiskConfig,
    BatchQueryRequest, BatchQueryResult, QueryStats
};
pub use optimized_repository::{
    OptimizedRiskRepository, ConnectionPoolConfig
};

