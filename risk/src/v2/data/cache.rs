//! V2风控缓存模块
//!
//! 提供风控结果的缓存功能，支持Redis集成和统计监控

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use crate::v2::core::types::{RiskRequest, RiskResult};
use sigmax_core::SigmaXResult;
use async_trait::async_trait;
use redis::{Client, AsyncCommands, cmd};
use serde::{Serialize, Deserialize};

pub struct RiskCache {
    cache: Arc<Mutex<HashMap<String, RiskResult>>>,
}

impl RiskCache {
    pub fn new() -> Self {
        RiskCache {
            cache: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub fn get(&self, key: &str) -> Option<RiskResult> {
        let cache = self.cache.lock().unwrap();
        cache.get(key).cloned()
    }

    pub fn set(&self, key: String, value: RiskResult) {
        let mut cache = self.cache.lock().unwrap();
        cache.insert(key, value);
    }

    pub fn remove(&self, key: &str) {
        let mut cache = self.cache.lock().unwrap();
        cache.remove(key);
    }

    pub fn clear(&self) {
        let mut cache = self.cache.lock().unwrap();
        cache.clear();
    }

    pub fn stats(&self) -> HashMap<String, u64> {
        let cache = self.cache.lock().unwrap();
        let mut stats = HashMap::new();
        stats.insert("size".to_string(), cache.len() as u64);
        // 简化实现，暂时返回基本统计
        stats.insert("hits".to_string(), 0);
        stats.insert("misses".to_string(), 0);
        stats.insert("evictions".to_string(), 0);
        stats
    }
}

/// 缓存配置
#[derive(Debug, Clone)]
pub struct CacheConfig {
    pub max_size: usize,
    pub ttl: Duration,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_size: 1000,
            ttl: Duration::from_secs(300), // 5分钟
        }
    }
}

/// 缓存统计信息
#[derive(Debug, Default)]
pub struct CacheStats {
    pub size: u64,
    pub hits: u64,
    pub misses: u64,
    pub evictions: u64,
    pub hit_rate: f64,
}

impl CacheStats {
    pub fn new() -> Self {
        Self::default()
    }

    /// 计算命中率
    pub fn calculate_hit_rate(&mut self) {
        let total = self.hits + self.misses;
        self.hit_rate = if total > 0 {
            self.hits as f64 / total as f64
        } else {
            0.0
        };
    }
}

/// 缓存统计信息（内部使用）
#[derive(Debug, Default, Clone)]
struct CacheStatistics {
    hits: u64,
    misses: u64,
    evictions: u64,
    total_requests: u64,
}

impl CacheStatistics {
    fn hit_rate(&self) -> f64 {
        if self.total_requests == 0 {
            0.0
        } else {
            self.hits as f64 / self.total_requests as f64
        }
    }
}

/// 缓存管理器特征
#[async_trait]
pub trait CacheManager: Send + Sync {
    /// 获取缓存值
    async fn get(&self, key: &str) -> SigmaXResult<Option<RiskResult>>;

    /// 设置缓存值
    async fn set(&self, key: &str, value: &RiskResult, ttl: Option<Duration>) -> SigmaXResult<()>;

    /// 删除缓存值
    async fn delete(&self, key: &str) -> SigmaXResult<bool>;

    /// 清空缓存
    async fn clear(&self) -> SigmaXResult<()>;

    /// 获取缓存统计信息
    async fn stats(&self) -> SigmaXResult<CacheStats>;

    /// 检查缓存健康状态
    async fn health_check(&self) -> SigmaXResult<bool>;
}

/// Redis缓存管理器
pub struct RedisCacheManager {
    client: Arc<Client>,
    stats: Arc<Mutex<CacheStatistics>>,
    config: CacheConfig,
}

impl RedisCacheManager {
    /// 创建新的Redis缓存管理器
    pub async fn new(redis_url: &str, config: CacheConfig) -> SigmaXResult<Self> {
        let client = Client::open(redis_url)
            .map_err(|e| sigmax_core::SigmaXError::Config(format!("Redis连接失败: {}", e)))?;

        // 测试连接
        let mut conn = client.get_async_connection().await
            .map_err(|e| sigmax_core::SigmaXError::Config(format!("Redis连接测试失败: {}", e)))?;

        let _: String = cmd("PING").query_async(&mut conn).await
            .map_err(|e| sigmax_core::SigmaXError::Config(format!("Redis ping失败: {}", e)))?;

        tracing::info!("Redis缓存管理器初始化成功: {}", redis_url);

        Ok(Self {
            client: Arc::new(client),
            stats: Arc::new(Mutex::new(CacheStatistics::default())),
            config,
        })
    }

    /// 生成缓存键
    fn cache_key(&self, key: &str) -> String {
        format!("sigmax:risk:{}", key)
    }

    /// 更新统计信息
    fn update_stats<F>(&self, update_fn: F)
    where
        F: FnOnce(&mut CacheStatistics),
    {
        if let Ok(mut stats) = self.stats.lock() {
            update_fn(&mut stats);
        }
    }
}

#[async_trait]
impl CacheManager for RedisCacheManager {
    async fn get(&self, key: &str) -> SigmaXResult<Option<RiskResult>> {
        let cache_key = self.cache_key(key);

        match self.client.get_async_connection().await {
            Ok(mut conn) => {
                self.update_stats(|stats| stats.total_requests += 1);

                match conn.get::<_, Option<String>>(&cache_key).await {
                    Ok(Some(data)) => {
                        match serde_json::from_str::<RiskResult>(&data) {
                            Ok(result) => {
                                self.update_stats(|stats| stats.hits += 1);
                                Ok(Some(result))
                            }
                            Err(e) => {
                                tracing::warn!("缓存数据反序列化失败: {}", e);
                                self.update_stats(|stats| stats.misses += 1);
                                Ok(None)
                            }
                        }
                    }
                    Ok(None) => {
                        self.update_stats(|stats| stats.misses += 1);
                        Ok(None)
                    }
                    Err(e) => {
                        tracing::error!("Redis获取数据失败: {}", e);
                        self.update_stats(|stats| stats.misses += 1);
                        Err(sigmax_core::SigmaXError::Database(format!("Redis获取失败: {}", e)))
                    }
                }
            }
            Err(e) => {
                tracing::error!("Redis连接失败: {}", e);
                Err(sigmax_core::SigmaXError::Database(format!("Redis连接失败: {}", e)))
            }
        }
    }

    async fn set(&self, key: &str, value: &RiskResult, ttl: Option<Duration>) -> SigmaXResult<()> {
        let cache_key = self.cache_key(key);

        match self.client.get_async_connection().await {
            Ok(mut conn) => {
                let serialized = serde_json::to_string(value)
                    .map_err(|e| sigmax_core::SigmaXError::Serialization(e.to_string()))?;

                let ttl_seconds = ttl.unwrap_or(self.config.ttl).as_secs();

                match conn.set_ex::<_, _, ()>(&cache_key, &serialized, ttl_seconds).await {
                    Ok(_) => {
                        tracing::debug!("缓存设置成功: key={}, ttl={}s", key, ttl_seconds);
                        Ok(())
                    }
                    Err(e) => {
                        tracing::error!("Redis设置数据失败: {}", e);
                        Err(sigmax_core::SigmaXError::Database(format!("Redis设置失败: {}", e)))
                    }
                }
            }
            Err(e) => {
                tracing::error!("Redis连接失败: {}", e);
                Err(sigmax_core::SigmaXError::Database(format!("Redis连接失败: {}", e)))
            }
        }
    }

    async fn delete(&self, key: &str) -> SigmaXResult<bool> {
        let cache_key = self.cache_key(key);

        match self.client.get_async_connection().await {
            Ok(mut conn) => {
                match conn.del::<_, i32>(&cache_key).await {
                    Ok(deleted_count) => {
                        let deleted = deleted_count > 0;
                        if deleted {
                            tracing::debug!("缓存删除成功: key={}", key);
                        }
                        Ok(deleted)
                    }
                    Err(e) => {
                        tracing::error!("Redis删除数据失败: {}", e);
                        Err(sigmax_core::SigmaXError::Database(format!("Redis删除失败: {}", e)))
                    }
                }
            }
            Err(e) => {
                tracing::error!("Redis连接失败: {}", e);
                Err(sigmax_core::SigmaXError::Database(format!("Redis连接失败: {}", e)))
            }
        }
    }

    async fn clear(&self) -> SigmaXResult<()> {
        match self.client.get_async_connection().await {
            Ok(mut conn) => {
                let pattern = format!("sigmax:risk:*");
                // 使用简单的方式：先获取所有匹配的键，然后删除
                let keys: Vec<String> = conn.keys(&pattern).await
                    .map_err(|e| sigmax_core::SigmaXError::Database(format!("获取键列表失败: {}", e)))?;

                let deleted_count = if !keys.is_empty() {
                    conn.del::<_, i32>(&keys).await
                        .map_err(|e| sigmax_core::SigmaXError::Database(format!("批量删除失败: {}", e)))?
                } else {
                    0
                };

                match Ok::<i32, sigmax_core::SigmaXError>(deleted_count) {
                    Ok(deleted_count) => {
                        tracing::info!("缓存清空完成，删除了{}个键", deleted_count);
                        Ok(())
                    }
                    Err(e) => {
                        tracing::error!("Redis清空缓存失败: {}", e);
                        Err(sigmax_core::SigmaXError::Database(format!("Redis清空失败: {}", e)))
                    }
                }
            }
            Err(e) => {
                tracing::error!("Redis连接失败: {}", e);
                Err(sigmax_core::SigmaXError::Database(format!("Redis连接失败: {}", e)))
            }
        }
    }

    async fn stats(&self) -> SigmaXResult<CacheStats> {
        let internal_stats = {
            let stats = self.stats.lock()
                .map_err(|e| sigmax_core::SigmaXError::Internal(format!("统计信息锁定失败: {}", e)))?;
            stats.clone()
        };

        // 获取Redis中的键数量
        let size = match self.client.get_async_connection().await {
            Ok(mut conn) => {
                let pattern = format!("sigmax:risk:*");
                match conn.keys::<_, Vec<String>>(&pattern).await {
                    Ok(keys) => keys.len() as u64,
                    Err(_) => 0,
                }
            }
            Err(_) => 0,
        };

        let cache_stats = CacheStats {
            size,
            hits: internal_stats.hits,
            misses: internal_stats.misses,
            evictions: internal_stats.evictions,
            hit_rate: internal_stats.hit_rate(),
        };

        Ok(cache_stats)
    }

    async fn health_check(&self) -> SigmaXResult<bool> {
        match self.client.get_async_connection().await {
            Ok(mut conn) => {
                match cmd("PING").query_async::<_, String>(&mut conn).await {
                    Ok(_) => Ok(true),
                    Err(e) => {
                        tracing::warn!("Redis健康检查失败: {}", e);
                        Ok(false)
                    }
                }
            }
            Err(e) => {
                tracing::warn!("Redis连接失败: {}", e);
                Ok(false)
            }
        }
    }
}

/// 内存缓存管理器（用于测试和无Redis环境）
pub struct MemoryCacheManager {
    cache: Arc<Mutex<HashMap<String, (RiskResult, Instant)>>>,
    stats: Arc<Mutex<CacheStatistics>>,
    config: CacheConfig,
}

impl MemoryCacheManager {
    /// 创建新的内存缓存管理器
    pub fn new(config: CacheConfig) -> Self {
        Self {
            cache: Arc::new(Mutex::new(HashMap::new())),
            stats: Arc::new(Mutex::new(CacheStatistics::default())),
            config,
        }
    }

    /// 清理过期条目
    fn cleanup_expired(&self) {
        if let Ok(mut cache) = self.cache.lock() {
            let now = Instant::now();
            let expired_keys: Vec<String> = cache
                .iter()
                .filter(|(_, (_, created_at))| now.duration_since(*created_at) > self.config.ttl)
                .map(|(key, _)| key.clone())
                .collect();

            for key in expired_keys {
                cache.remove(&key);
                if let Ok(mut stats) = self.stats.lock() {
                    stats.evictions += 1;
                }
            }
        }
    }

    /// 更新统计信息
    fn update_stats<F>(&self, update_fn: F)
    where
        F: FnOnce(&mut CacheStatistics),
    {
        if let Ok(mut stats) = self.stats.lock() {
            update_fn(&mut stats);
        }
    }
}

#[async_trait]
impl CacheManager for MemoryCacheManager {
    async fn get(&self, key: &str) -> SigmaXResult<Option<RiskResult>> {
        self.cleanup_expired();
        self.update_stats(|stats| stats.total_requests += 1);

        if let Ok(cache) = self.cache.lock() {
            if let Some((result, created_at)) = cache.get(key) {
                let now = Instant::now();
                if now.duration_since(*created_at) <= self.config.ttl {
                    self.update_stats(|stats| stats.hits += 1);
                    return Ok(Some(result.clone()));
                }
            }
        }

        self.update_stats(|stats| stats.misses += 1);
        Ok(None)
    }

    async fn set(&self, key: &str, value: &RiskResult, _ttl: Option<Duration>) -> SigmaXResult<()> {
        if let Ok(mut cache) = self.cache.lock() {
            cache.insert(key.to_string(), (value.clone(), Instant::now()));

            // 检查缓存大小限制
            if cache.len() > self.config.max_size {
                // 简单的LRU策略：移除最旧的条目
                if let Some((oldest_key, _)) = cache
                    .iter()
                    .min_by_key(|(_, (_, created_at))| *created_at)
                    .map(|(k, v)| (k.clone(), v.clone()))
                {
                    cache.remove(&oldest_key);
                    self.update_stats(|stats| stats.evictions += 1);
                }
            }
        }
        Ok(())
    }

    async fn delete(&self, key: &str) -> SigmaXResult<bool> {
        if let Ok(mut cache) = self.cache.lock() {
            Ok(cache.remove(key).is_some())
        } else {
            Ok(false)
        }
    }

    async fn clear(&self) -> SigmaXResult<()> {
        if let Ok(mut cache) = self.cache.lock() {
            cache.clear();
        }
        Ok(())
    }

    async fn stats(&self) -> SigmaXResult<CacheStats> {
        self.cleanup_expired();

        let size = if let Ok(cache) = self.cache.lock() {
            cache.len() as u64
        } else {
            0
        };

        let internal_stats = if let Ok(stats) = self.stats.lock() {
            stats.clone()
        } else {
            CacheStatistics::default()
        };

        Ok(CacheStats {
            size,
            hits: internal_stats.hits,
            misses: internal_stats.misses,
            evictions: internal_stats.evictions,
            hit_rate: internal_stats.hit_rate(),
        })
    }

    async fn health_check(&self) -> SigmaXResult<bool> {
        // 内存缓存总是健康的
        Ok(true)
    }
}

