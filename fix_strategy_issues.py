#!/usr/bin/env python3
"""
修复策略问题的脚本
基于深入分析，修复AsymmetricVolatilityGridStrategy的关键问题
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:8080"
API_BASE = f"{BASE_URL}/api/v1"

def create_fixed_strategy_backtest():
    """创建修复后的策略回测"""
    print("🔧 创建修复后的策略回测...")
    
    # 1. 创建引擎
    engine_data = {
        "engine_type": "Backtest",
        "trading_pairs": [{"base": "BNB", "quote": "USDT"}],
        "initial_capital": "10000.00"
    }
    
    response = requests.post(f"{API_BASE}/engines", json=engine_data)
    if response.status_code != 200:
        print(f"❌ 创建引擎失败: {response.status_code} - {response.text}")
        return None
    
    engine_id = response.json()['data']['id']
    print(f"✅ 引擎创建成功: {engine_id}")
    
    # 2. 配置回测 - 修复关键问题
    config_data = {
        "data_file": "BNB_USDT_1d_60.json",
        "start_time": "2021-01-01T00:00:00Z",
        "end_time": "2021-01-15T23:59:59Z",  # 使用前15天数据
        "initial_capital": "10000.00",
        "trading_pairs": ["BNB_USDT"],
        "timeframe": "1d",
        "strategy_type": "asymmetric_volatility_grid",
        "strategy_config": {
            # 基础配置
            "base_price": 0.0,  # 使用第一个K线价格
            "order_amount": 200.0,
            "trading_pair": "BNB_USDT",
            "exchange_id": "binance",
            
            # 修复1: 确保网格范围计算正确
            # 下跌区间: 从-2%到-20%，范围大小=18%
            "down_range_start": -0.02,  # -2%
            "down_range_end": -0.20,    # -20%
            "down_grid_count": 3,       # 3个网格
            "down_base_quantity": 0.5,
            "down_grid_distribution": "Linear",
            "down_quantity_model": "Linear",
            
            # 上涨区间: 从+2%到+30%，范围大小=28%
            "up_range_start": 0.02,     # +2%
            "up_range_end": 0.30,       # +30%
            "up_grid_count": 3,         # 3个网格
            "up_base_quantity": 0.5,
            "up_grid_distribution": "Linear",
            "up_quantity_model": "Linear",
            
            # 修复2: 简化波动率计算
            "volatility_window_hours": 24,
            "volatility_multiplier": 1.0,
            "enable_dynamic_volatility": False,  # 禁用动态波动率
            
            # 修复3: 宽松的风险控制
            "max_position_amount": 8000.0,
            "max_daily_trades": 100,
            "stop_loss_config": "Disabled",
            
            # 修复4: 禁用所有可能阻止交易的功能
            "strategy_preset": "Balanced",  # 使用平衡模式
            "recenter_threshold_percent": None,  # 禁用网格重置
            "regime_filter": None  # 禁用市场状态过滤器
        }
    }
    
    print("📋 修复后的回测配置:")
    print(f"  数据文件: {config_data['data_file']}")
    print(f"  网格配置: 下跌 -2% 到 -20%，上涨 +2% 到 +30%")
    print(f"  网格数量: 各3个")
    print(f"  禁用功能: 动态波动率、网格重置、市场过滤器")
    
    response = requests.post(f"{API_BASE}/engines/{engine_id}/backtest/config", json=config_data)
    if response.status_code != 200:
        print(f"❌ 配置回测失败: {response.status_code} - {response.text}")
        return None
    
    print("✅ 回测配置成功")
    
    # 3. 启动引擎
    response = requests.post(f"{API_BASE}/engines/{engine_id}/start")
    if response.status_code != 200:
        print(f"❌ 启动引擎失败: {response.status_code} - {response.text}")
        return None
    
    print("✅ 引擎启动成功")
    return engine_id

def monitor_fixed_backtest(engine_id):
    """监控修复后的回测"""
    print(f"📊 监控修复后的回测进度...")
    
    start_time = time.time()
    timeout_seconds = 8 * 60  # 8分钟超时
    
    while time.time() - start_time < timeout_seconds:
        try:
            response = requests.get(f"{API_BASE}/engines/{engine_id}/backtest/progress")
            if response.status_code == 200:
                data = response.json()
                progress_data = data['data']
                
                progress = progress_data['progress']['percentage']
                status = progress_data['status']
                current_candle = progress_data['progress']['current_candle']
                total_candles = progress_data['progress']['total_candles']
                
                print(f"进度: {progress:.1f}% ({current_candle}/{total_candles}) - 状态: {status}")
                
                if status == 'completed':
                    print("✅ 修复后回测完成!")
                    return True
                elif status == 'failed':
                    print("❌ 修复后回测失败!")
                    return False
                    
            else:
                print(f"⚠️ 获取进度失败: {response.status_code}")
                
        except Exception as e:
            print(f"⚠️ 监控异常: {e}")
        
        time.sleep(3)
    
    print("⏰ 监控超时")
    return False

def analyze_fixed_result(engine_id):
    """分析修复后的结果"""
    print("📈 分析修复后的回测结果...")
    
    try:
        # 获取回测结果
        response = requests.get(f"{API_BASE}/engines/{engine_id}/backtest/result")
        if response.status_code != 200:
            print(f"❌ 获取结果失败: {response.status_code} - {response.text}")
            return False
        
        result_data = response.json()['data']
        
        print("=" * 70)
        print("🔧 修复后回测结果分析")
        print("=" * 70)
        
        print(f"引擎ID: {result_data['engine_id']}")
        print(f"状态: {result_data['status']}")
        
        # 性能指标
        if 'performance' in result_data:
            perf = result_data['performance']
            print("\n💰 性能指标:")
            
            initial_capital = float(perf.get('initial_capital', 0))
            final_capital = float(perf.get('final_capital', 0))
            
            print(f"初始资金: ${initial_capital:,.2f}")
            print(f"最终资金: ${final_capital:,.2f}")
            print(f"总收益: ${final_capital - initial_capital:,.2f}")
            
            if initial_capital > 0:
                total_return_pct = ((final_capital - initial_capital) / initial_capital) * 100
                print(f"总收益率: {total_return_pct:.2f}%")
        
        # 交易统计
        if 'trading_stats' in result_data:
            stats = result_data['trading_stats']
            print("\n📊 交易统计:")
            
            total_trades = stats.get('total_trades', 0)
            winning_trades = stats.get('winning_trades', 0)
            
            print(f"总交易数: {total_trades}")
            
            if total_trades > 0:
                print(f"盈利交易: {winning_trades}")
                print(f"亏损交易: {total_trades - winning_trades}")
                
                win_rate = (winning_trades / total_trades) * 100
                print(f"胜率: {win_rate:.1f}%")
                
                print("🎉 策略修复成功！成功生成交易!")
                print("✅ 问题已解决!")
            else:
                print("❌ 仍然没有交易")
                print("🔍 需要进一步深入修复...")
        
        # 获取详细交易记录
        response = requests.get(f"{API_BASE}/engines/{engine_id}/backtest/trades")
        if response.status_code == 200:
            trades_data = response.json()['data']
            trades = trades_data['trades']
            print(f"\n📋 交易记录: {len(trades)} 笔交易")
            
            if len(trades) > 0:
                print("前5笔交易:")
                for i, trade in enumerate(trades[:5]):
                    side = trade.get('side', 'N/A')
                    quantity = trade.get('quantity', 'N/A')
                    price = trade.get('price', 'N/A')
                    timestamp = trade.get('timestamp', 'N/A')
                    print(f"  {i+1}. {side} {quantity} @ ${price} ({timestamp})")
        
        print("=" * 70)
        return True
        
    except Exception as e:
        print(f"❌ 分析结果异常: {e}")
        return False

def create_simple_grid_strategy_test():
    """创建简单网格策略测试"""
    print("\n🧪 创建简单网格策略测试...")
    
    # 使用最简单的配置
    engine_data = {
        "engine_type": "Backtest",
        "trading_pairs": [{"base": "BNB", "quote": "USDT"}],
        "initial_capital": "10000.00"
    }
    
    response = requests.post(f"{API_BASE}/engines", json=engine_data)
    if response.status_code != 200:
        print(f"❌ 创建引擎失败: {response.status_code} - {response.text}")
        return None
    
    engine_id = response.json()['data']['id']
    print(f"✅ 简单测试引擎创建成功: {engine_id}")
    
    # 极简配置
    config_data = {
        "data_file": "BNB_USDT_1d_60.json",
        "start_time": "2021-01-01T00:00:00Z",
        "end_time": "2021-01-05T23:59:59Z",  # 只用5天数据
        "initial_capital": "10000.00",
        "trading_pairs": ["BNB_USDT"],
        "timeframe": "1d",
        "strategy_type": "asymmetric_volatility_grid",
        "strategy_config": {
            "base_price": 38.0,  # 固定基准价格
            "order_amount": 100.0,
            "trading_pair": "BNB_USDT",
            "exchange_id": "binance",
            
            # 极简网格配置
            "down_range_start": -0.05,  # -5%
            "down_range_end": -0.15,    # -15%
            "down_grid_count": 1,       # 只有1个网格
            "down_base_quantity": 1.0,
            "down_grid_distribution": "Linear",
            "down_quantity_model": "Constant",
            
            "up_range_start": 0.05,     # +5%
            "up_range_end": 0.15,       # +15%
            "up_grid_count": 1,         # 只有1个网格
            "up_base_quantity": 1.0,
            "up_grid_distribution": "Linear",
            "up_quantity_model": "Constant",
            
            # 禁用所有复杂功能
            "volatility_window_hours": 24,
            "volatility_multiplier": 1.0,
            "enable_dynamic_volatility": False,
            "max_position_amount": 9000.0,
            "max_daily_trades": 1000,
            "stop_loss_config": "Disabled",
            "strategy_preset": "Conservative",
            "recenter_threshold_percent": None,
            "regime_filter": None
        }
    }
    
    print("📋 极简测试配置:")
    print(f"  基准价格: $38.0 (固定)")
    print(f"  网格配置: 下跌1个(-5%到-15%)，上涨1个(+5%到+15%)")
    print(f"  数据范围: 前5天")
    
    response = requests.post(f"{API_BASE}/engines/{engine_id}/backtest/config", json=config_data)
    if response.status_code != 200:
        print(f"❌ 配置测试失败: {response.status_code} - {response.text}")
        return None
    
    print("✅ 极简测试配置成功")
    
    # 启动引擎
    response = requests.post(f"{API_BASE}/engines/{engine_id}/start")
    if response.status_code != 200:
        print(f"❌ 启动测试失败: {response.status_code} - {response.text}")
        return None
    
    print("✅ 极简测试启动成功")
    return engine_id

def main():
    """主函数"""
    print("🔧 开始策略问题修复")
    print("🎯 目标：修复AsymmetricVolatilityGridStrategy的关键问题")
    print("=" * 70)
    
    # 方案1: 修复后的策略测试
    print("📋 方案1: 修复关键问题后测试")
    engine_id1 = create_fixed_strategy_backtest()
    if engine_id1:
        success1 = monitor_fixed_backtest(engine_id1)
        if success1:
            analyze_fixed_result(engine_id1)
    
    print("\n" + "=" * 70)
    
    # 方案2: 极简策略测试
    print("📋 方案2: 极简配置测试")
    engine_id2 = create_simple_grid_strategy_test()
    if engine_id2:
        success2 = monitor_fixed_backtest(engine_id2)
        if success2:
            analyze_fixed_result(engine_id2)
    
    print("\n🎉 策略修复测试完成!")
    print("💡 如果仍然没有交易，问题可能在Rust代码的深层逻辑中")

if __name__ == "__main__":
    main()
