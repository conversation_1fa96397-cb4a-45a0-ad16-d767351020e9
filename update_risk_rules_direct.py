#!/usr/bin/env python3
"""
直接通过数据库更新风控规则配置
"""

import psycopg2
import json
from datetime import datetime

# 数据库连接配置
DATABASE_URL = "****************************************************/mx"

def connect_db():
    """连接数据库"""
    try:
        conn = psycopg2.connect(DATABASE_URL)
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def update_position_limit_rule(conn):
    """更新单个持仓大小限制规则"""
    print("🔧 更新单个持仓大小限制规则...")
    
    new_parameters = {
        "max_position_percent": 95.0,
        "base_amount": "total_capital",
        "include_unrealized_pnl": True,
        "backtest_mode": True
    }
    
    try:
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE unified_risk_rules 
            SET 
                parameters = %s,
                description = %s,
                updated_at = NOW()
            WHERE 
                id = '40000000-0000-0000-0000-000000000001'
                AND name = '单个持仓大小限制'
        """, (json.dumps(new_parameters), '限制单个交易对的最大持仓（回测优化版本）'))
        
        rows_affected = cursor.rowcount
        conn.commit()
        cursor.close()
        
        if rows_affected > 0:
            print(f"✅ 成功更新持仓限制规则，影响 {rows_affected} 行")
        else:
            print("⚠️ 没有找到要更新的持仓限制规则")
        
        return True
    except Exception as e:
        print(f"❌ 更新持仓限制规则失败: {e}")
        conn.rollback()
        return False

def disable_strict_rules(conn):
    """禁用过于严格的规则"""
    print("🔇 禁用过于严格的规则...")
    
    strict_rules = [
        '最大回撤保护',
        '日交易频率限制',
        '小时交易频率限制'
    ]
    
    try:
        cursor = conn.cursor()
        for rule_name in strict_rules:
            cursor.execute("""
                UPDATE unified_risk_rules 
                SET 
                    enabled = false,
                    description = description || ' (临时禁用用于回测)',
                    updated_at = NOW()
                WHERE 
                    name = %s
            """, (rule_name,))
            
            if cursor.rowcount > 0:
                print(f"✅ 禁用规则: {rule_name}")
        
        conn.commit()
        cursor.close()
        return True
    except Exception as e:
        print(f"❌ 禁用严格规则失败: {e}")
        conn.rollback()
        return False

def create_backtest_rule(conn):
    """创建回测专用规则"""
    print("🆕 创建回测专用规则...")
    
    backtest_parameters = {
        "max_position_percent": 98.0,
        "base_amount": "total_capital",
        "include_unrealized_pnl": True,
        "backtest_mode": True,
        "allow_initial_cash_position": True
    }
    
    try:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO unified_risk_rules (
                id, name, description, category, rule_type, 
                parameters, enabled, priority, strategy_type, created_by
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            ) ON CONFLICT (id) DO UPDATE SET
                parameters = EXCLUDED.parameters,
                enabled = EXCLUDED.enabled,
                updated_at = NOW()
        """, (
            '99000000-0000-0000-0000-000000000001',
            '回测持仓大小限制',
            '专门为回测环境设计的持仓限制规则',
            'position',
            'single_position_limit',
            json.dumps(backtest_parameters),
            True,
            95,
            None,
            'system'
        ))
        
        conn.commit()
        cursor.close()
        print("✅ 成功创建回测专用规则")
        return True
    except Exception as e:
        print(f"❌ 创建回测规则失败: {e}")
        conn.rollback()
        return False

def update_order_amount_limit(conn):
    """更新订单金额限制"""
    print("💰 更新订单金额限制...")
    
    new_parameters = {
        "max_amount": 50000.0,
        "include_fees": True,
        "currency": "USDT"
    }
    
    try:
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE unified_risk_rules 
            SET 
                parameters = %s,
                updated_at = NOW()
            WHERE 
                rule_type = 'order_amount_limit'
        """, (json.dumps(new_parameters),))
        
        rows_affected = cursor.rowcount
        conn.commit()
        cursor.close()
        
        if rows_affected > 0:
            print(f"✅ 成功更新订单金额限制，影响 {rows_affected} 行")
        else:
            print("⚠️ 没有找到订单金额限制规则")
        
        return True
    except Exception as e:
        print(f"❌ 更新订单金额限制失败: {e}")
        conn.rollback()
        return False

def show_updated_rules(conn):
    """显示更新后的规则"""
    print("\n📋 更新后的风控规则:")
    
    try:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT 
                id,
                name,
                enabled,
                priority,
                parameters,
                updated_at
            FROM unified_risk_rules 
            WHERE 
                rule_type IN ('single_position_limit', 'order_amount_limit', 'max_leverage_limit')
                OR name LIKE '%回测%'
            ORDER BY priority DESC, name
        """)
        
        rules = cursor.fetchall()
        cursor.close()
        
        for rule in rules:
            rule_id, name, enabled, priority, parameters, updated_at = rule
            print(f"  - {name}")
            print(f"    ID: {rule_id}")
            print(f"    启用: {enabled}")
            print(f"    优先级: {priority}")
            print(f"    参数: {parameters}")
            print(f"    更新时间: {updated_at}")
            print()
        
        return True
    except Exception as e:
        print(f"❌ 查询规则失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始直接更新风控规则配置")
    print("=" * 60)
    
    # 连接数据库
    conn = connect_db()
    if not conn:
        return
    
    try:
        # 1. 更新持仓限制规则
        update_position_limit_rule(conn)
        
        # 2. 禁用严格规则
        disable_strict_rules(conn)
        
        # 3. 创建回测专用规则
        create_backtest_rule(conn)
        
        # 4. 更新订单金额限制
        update_order_amount_limit(conn)
        
        # 5. 显示更新结果
        show_updated_rules(conn)
        
        print("\n🎉 风控规则配置更新完成!")
        print("💡 提示: 重启回测以应用新规则")
        
    finally:
        conn.close()

if __name__ == "__main__":
    main()
