//! 配置服务实现

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, SigmaXError};
use serde_json::Value;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, mpsc};
use tracing::{debug, info, warn};

/// 配置变更事件
#[derive(Debug, Clone)]
pub struct ConfigChangeEvent {
    pub key: String,
    pub old_value: Option<Value>,
    pub new_value: Value,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// 配置服务trait
#[async_trait]
pub trait ConfigService: Send + Sync {
    /// 获取字符串配置
    async fn get_string_config(&self, key: &str) -> SigmaXResult<Option<String>>;

    /// 获取JSON配置
    async fn get_json_config(&self, key: &str) -> SigmaXResult<Option<Value>>;
    
    /// 重新加载配置
    async fn reload_config(&self) -> SigmaXResult<()>;
    
    /// 验证配置
    async fn validate_config(&self, config: &Value) -> SigmaXResult<()>;
    
    /// 监听配置变更
    async fn watch_changes(&self) -> SigmaXResult<mpsc::Receiver<ConfigChangeEvent>>;
}

/// 内存配置服务
pub struct MemoryConfigService {
    /// 配置存储
    configs: Arc<RwLock<HashMap<String, Value>>>,
    /// 变更通知发送器
    change_senders: Arc<RwLock<Vec<mpsc::Sender<ConfigChangeEvent>>>>,
    /// 配置文件路径
    config_file_path: Option<String>,
}

impl MemoryConfigService {
    /// 创建新的内存配置服务
    pub fn new() -> Self {
        Self {
            configs: Arc::new(RwLock::new(HashMap::new())),
            change_senders: Arc::new(RwLock::new(Vec::new())),
            config_file_path: None,
        }
    }
    
    /// 从文件创建配置服务
    pub async fn from_file(file_path: &str) -> SigmaXResult<Self> {
        let mut service = Self::new();
        service.config_file_path = Some(file_path.to_string());
        service.load_from_file().await?;
        Ok(service)
    }
    
    /// 设置配置值
    pub async fn set_config<T>(&self, key: &str, value: T) -> SigmaXResult<()>
    where
        T: serde::Serialize,
    {
        let new_value = serde_json::to_value(value)
            .map_err(|e| SigmaXError::Configuration(format!("Serialization failed: {}", e)))?;
        
        let old_value = {
            let mut configs = self.configs.write().await;
            configs.insert(key.to_string(), new_value.clone())
        };
        
        // 发送变更事件
        let event = ConfigChangeEvent {
            key: key.to_string(),
            old_value,
            new_value,
            timestamp: chrono::Utc::now(),
        };
        
        self.notify_change(event).await;
        
        debug!("Configuration set: {}", key);
        Ok(())
    }
    
    /// 删除配置
    pub async fn remove_config(&self, key: &str) -> SigmaXResult<Option<Value>> {
        let old_value = {
            let mut configs = self.configs.write().await;
            configs.remove(key)
        };
        
        if let Some(old_val) = old_value.clone() {
            let event = ConfigChangeEvent {
                key: key.to_string(),
                old_value: Some(old_val),
                new_value: Value::Null,
                timestamp: chrono::Utc::now(),
            };
            
            self.notify_change(event).await;
        }
        
        debug!("Configuration removed: {}", key);
        Ok(old_value)
    }
    
    /// 获取所有配置键
    pub async fn get_all_keys(&self) -> Vec<String> {
        let configs = self.configs.read().await;
        configs.keys().cloned().collect()
    }
    
    /// 从文件加载配置
    async fn load_from_file(&self) -> SigmaXResult<()> {
        if let Some(file_path) = &self.config_file_path {
            match tokio::fs::read_to_string(file_path).await {
                Ok(content) => {
                    let config_map: HashMap<String, Value> = serde_json::from_str(&content)
                        .map_err(|e| SigmaXError::Configuration(format!("Failed to parse config file: {}", e)))?;
                    
                    let mut configs = self.configs.write().await;
                    *configs = config_map;
                    
                    info!("Loaded configuration from file: {}", file_path);
                }
                Err(e) => {
                    warn!("Failed to read config file {}: {}", file_path, e);
                }
            }
        }
        Ok(())
    }
    
    /// 保存配置到文件
    pub async fn save_to_file(&self) -> SigmaXResult<()> {
        if let Some(file_path) = &self.config_file_path {
            let configs = self.configs.read().await;
            let content = serde_json::to_string_pretty(&*configs)
                .map_err(|e| SigmaXError::Configuration(format!("Failed to serialize config: {}", e)))?;

            tokio::fs::write(file_path, content).await
                .map_err(|e| SigmaXError::Configuration(format!("Failed to write config file: {}", e)))?;
            
            info!("Saved configuration to file: {}", file_path);
        }
        Ok(())
    }
    
    /// 通知配置变更
    async fn notify_change(&self, event: ConfigChangeEvent) {
        let senders = self.change_senders.read().await;
        let mut failed_senders = Vec::new();
        
        for (index, sender) in senders.iter().enumerate() {
            if sender.send(event.clone()).await.is_err() {
                failed_senders.push(index);
            }
        }
        
        // 清理失败的发送器
        if !failed_senders.is_empty() {
            drop(senders);
            let mut senders = self.change_senders.write().await;
            for &index in failed_senders.iter().rev() {
                senders.remove(index);
            }
        }
    }
}

#[async_trait]
impl ConfigService for MemoryConfigService {
    async fn get_string_config(&self, key: &str) -> SigmaXResult<Option<String>> {
        let configs = self.configs.read().await;
        if let Some(value) = configs.get(key) {
            if let Some(s) = value.as_str() {
                Ok(Some(s.to_string()))
            } else {
                // 尝试将其他类型转换为字符串
                Ok(Some(value.to_string()))
            }
        } else {
            Ok(None)
        }
    }

    async fn get_json_config(&self, key: &str) -> SigmaXResult<Option<Value>> {
        let configs = self.configs.read().await;
        Ok(configs.get(key).cloned())
    }
    
    async fn reload_config(&self) -> SigmaXResult<()> {
        info!("Reloading configuration");
        self.load_from_file().await
    }
    
    async fn validate_config(&self, config: &Value) -> SigmaXResult<()> {
        // 基本验证：检查是否为有效的JSON对象
        if !config.is_object() {
            return Err(SigmaXError::Config("Configuration must be a JSON object".to_string()));
        }
        
        // 可以添加更多特定的验证逻辑
        debug!("Configuration validation passed");
        Ok(())
    }
    
    async fn watch_changes(&self) -> SigmaXResult<mpsc::Receiver<ConfigChangeEvent>> {
        let (sender, receiver) = mpsc::channel(100);
        
        let mut senders = self.change_senders.write().await;
        senders.push(sender);
        
        debug!("Added configuration change watcher");
        Ok(receiver)
    }
}

/// 分层配置服务
pub struct TieredConfigService {
    /// 主配置服务
    primary: Arc<dyn ConfigService>,
    /// 备用配置服务
    fallback: Arc<dyn ConfigService>,
}

impl TieredConfigService {
    pub fn new(
        primary: Arc<dyn ConfigService>,
        fallback: Arc<dyn ConfigService>,
    ) -> Self {
        Self {
            primary,
            fallback,
        }
    }
}

#[async_trait]
impl ConfigService for TieredConfigService {
    async fn get_string_config(&self, key: &str) -> SigmaXResult<Option<String>> {
        // 先尝试主配置服务
        match self.primary.get_string_config(key).await {
            Ok(Some(value)) => Ok(Some(value)),
            Ok(None) => {
                // 主配置服务没有找到，尝试备用配置服务
                self.fallback.get_string_config(key).await
            }
            Err(_) => {
                // 主配置服务出错，尝试备用配置服务
                warn!("Primary config service failed, falling back to secondary");
                self.fallback.get_string_config(key).await
            }
        }
    }

    async fn get_json_config(&self, key: &str) -> SigmaXResult<Option<Value>> {
        // 先尝试主配置服务
        match self.primary.get_json_config(key).await {
            Ok(Some(value)) => Ok(Some(value)),
            Ok(None) => {
                // 主配置服务没有找到，尝试备用配置服务
                self.fallback.get_json_config(key).await
            }
            Err(_) => {
                // 主配置服务出错，尝试备用配置服务
                warn!("Primary config service failed, falling back to secondary");
                self.fallback.get_json_config(key).await
            }
        }
    }
    
    async fn reload_config(&self) -> SigmaXResult<()> {
        // 重新加载两个配置服务
        let primary_result = self.primary.reload_config().await;
        let fallback_result = self.fallback.reload_config().await;
        
        // 如果主配置服务成功，返回成功
        if primary_result.is_ok() {
            primary_result
        } else {
            fallback_result
        }
    }
    
    async fn validate_config(&self, config: &Value) -> SigmaXResult<()> {
        // 使用主配置服务进行验证
        self.primary.validate_config(config).await
    }
    
    async fn watch_changes(&self) -> SigmaXResult<mpsc::Receiver<ConfigChangeEvent>> {
        // 监听主配置服务的变更
        self.primary.watch_changes().await
    }
}

/// 环境变量配置服务
pub struct EnvConfigService {
    prefix: String,
}

impl EnvConfigService {
    pub fn new(prefix: &str) -> Self {
        Self {
            prefix: prefix.to_string(),
        }
    }
    
    fn env_key(&self, key: &str) -> String {
        format!("{}_{}", self.prefix, key.to_uppercase())
    }
}

#[async_trait]
impl ConfigService for EnvConfigService {
    async fn get_string_config(&self, key: &str) -> SigmaXResult<Option<String>> {
        let env_key = self.env_key(key);
        match std::env::var(&env_key) {
            Ok(value) => Ok(Some(value)),
            Err(_) => Ok(None),
        }
    }

    async fn get_json_config(&self, key: &str) -> SigmaXResult<Option<Value>> {
        let env_key = self.env_key(key);

        if let Ok(value_str) = std::env::var(&env_key) {
            // 尝试解析为JSON
            match serde_json::from_str(&value_str) {
                Ok(value) => Ok(Some(value)),
                Err(_) => {
                    // 如果不是JSON，作为字符串处理
                    Ok(Some(Value::String(value_str)))
                }
            }
        } else {
            Ok(None)
        }
    }
    
    async fn reload_config(&self) -> SigmaXResult<()> {
        // 环境变量不需要重新加载
        Ok(())
    }
    
    async fn validate_config(&self, _config: &Value) -> SigmaXResult<()> {
        // 环境变量配置总是有效的
        Ok(())
    }
    
    async fn watch_changes(&self) -> SigmaXResult<mpsc::Receiver<ConfigChangeEvent>> {
        // 环境变量不支持变更监听
        let (_sender, receiver) = mpsc::channel(1);
        Ok(receiver)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_memory_config_service() {
        let service = MemoryConfigService::new();
        
        // 设置配置
        service.set_config("test_key", "test_value").await.unwrap();
        
        // 获取配置
        let result: Option<String> = service.get_config("test_key").await.unwrap();
        assert_eq!(result, Some("test_value".to_string()));
        
        // 获取不存在的配置
        let result: Option<String> = service.get_config("non_existent").await.unwrap();
        assert_eq!(result, None);
    }
    
    #[tokio::test]
    async fn test_config_change_notification() {
        let service = MemoryConfigService::new();
        let mut receiver = service.watch_changes().await.unwrap();
        
        // 设置配置
        service.set_config("test_key", "test_value").await.unwrap();
        
        // 接收变更通知
        let event = receiver.recv().await.unwrap();
        assert_eq!(event.key, "test_key");
        assert_eq!(event.new_value, Value::String("test_value".to_string()));
    }
    
    #[tokio::test]
    async fn test_tiered_config_service() {
        let primary = Arc::new(MemoryConfigService::new());
        let fallback = Arc::new(MemoryConfigService::new());
        
        // 在备用服务中设置配置
        fallback.set_config("fallback_key", "fallback_value").await.unwrap();
        
        let tiered = TieredConfigService::new(primary.clone(), fallback.clone());
        
        // 应该从备用服务获取配置
        let result: Option<String> = tiered.get_config("fallback_key").await.unwrap();
        assert_eq!(result, Some("fallback_value".to_string()));
        
        // 在主服务中设置相同的键
        primary.set_config("fallback_key", "primary_value").await.unwrap();
        
        // 应该从主服务获取配置
        let result: Option<String> = tiered.get_config("fallback_key").await.unwrap();
        assert_eq!(result, Some("primary_value".to_string()));
    }
}
