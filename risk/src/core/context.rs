//! 风险检查上下文
//!
//! 提供风险检查所需的上下文信息

use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// 风险检查上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskContext {
    /// 策略类型
    pub strategy_type: Option<String>,
    /// 交易所ID
    pub exchange_id: Option<String>,
    /// 用户ID
    pub user_id: Option<String>,
    /// 额外参数
    pub parameters: HashMap<String, serde_json::Value>,
}

impl Default for RiskContext {
    fn default() -> Self {
        Self {
            strategy_type: None,
            exchange_id: None,
            user_id: None,
            parameters: HashMap::new(),
        }
    }
}

impl RiskContext {
    /// 创建新的风险上下文
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置策略类型
    pub fn with_strategy_type(mut self, strategy_type: String) -> Self {
        self.strategy_type = Some(strategy_type);
        self
    }

    /// 设置交易所ID
    pub fn with_exchange_id(mut self, exchange_id: String) -> Self {
        self.exchange_id = Some(exchange_id);
        self
    }

    /// 设置用户ID
    pub fn with_user_id(mut self, user_id: String) -> Self {
        self.user_id = Some(user_id);
        self
    }

    /// 添加参数
    pub fn with_parameter(mut self, key: String, value: serde_json::Value) -> Self {
        self.parameters.insert(key, value);
        self
    }
}
