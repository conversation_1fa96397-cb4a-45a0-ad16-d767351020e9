//! 规则管理服务
//!
//! 提供风险规则的管理功能

use async_trait::async_trait;
use sigmax_core::SigmaXResult;
use std::sync::Arc;

use crate::core::{RiskRule, RuleExecutor};

/// 规则管理器特征
#[async_trait]
pub trait RuleManager: Send + Sync {
    /// 获取所有规则
    async fn get_all_rules(&self) -> SigmaXResult<Vec<RiskRule>>;
    
    /// 根据类型获取规则
    async fn get_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Vec<RiskRule>>;
    
    /// 添加规则
    async fn add_rule(&self, rule: RiskRule) -> SigmaXResult<()>;
    
    /// 更新规则
    async fn update_rule(&self, rule: RiskRule) -> SigmaXResult<()>;
    
    /// 删除规则
    async fn delete_rule(&self, rule_id: &str) -> SigmaXResult<()>;
}

/// 默认规则管理器
pub struct DefaultRuleManager {
    executor: Arc<dyn RuleExecutor>,
}

impl DefaultRuleManager {
    pub fn new(executor: Arc<dyn RuleExecutor>) -> Self {
        Self { executor }
    }
}

#[async_trait]
impl RuleManager for DefaultRuleManager {
    async fn get_all_rules(&self) -> SigmaXResult<Vec<RiskRule>> {
        // 默认实现：返回空列表
        Ok(vec![])
    }
    
    async fn get_rules_by_type(&self, _rule_type: &str) -> SigmaXResult<Vec<RiskRule>> {
        // 默认实现：返回空列表
        Ok(vec![])
    }
    
    async fn add_rule(&self, _rule: RiskRule) -> SigmaXResult<()> {
        // 默认实现：什么都不做
        Ok(())
    }
    
    async fn update_rule(&self, _rule: RiskRule) -> SigmaXResult<()> {
        // 默认实现：什么都不做
        Ok(())
    }
    
    async fn delete_rule(&self, _rule_id: &str) -> SigmaXResult<()> {
        // 默认实现：什么都不做
        Ok(())
    }
}
