//! 实盘风控适配器 - 低延迟优化
//! 
//! 设计原则：
//! - 低延迟：毫秒级响应，针对实盘交易优化
//! - 高可靠性：熔断器保护，故障快速恢复
//! - 热数据缓存：常用数据预加载和热缓存
//! - 实时监控：详细的性能指标和告警

use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use sigmax_core::{EngineType, Order, Balance, SigmaXResult, RiskManager, TradingPair, Quantity};
use super::circuit_breaker::CircuitBreakerStats;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};

use crate::risk::{
    EngineRiskAdapter, AdapterBase, AdapterConfig,
    RiskEngine, CacheService, MetricsCollector
};
use sigmax_core::AdapterMetrics;
use super::{LiveTradingStats, LiveTradingMonitor, CircuitBreaker};

/// 实盘风控适配器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiveAdapterConfig {
    /// 低延迟模式
    pub low_latency_mode: bool,
    /// 热缓存大小
    pub hot_cache_size: usize,
    /// 缓存TTL (秒)
    pub cache_ttl_secs: u64,
    /// 熔断器阈值
    pub circuit_breaker_threshold: f64,
    /// 超时时间 (毫秒)
    pub timeout_ms: u64,
    /// 最大重试次数
    pub max_retries: u32,
    /// 预热缓存
    pub preload_hot_data: bool,
    /// 启用调试日志
    pub enable_debug_logging: bool,
    /// 连接池大小
    pub connection_pool_size: usize,
    /// 健康检查间隔 (秒)
    pub health_check_interval_secs: u64,
}

impl Default for LiveAdapterConfig {
    fn default() -> Self {
        Self {
            low_latency_mode: true,
            hot_cache_size: 1000,
            cache_ttl_secs: 60, // 1分钟
            circuit_breaker_threshold: 0.1, // 10%错误率
            timeout_ms: 100, // 100ms
            max_retries: 2,
            preload_hot_data: true,
            enable_debug_logging: false,
            connection_pool_size: 10,
            health_check_interval_secs: 30,
        }
    }
}

/// 热缓存条目
#[derive(Debug, Clone)]
struct HotCacheEntry {
    result: bool,
    created_at: Instant,
    access_count: u64,
    last_access: Instant,
}

impl HotCacheEntry {
    fn new(result: bool) -> Self {
        let now = Instant::now();
        Self {
            result,
            created_at: now,
            access_count: 1,
            last_access: now,
        }
    }
    
    fn access(&mut self) -> bool {
        self.access_count += 1;
        self.last_access = Instant::now();
        self.result
    }
    
    fn is_hot(&self) -> bool {
        self.access_count > 5 && self.last_access.elapsed() < Duration::from_secs(60)
    }
}

/// 实盘风控适配器 - 低延迟优化实现
pub struct LiveTradingRiskAdapter {
    /// 适配器基础功能
    base: AdapterBase,
    /// 实盘特定配置
    config: LiveAdapterConfig,
    /// 热数据缓存
    hot_cache: Arc<RwLock<HashMap<String, HotCacheEntry>>>,
    /// 性能监控器
    monitor: Arc<LiveTradingMonitor>,
    /// 熔断器
    circuit_breaker: Arc<CircuitBreaker>,
    /// 预计算结果缓存
    precompute_cache: Arc<RwLock<HashMap<String, bool>>>,
}

impl LiveTradingRiskAdapter {
    /// 创建新的实盘风控适配器
    pub async fn new(
        risk_engine: Arc<dyn RiskEngine>,
        cache_service: Arc<dyn CacheService>,
        metrics_collector: Arc<dyn MetricsCollector>,
        config: LiveAdapterConfig,
    ) -> SigmaXResult<Self> {
        info!("Creating LiveTradingRiskAdapter with low-latency optimization");
        
        let base_config = AdapterConfig {
            enable_cache: true,
            cache_ttl: Duration::from_secs(config.cache_ttl_secs),
            enable_metrics: true,
            timeout: Duration::from_millis(config.timeout_ms),
            retry_attempts: config.max_retries,
        };
        
        let base = AdapterBase::new(
            risk_engine,
            cache_service,
            metrics_collector,
            EngineType::Live,
            base_config,
        );
        
        let circuit_breaker = Arc::new(CircuitBreaker::new(
            config.circuit_breaker_threshold,
            Duration::from_secs(30), // 30秒恢复时间
            5, // 最少5次请求后开始统计
        ));
        
        let adapter = Self {
            base,
            config: config.clone(),
            hot_cache: Arc::new(RwLock::new(HashMap::with_capacity(config.hot_cache_size))),
            monitor: Arc::new(LiveTradingMonitor::new()),
            circuit_breaker,
            precompute_cache: Arc::new(RwLock::new(HashMap::new())),
        };
        
        // 启动后台任务
        adapter.start_background_tasks().await;
        
        // 预热缓存
        if config.preload_hot_data {
            adapter.preload_hot_cache().await?;
        }
        
        info!("LiveTradingRiskAdapter created successfully");
        Ok(adapter)
    }
    
    /// 启动后台任务
    async fn start_background_tasks(&self) {
        let hot_cache = self.hot_cache.clone();
        let monitor = self.monitor.clone();
        let circuit_breaker = self.circuit_breaker.clone();
        let health_check_interval = self.config.health_check_interval_secs;
        
        // 热缓存清理任务
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60)); // 每分钟清理一次
            loop {
                interval.tick().await;
                
                let mut cache = hot_cache.write().await;
                let initial_size = cache.len();
                
                // 移除不再热门的条目
                cache.retain(|_, entry| entry.is_hot());
                
                let removed = initial_size - cache.len();
                if removed > 0 {
                    debug!("Cleaned {} cold entries from hot cache, remaining: {}", removed, cache.len());
                }
            }
        });
        
        // 健康检查任务
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(health_check_interval));
            loop {
                interval.tick().await;
                
                let health_status = monitor.health_check().await;
                let alerts = monitor.check_alerts().await;
                
                if !alerts.is_empty() {
                    warn!("Health check alerts detected:");
                    for alert in alerts {
                        alert.print();
                    }
                }
                
                // 根据健康状态调整熔断器
                match health_status {
                    super::HealthStatus::Critical => {
                        circuit_breaker.force_open().await;
                        error!("System health critical, circuit breaker forced open");
                    }
                    super::HealthStatus::Healthy => {
                        circuit_breaker.reset().await;
                    }
                    _ => {} // Warning状态不做特殊处理
                }
            }
        });
    }
    
    /// 预热热缓存
    async fn preload_hot_cache(&self) -> SigmaXResult<()> {
        info!("Preloading hot cache for common trading pairs");
        
        // 这里可以预加载常用交易对的风控结果
        // 例如：BTC/USDT, ETH/USDT等主流交易对的常见订单类型
        
        let common_pairs = vec![
            ("BTC", "USDT"),
            ("ETH", "USDT"),
            ("BNB", "USDT"),
            ("ADA", "USDT"),
            ("SOL", "USDT"),
        ];
        
        let mut preload_count = 0;
        for (base, quote) in common_pairs {
            // 为每个交易对预加载常见的风控检查结果
            let cache_key = format!("hot:{}:{}:market:buy", base, quote);
            
            // 这里应该调用实际的风控检查，但为了演示，我们使用模拟结果
            let mut hot_cache = self.hot_cache.write().await;
            hot_cache.insert(cache_key, HotCacheEntry::new(true));
            preload_count += 1;
        }
        
        info!("Preloaded {} entries into hot cache", preload_count);
        Ok(())
    }
    
    /// 低延迟风控检查
    pub async fn low_latency_check(&self, order: &Order) -> SigmaXResult<bool> {
        let start_time = Instant::now();
        
        // 检查熔断器状态
        if !self.circuit_breaker.can_execute().await {
            warn!("Circuit breaker is open, rejecting order");
            self.monitor.record_error().await;
            return Ok(false); // 熔断器打开时拒绝所有请求
        }
        
        // 生成热缓存键
        let hot_key = self.generate_hot_cache_key(order);
        
        // 检查热缓存
        if let Some(result) = self.check_hot_cache(&hot_key).await {
            let latency = start_time.elapsed().as_millis() as f64;
            self.monitor.record_order_processing(latency).await;
            self.monitor.record_risk_check(result, true).await;
            
            if self.config.enable_debug_logging {
                debug!("Hot cache hit for order: {} -> {}, latency: {:.1}ms", 
                       hot_key, result, latency);
            }
            
            self.circuit_breaker.record_success().await;
            return Ok(result);
        }
        
        // 执行实际风控检查
        let result = self.execute_risk_check_with_circuit_breaker(order).await;
        
        // 记录到热缓存
        if let Ok(risk_result) = &result {
            self.update_hot_cache(hot_key, *risk_result).await;
        }
        
        // 记录指标
        let latency = start_time.elapsed().as_millis() as f64;
        self.monitor.record_order_processing(latency).await;

        // 先记录调试信息，再记录指标
        if self.config.enable_debug_logging {
            debug!("Risk check completed for order, latency: {:.1}ms, result: {:?}",
                   latency, result);
        }

        let result_for_metrics = result.as_ref().map(|&b| b).unwrap_or(false);
        self.monitor.record_risk_check(result_for_metrics, false).await;

        result
    }
    
    /// 带熔断器的风控检查执行
    async fn execute_risk_check_with_circuit_breaker(&self, order: &Order) -> SigmaXResult<bool> {
        match self.circuit_breaker.execute(|| async {
            // 使用基础适配器的缓存检查
            let cache_key = self.base.generate_cache_key("live_order", order);
            self.base.cached_risk_check(&cache_key, || async {
                // 调用底层风控引擎
                let risk_result = self.base.risk_engine().check_order_risk(order, Some("live")).await?;
                Ok(risk_result.passed)
            }).await
        }).await {
            Ok(result) => {
                self.circuit_breaker.record_success().await;
                Ok(result)
            }
            Err(e) => {
                self.circuit_breaker.record_failure().await;
                self.monitor.record_error().await;
                
                if self.circuit_breaker.is_open().await {
                    self.monitor.record_circuit_breaker_trip().await;
                    warn!("Circuit breaker tripped due to error: {}", e);
                }
                
                Err(e)
            }
        }
    }
    
    /// 生成热缓存键
    fn generate_hot_cache_key(&self, order: &Order) -> String {
        format!(
            "hot:{}:{}:{}:{}",
            order.trading_pair.base,
            order.trading_pair.quote,
            order.order_type.to_string().to_lowercase(),
            order.side.to_string().to_lowercase()
        )
    }
    
    /// 检查热缓存
    async fn check_hot_cache(&self, key: &str) -> Option<bool> {
        let mut cache = self.hot_cache.write().await;
        if let Some(entry) = cache.get_mut(key) {
            if entry.is_hot() {
                return Some(entry.access());
            } else {
                // 移除不再热门的条目
                cache.remove(key);
            }
        }
        None
    }
    
    /// 更新热缓存
    async fn update_hot_cache(&self, key: String, result: bool) {
        let mut cache = self.hot_cache.write().await;
        
        // 如果缓存已满，移除最不活跃的条目
        if cache.len() >= self.config.hot_cache_size {
            if let Some((least_active_key, _)) = cache.iter()
                .min_by_key(|(_, entry)| entry.access_count)
                .map(|(k, v)| (k.clone(), v.clone()))
            {
                cache.remove(&least_active_key);
            }
        }
        
        cache.insert(key, HotCacheEntry::new(result));
    }
    
    /// 获取热缓存统计
    pub async fn get_hot_cache_stats(&self) -> HotCacheStats {
        let cache = self.hot_cache.read().await;
        let total_entries = cache.len();
        let hot_entries = cache.values().filter(|entry| entry.is_hot()).count();
        let total_accesses: u64 = cache.values().map(|entry| entry.access_count).sum();
        
        HotCacheStats {
            total_entries,
            hot_entries,
            cold_entries: total_entries - hot_entries,
            total_accesses,
            avg_accesses_per_entry: if total_entries > 0 {
                total_accesses as f64 / total_entries as f64
            } else {
                0.0
            },
        }
    }
    
    /// 强制刷新热缓存
    pub async fn refresh_hot_cache(&self) -> SigmaXResult<()> {
        info!("Refreshing hot cache");
        
        {
            let mut cache = self.hot_cache.write().await;
            cache.clear();
        }
        
        if self.config.preload_hot_data {
            self.preload_hot_cache().await?;
        }
        
        info!("Hot cache refreshed successfully");
        Ok(())
    }
    
    /// 获取性能监控报告
    pub async fn get_performance_report(&self) -> LiveTradingPerformanceReport {
        let stats = self.monitor.get_stats().await;
        let hot_cache_stats = self.get_hot_cache_stats().await;
        let circuit_breaker_stats = self.circuit_breaker.get_stats().await;
        
        LiveTradingPerformanceReport {
            stats,
            hot_cache_stats,
            circuit_breaker_stats,
            health_status: self.monitor.health_check().await,
            alerts: self.monitor.check_alerts().await,
        }
    }
}

/// 热缓存统计
#[derive(Debug, Clone)]
pub struct HotCacheStats {
    pub total_entries: usize,
    pub hot_entries: usize,
    pub cold_entries: usize,
    pub total_accesses: u64,
    pub avg_accesses_per_entry: f64,
}

/// 实盘性能报告
#[derive(Debug, Clone)]
pub struct LiveTradingPerformanceReport {
    pub stats: LiveTradingStats,
    pub hot_cache_stats: HotCacheStats,
    pub circuit_breaker_stats: CircuitBreakerStats,
    pub health_status: super::HealthStatus,
    pub alerts: Vec<super::Alert>,
}

impl LiveTradingPerformanceReport {
    /// 打印报告
    pub fn print(&self) {
        println!("📊 实盘交易性能报告");
        println!("==================");
        
        println!("📈 交易统计:");
        println!("  处理订单: {}", self.stats.orders_processed);
        println!("  风控检查: {}", self.stats.risk_checks);
        println!("  风控通过率: {:.1}%", self.stats.risk_pass_rate() * 100.0);
        println!("  缓存命中率: {:.1}%", self.stats.cache_hit_rate() * 100.0);
        
        println!("⚡ 性能指标:");
        println!("  平均延迟: {:.1}ms", self.stats.avg_latency_ms);
        println!("  P99延迟: {:.1}ms", self.stats.p99_latency_ms);
        println!("  错误率: {:.2}%", self.stats.error_rate() * 100.0);
        
        println!("🔥 热缓存统计:");
        println!("  总条目: {}", self.hot_cache_stats.total_entries);
        println!("  热条目: {}", self.hot_cache_stats.hot_entries);
        println!("  平均访问次数: {:.1}", self.hot_cache_stats.avg_accesses_per_entry);
        
        println!("🔌 熔断器状态:");
        println!("  状态: {:?}", self.circuit_breaker_stats.state);
        println!("  失败次数: {}", self.circuit_breaker_stats.failure_count);
        println!("  成功次数: {}", self.circuit_breaker_stats.success_count);
        
        println!("🏥 健康状态: {:?}", self.health_status);
        
        if !self.alerts.is_empty() {
            println!("🚨 告警信息:");
            for alert in &self.alerts {
                alert.print();
            }
        }
    }
}

#[async_trait]
impl EngineRiskAdapter for LiveTradingRiskAdapter {
    async fn check_order_risk(&self, order: &Order) -> SigmaXResult<bool> {
        if self.config.low_latency_mode {
            self.low_latency_check(order).await
        } else {
            // 标准模式，使用基础适配器
            let cache_key = self.base.generate_cache_key("live_order", order);
            self.base.cached_risk_check(&cache_key, || async {
                let risk_result = self.base.risk_engine().check_order_risk(order, Some("live")).await?;
                Ok(risk_result.passed)
            }).await
        }
    }
    
    async fn check_position_risk(&self, balances: &[Balance]) -> SigmaXResult<bool> {
        let start_time = Instant::now();
        
        // 检查熔断器状态
        if !self.circuit_breaker.can_execute().await {
            warn!("Circuit breaker is open, rejecting position check");
            return Ok(false);
        }
        
        let result = self.circuit_breaker.execute(|| async {
            self.base.execute_with_retry(|| async {
                let risk_result = self.base.risk_engine().check_position_risk(balances, Some("live")).await?;
                Ok(risk_result.passed)
            }).await
        }).await;
        
        // 记录指标
        let latency = start_time.elapsed().as_millis() as f64;
        if self.base.config().enable_metrics {
            self.base.metrics_collector().record_latency("position_risk_check", start_time.elapsed()).await;
        }
        
        match &result {
            Ok(_) => self.circuit_breaker.record_success().await,
            Err(_) => {
                self.circuit_breaker.record_failure().await;
                self.monitor.record_error().await;
            }
        }
        
        result
    }
    
    fn engine_type(&self) -> EngineType {
        EngineType::Live
    }
    
    async fn get_metrics(&self) -> SigmaXResult<AdapterMetrics> {
        let stats = self.monitor.get_stats().await;
        
        Ok(AdapterMetrics {
            engine_type: EngineType::Live,
            cache_hit_rate: stats.cache_hit_rate(),
            avg_latency_ms: stats.avg_latency_ms,
            throughput_rps: if stats.avg_latency_ms > 0.0 {
                1000.0 / stats.avg_latency_ms // 基于延迟估算吞吐量
            } else {
                0.0
            },
            error_rate: stats.error_rate(),
        })
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

// 实现RiskManager trait以保持向后兼容
#[async_trait]
impl RiskManager for LiveTradingRiskAdapter {
    async fn check_order_risk(&self, order: &Order) -> SigmaXResult<bool> {
        EngineRiskAdapter::check_order_risk(self, order).await
    }
    
    async fn check_position_risk(&self, balances: &[Balance]) -> SigmaXResult<bool> {
        EngineRiskAdapter::check_position_risk(self, balances).await
    }
    
    async fn get_max_order_size(&self, trading_pair: &TradingPair) -> SigmaXResult<Quantity> {
        // 实盘模式下的最大订单大小计算
        // 这里可以基于实时市场数据和风控规则计算
        Ok(Quantity::from(100)) // 临时返回固定值，实际应该动态计算
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_live_adapter_config_default() {
        let config = LiveAdapterConfig::default();
        assert!(config.low_latency_mode);
        assert_eq!(config.hot_cache_size, 1000);
        assert_eq!(config.timeout_ms, 100);
        assert_eq!(config.circuit_breaker_threshold, 0.1);
    }
    
    #[test]
    fn test_hot_cache_entry() {
        let mut entry = HotCacheEntry::new(true);
        assert_eq!(entry.access(), true);
        assert_eq!(entry.access_count, 2); // 创建时1次，访问时1次
        
        // 模拟多次访问
        for _ in 0..5 {
            entry.access();
        }
        assert!(entry.is_hot()); // 访问次数超过5次应该是热数据
    }
    
    #[tokio::test]
    async fn test_hot_cache_stats() {
        let config = LiveAdapterConfig::default();
        // 这个测试需要Mock实现才能完成
        // 实际测试将在具体服务实现后添加
        assert_eq!(config.hot_cache_size, 1000);
    }
}
