//! 实时风险预警系统
//! 
//! 实现自动风险阈值监控和预警触发

use sigmax_core::SigmaXResult;
use std::collections::{HashMap, VecDeque};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc, Duration};
use uuid::Uuid;
use tokio::sync::{RwLock, mpsc};
use std::sync::Arc;
use tracing::{info, warn, error, debug};

/// 风险预警类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum AlertType {
    /// VaR超限
    VarBreach,
    /// CVaR超限
    CvarBreach,
    /// 最大回撤超限
    MaxDrawdownBreach,
    /// 日损失超限
    DailyLossBreach,
    /// 持仓集中度超限
    ConcentrationBreach,
    /// 流动性不足
    LiquidityShortage,
    /// 相关性风险
    CorrelationRisk,
    /// 信用风险
    CreditRisk,
    /// 系统风险
    SystemRisk,
    /// 自定义风险
    CustomRisk(String),
}

/// 预警严重程度
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum AlertSeverity {
    /// 信息
    Info = 1,
    /// 警告
    Warning = 2,
    /// 严重
    Critical = 3,
    /// 紧急
    Emergency = 4,
}

/// 风险预警
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAlert {
    /// 预警ID
    pub alert_id: Uuid,
    /// 预警类型
    pub alert_type: AlertType,
    /// 严重程度
    pub severity: AlertSeverity,
    /// 预警消息
    pub message: String,
    /// 当前值
    pub current_value: f64,
    /// 阈值
    pub threshold_value: f64,
    /// 超出百分比
    pub breach_percentage: f64,
    /// 触发时间
    pub triggered_at: DateTime<Utc>,
    /// 相关策略ID
    pub strategy_id: Option<Uuid>,
    /// 相关交易对
    pub trading_pair: Option<String>,
    /// 建议动作
    pub recommended_actions: Vec<String>,
    /// 是否已确认
    pub acknowledged: bool,
    /// 确认时间
    pub acknowledged_at: Option<DateTime<Utc>>,
}

/// 预警规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertRule {
    /// 规则ID
    pub rule_id: Uuid,
    /// 规则名称
    pub name: String,
    /// 预警类型
    pub alert_type: AlertType,
    /// 阈值配置
    pub threshold_config: ThresholdConfig,
    /// 是否启用
    pub enabled: bool,
    /// 检查间隔（秒）
    pub check_interval_seconds: u64,
    /// 冷却期（秒）- 避免重复预警
    pub cooldown_seconds: u64,
    /// 最后触发时间
    pub last_triggered: Option<DateTime<Utc>>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// 阈值配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThresholdConfig {
    /// 警告阈值
    pub warning_threshold: f64,
    /// 严重阈值
    pub critical_threshold: f64,
    /// 紧急阈值
    pub emergency_threshold: f64,
    /// 阈值类型（绝对值或百分比）
    pub threshold_type: ThresholdType,
    /// 比较方向
    pub comparison: ComparisonOperator,
}

/// 阈值类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThresholdType {
    /// 绝对值
    Absolute,
    /// 百分比
    Percentage,
}

/// 比较操作符
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComparisonOperator {
    /// 大于
    GreaterThan,
    /// 小于
    LessThan,
    /// 等于
    Equal,
}

/// 风险监控数据
#[derive(Debug, Clone)]
pub struct RiskMonitoringData {
    /// VaR值
    pub var_95: f64,
    /// CVaR值
    pub cvar_95: f64,
    /// 当前回撤
    pub current_drawdown: f64,
    /// 最大回撤
    pub max_drawdown: f64,
    /// 日损益
    pub daily_pnl: f64,
    /// 总损益
    pub total_pnl: f64,
    /// 持仓集中度
    pub concentration_ratio: f64,
    /// 流动性评分
    pub liquidity_score: f64,
    /// 相关性风险
    pub correlation_risk: f64,
    /// 信用风险
    pub credit_risk: f64,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 实时风险预警系统
pub struct RealTimeAlertSystem {
    /// 预警规则
    alert_rules: Arc<RwLock<HashMap<Uuid, AlertRule>>>,
    /// 活跃预警
    active_alerts: Arc<RwLock<HashMap<Uuid, RiskAlert>>>,
    /// 预警历史
    alert_history: Arc<RwLock<VecDeque<RiskAlert>>>,
    /// 风险监控数据
    monitoring_data: Arc<RwLock<Option<RiskMonitoringData>>>,
    /// 预警发送器
    alert_sender: mpsc::UnboundedSender<RiskAlert>,
    /// 预警接收器
    alert_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<RiskAlert>>>>,
    /// 最大历史记录数
    max_history_size: usize,
}

impl RealTimeAlertSystem {
    /// 创建新的实时预警系统
    pub fn new(max_history_size: usize) -> Self {
        let (alert_sender, alert_receiver) = mpsc::unbounded_channel();
        
        Self {
            alert_rules: Arc::new(RwLock::new(HashMap::new())),
            active_alerts: Arc::new(RwLock::new(HashMap::new())),
            alert_history: Arc::new(RwLock::new(VecDeque::new())),
            monitoring_data: Arc::new(RwLock::new(None)),
            alert_sender,
            alert_receiver: Arc::new(RwLock::new(Some(alert_receiver))),
            max_history_size,
        }
    }

    /// 添加预警规则
    pub async fn add_alert_rule(&self, rule: AlertRule) -> SigmaXResult<()> {
        let mut rules = self.alert_rules.write().await;
        rules.insert(rule.rule_id, rule);
        info!("添加预警规则: {}", rules.len());
        Ok(())
    }

    /// 移除预警规则
    pub async fn remove_alert_rule(&self, rule_id: Uuid) -> SigmaXResult<()> {
        let mut rules = self.alert_rules.write().await;
        rules.remove(&rule_id);
        info!("移除预警规则: {}", rule_id);
        Ok(())
    }

    /// 更新风险监控数据
    pub async fn update_monitoring_data(&self, data: RiskMonitoringData) -> SigmaXResult<()> {
        {
            let mut monitoring_data = self.monitoring_data.write().await;
            *monitoring_data = Some(data.clone());
        }
        
        // 触发风险检查
        self.check_all_rules(&data).await?;
        
        Ok(())
    }

    /// 检查所有预警规则
    async fn check_all_rules(&self, data: &RiskMonitoringData) -> SigmaXResult<()> {
        let rules = self.alert_rules.read().await;
        let now = Utc::now();
        
        for rule in rules.values() {
            if !rule.enabled {
                continue;
            }
            
            // 检查冷却期
            if let Some(last_triggered) = rule.last_triggered {
                let cooldown_duration = Duration::seconds(rule.cooldown_seconds as i64);
                if now - last_triggered < cooldown_duration {
                    continue;
                }
            }
            
            // 检查规则
            if let Some(alert) = self.check_rule(rule, data).await? {
                self.trigger_alert(alert).await?;
                
                // 更新最后触发时间
                let mut rules_mut = self.alert_rules.write().await;
                if let Some(rule_mut) = rules_mut.get_mut(&rule.rule_id) {
                    rule_mut.last_triggered = Some(now);
                }
            }
        }
        
        Ok(())
    }

    /// 检查单个规则
    async fn check_rule(&self, rule: &AlertRule, data: &RiskMonitoringData) -> SigmaXResult<Option<RiskAlert>> {
        let current_value = self.get_metric_value(&rule.alert_type, data)?;
        let threshold_config = &rule.threshold_config;
        
        // 确定严重程度
        let severity = if self.check_threshold(current_value, threshold_config.emergency_threshold, &threshold_config.comparison) {
            AlertSeverity::Emergency
        } else if self.check_threshold(current_value, threshold_config.critical_threshold, &threshold_config.comparison) {
            AlertSeverity::Critical
        } else if self.check_threshold(current_value, threshold_config.warning_threshold, &threshold_config.comparison) {
            AlertSeverity::Warning
        } else {
            return Ok(None); // 未触发阈值
        };
        
        // 计算超出百分比
        let threshold_value = match severity {
            AlertSeverity::Emergency => threshold_config.emergency_threshold,
            AlertSeverity::Critical => threshold_config.critical_threshold,
            AlertSeverity::Warning => threshold_config.warning_threshold,
            AlertSeverity::Info => threshold_config.warning_threshold,
        };
        
        let breach_percentage = if threshold_value != 0.0 {
            ((current_value - threshold_value) / threshold_value * 100.0).abs()
        } else {
            0.0
        };
        
        // 生成预警消息
        let message = self.generate_alert_message(&rule.alert_type, current_value, threshold_value, &severity);
        
        // 生成建议动作
        let recommended_actions = self.generate_recommended_actions(&rule.alert_type, &severity);
        
        let alert = RiskAlert {
            alert_id: Uuid::new_v4(),
            alert_type: rule.alert_type.clone(),
            severity,
            message,
            current_value,
            threshold_value,
            breach_percentage,
            triggered_at: Utc::now(),
            strategy_id: None, // 可以根据需要设置
            trading_pair: None, // 可以根据需要设置
            recommended_actions,
            acknowledged: false,
            acknowledged_at: None,
        };
        
        Ok(Some(alert))
    }

    /// 获取指标值
    fn get_metric_value(&self, alert_type: &AlertType, data: &RiskMonitoringData) -> SigmaXResult<f64> {
        let value = match alert_type {
            AlertType::VarBreach => data.var_95,
            AlertType::CvarBreach => data.cvar_95,
            AlertType::MaxDrawdownBreach => data.max_drawdown,
            AlertType::DailyLossBreach => data.daily_pnl,
            AlertType::ConcentrationBreach => data.concentration_ratio,
            AlertType::LiquidityShortage => data.liquidity_score,
            AlertType::CorrelationRisk => data.correlation_risk,
            AlertType::CreditRisk => data.credit_risk,
            AlertType::SystemRisk => 0.0, // 需要额外实现
            AlertType::CustomRisk(_) => 0.0, // 需要额外实现
        };
        
        Ok(value)
    }

    /// 检查阈值
    fn check_threshold(&self, current_value: f64, threshold: f64, comparison: &ComparisonOperator) -> bool {
        match comparison {
            ComparisonOperator::GreaterThan => current_value > threshold,
            ComparisonOperator::LessThan => current_value < threshold,
            ComparisonOperator::Equal => (current_value - threshold).abs() < 1e-6,
        }
    }

    /// 触发预警
    async fn trigger_alert(&self, alert: RiskAlert) -> SigmaXResult<()> {
        let alert_id = alert.alert_id;
        
        // 添加到活跃预警
        {
            let mut active_alerts = self.active_alerts.write().await;
            active_alerts.insert(alert_id, alert.clone());
        }
        
        // 添加到历史记录
        {
            let mut history = self.alert_history.write().await;
            history.push_back(alert.clone());
            
            // 保持历史记录在限制范围内
            if history.len() > self.max_history_size {
                history.pop_front();
            }
        }
        
        // 发送预警通知 (非阻塞)
        if let Err(_) = self.alert_sender.send(alert.clone()) {
            // 通道关闭时忽略，不阻塞主流程
            debug!("预警通知通道已关闭，跳过发送");
        }
        
        // 记录日志
        match alert.severity {
            AlertSeverity::Emergency => error!("🚨 紧急预警: {}", alert.message),
            AlertSeverity::Critical => error!("⚠️ 严重预警: {}", alert.message),
            AlertSeverity::Warning => warn!("⚠️ 风险警告: {}", alert.message),
            AlertSeverity::Info => info!("ℹ️ 风险信息: {}", alert.message),
        }
        
        Ok(())
    }

    /// 生成预警消息
    fn generate_alert_message(&self, alert_type: &AlertType, current_value: f64, threshold_value: f64, severity: &AlertSeverity) -> String {
        let severity_text = match severity {
            AlertSeverity::Emergency => "紧急",
            AlertSeverity::Critical => "严重",
            AlertSeverity::Warning => "警告",
            AlertSeverity::Info => "信息",
        };
        
        let alert_type_text = match alert_type {
            AlertType::VarBreach => "VaR超限",
            AlertType::CvarBreach => "CVaR超限",
            AlertType::MaxDrawdownBreach => "最大回撤超限",
            AlertType::DailyLossBreach => "日损失超限",
            AlertType::ConcentrationBreach => "持仓集中度超限",
            AlertType::LiquidityShortage => "流动性不足",
            AlertType::CorrelationRisk => "相关性风险",
            AlertType::CreditRisk => "信用风险",
            AlertType::SystemRisk => "系统风险",
            AlertType::CustomRisk(name) => name,
        };
        
        format!("{} - {}: 当前值 {:.4}, 阈值 {:.4}", 
                severity_text, alert_type_text, current_value, threshold_value)
    }

    /// 生成建议动作
    fn generate_recommended_actions(&self, alert_type: &AlertType, severity: &AlertSeverity) -> Vec<String> {
        let mut actions = Vec::new();
        
        match alert_type {
            AlertType::VarBreach | AlertType::CvarBreach => {
                actions.push("减少风险敞口".to_string());
                actions.push("调整投资组合配置".to_string());
                if matches!(severity, AlertSeverity::Emergency) {
                    actions.push("立即止损".to_string());
                }
            }
            AlertType::MaxDrawdownBreach => {
                actions.push("暂停新开仓".to_string());
                actions.push("考虑部分平仓".to_string());
            }
            AlertType::DailyLossBreach => {
                actions.push("暂停交易".to_string());
                actions.push("检查策略参数".to_string());
            }
            AlertType::ConcentrationBreach => {
                actions.push("分散投资组合".to_string());
                actions.push("减少单一资产持仓".to_string());
            }
            AlertType::LiquidityShortage => {
                actions.push("增加流动性缓冲".to_string());
                actions.push("避免大额交易".to_string());
            }
            _ => {
                actions.push("检查风险状况".to_string());
                actions.push("考虑调整策略".to_string());
            }
        }
        
        actions
    }

    /// 确认预警
    pub async fn acknowledge_alert(&self, alert_id: Uuid) -> SigmaXResult<()> {
        let mut active_alerts = self.active_alerts.write().await;
        
        if let Some(alert) = active_alerts.get_mut(&alert_id) {
            alert.acknowledged = true;
            alert.acknowledged_at = Some(Utc::now());
            info!("预警已确认: {}", alert_id);
        }
        
        Ok(())
    }

    /// 获取活跃预警
    pub async fn get_active_alerts(&self) -> Vec<RiskAlert> {
        let active_alerts = self.active_alerts.read().await;
        active_alerts.values().cloned().collect()
    }

    /// 获取预警历史
    pub async fn get_alert_history(&self, limit: Option<usize>) -> Vec<RiskAlert> {
        let history = self.alert_history.read().await;
        let limit = limit.unwrap_or(history.len());
        
        history.iter()
            .rev()
            .take(limit)
            .cloned()
            .collect()
    }

    /// 清除已确认的预警
    pub async fn clear_acknowledged_alerts(&self) -> SigmaXResult<usize> {
        let mut active_alerts = self.active_alerts.write().await;
        let initial_count = active_alerts.len();
        
        active_alerts.retain(|_, alert| !alert.acknowledged);
        
        let cleared_count = initial_count - active_alerts.len();
        info!("清除了 {} 个已确认的预警", cleared_count);
        
        Ok(cleared_count)
    }

    /// 获取预警统计
    pub async fn get_alert_statistics(&self) -> AlertStatistics {
        let active_alerts = self.active_alerts.read().await;
        let history = self.alert_history.read().await;
        
        let mut stats = AlertStatistics::default();
        stats.total_active_alerts = active_alerts.len();
        stats.total_historical_alerts = history.len();
        
        // 统计活跃预警的严重程度
        for alert in active_alerts.values() {
            match alert.severity {
                AlertSeverity::Emergency => stats.emergency_alerts += 1,
                AlertSeverity::Critical => stats.critical_alerts += 1,
                AlertSeverity::Warning => stats.warning_alerts += 1,
                AlertSeverity::Info => stats.info_alerts += 1,
            }
            
            if !alert.acknowledged {
                stats.unacknowledged_alerts += 1;
            }
        }
        
        stats
    }
}

/// 预警统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct AlertStatistics {
    pub total_active_alerts: usize,
    pub total_historical_alerts: usize,
    pub emergency_alerts: usize,
    pub critical_alerts: usize,
    pub warning_alerts: usize,
    pub info_alerts: usize,
    pub unacknowledged_alerts: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration as TokioDuration};

    #[tokio::test]
    async fn test_alert_system() {
        let alert_system = RealTimeAlertSystem::new(100);
        
        // 创建测试规则
        let rule = AlertRule {
            rule_id: Uuid::new_v4(),
            name: "VaR测试规则".to_string(),
            alert_type: AlertType::VarBreach,
            threshold_config: ThresholdConfig {
                warning_threshold: 1000.0,
                critical_threshold: 2000.0,
                emergency_threshold: 3000.0,
                threshold_type: ThresholdType::Absolute,
                comparison: ComparisonOperator::GreaterThan,
            },
            enabled: true,
            check_interval_seconds: 1,
            cooldown_seconds: 5,
            last_triggered: None,
            created_at: Utc::now(),
        };
        
        alert_system.add_alert_rule(rule).await.unwrap();
        
        // 更新监控数据，触发预警
        let monitoring_data = RiskMonitoringData {
            var_95: 2500.0, // 超过严重阈值
            cvar_95: 3000.0,
            current_drawdown: 0.05,
            max_drawdown: 0.1,
            daily_pnl: -500.0,
            total_pnl: 1000.0,
            concentration_ratio: 0.3,
            liquidity_score: 0.8,
            correlation_risk: 0.4,
            credit_risk: 0.2,
            updated_at: Utc::now(),
        };
        
        alert_system.update_monitoring_data(monitoring_data).await.unwrap();
        
        // 等待预警处理
        sleep(TokioDuration::from_millis(100)).await;
        
        // 检查预警
        let active_alerts = alert_system.get_active_alerts().await;
        assert!(!active_alerts.is_empty());
        
        let alert = &active_alerts[0];
        assert_eq!(alert.alert_type, AlertType::VarBreach);
        assert_eq!(alert.severity, AlertSeverity::Critical);
        
        // 确认预警
        alert_system.acknowledge_alert(alert.alert_id).await.unwrap();
        
        let stats = alert_system.get_alert_statistics().await;
        assert_eq!(stats.unacknowledged_alerts, 0);
    }
}
