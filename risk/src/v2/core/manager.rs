//! 统一风控管理器接口
//!
//! 提供统一的风控管理接口，符合最佳实践架构设计

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, Order, Balance};
use std::sync::Arc;
use super::{RiskCore, RiskRequest, RiskResult, RiskContext};
use crate::v2::rules::RiskRule;

/// 统一风控管理器接口
/// 
/// 按照最佳实践架构设计，提供以下核心功能：
/// - check_order_risk(): 订单风控检查
/// - check_position_risk(): 持仓风控检查  
/// - get_risk_metrics(): 风险指标获取
#[async_trait]
pub trait RiskManager: Send + Sync {
    /// 检查订单风险
    /// 
    /// # 参数
    /// - `order`: 待检查的订单
    /// - `context`: 风控上下文信息
    /// 
    /// # 返回
    /// - `RiskResult`: 详细的风控检查结果
    async fn check_order_risk(
        &self, 
        order: &Order, 
        context: &RiskContext
    ) -> SigmaXResult<RiskResult>;

    /// 检查持仓风险
    /// 
    /// # 参数
    /// - `balances`: 当前持仓信息
    /// - `context`: 风控上下文信息
    /// 
    /// # 返回
    /// - `RiskResult`: 详细的风控检查结果
    async fn check_position_risk(
        &self, 
        balances: &[Balance], 
        context: &RiskContext
    ) -> SigmaXResult<RiskResult>;

    /// 获取风险指标
    /// 
    /// # 返回
    /// - `RiskMetrics`: 当前风险指标数据
    async fn get_risk_metrics(&self) -> SigmaXResult<RiskMetrics>;

    /// 添加风控规则
    async fn add_rule(&self, rule: Box<dyn RiskRule>) -> SigmaXResult<()>;

    /// 移除风控规则
    async fn remove_rule(&self, rule_name: &str) -> SigmaXResult<bool>;

    /// 获取规则列表
    async fn list_rules(&self) -> Vec<String>;

    /// 重新加载规则配置
    async fn reload_rules(&self) -> SigmaXResult<()>;
}

/// 风险指标数据
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct RiskMetrics {
    /// 总检查次数
    pub total_checks: u64,
    /// 通过次数
    pub passed_checks: u64,
    /// 失败次数
    pub failed_checks: u64,
    /// 平均执行时间（微秒）
    pub avg_execution_time_us: u64,
    /// 缓存命中率
    pub cache_hit_rate: f64,
    /// 活跃规则数量
    pub active_rules_count: usize,
    /// 最后更新时间
    pub last_updated: chrono::DateTime<chrono::Utc>,
}

/// 基于V2 RiskCore的统一风控管理器实现
pub struct UnifiedRiskManager {
    /// V2微内核风控引擎
    core: Arc<RiskCore>,
}

impl UnifiedRiskManager {
    /// 创建新的统一风控管理器
    pub fn new(core: Arc<RiskCore>) -> Self {
        Self { core }
    }

    /// 获取底层的RiskCore引用
    pub fn core(&self) -> &Arc<RiskCore> {
        &self.core
    }
}

#[async_trait]
impl RiskManager for UnifiedRiskManager {
    async fn check_order_risk(
        &self, 
        order: &Order, 
        context: &RiskContext
    ) -> SigmaXResult<RiskResult> {
        // 构建V2风控请求
        let request = RiskRequest::from_order_with_context(
            order.clone(), 
            context.clone()
        );
        
        // 调用V2核心引擎
        self.core.check_risk(request).await
    }

    async fn check_position_risk(
        &self, 
        balances: &[Balance], 
        context: &RiskContext
    ) -> SigmaXResult<RiskResult> {
        // 构建V2风控请求
        let request = RiskRequest::from_balances_with_context(
            balances.to_vec(), 
            context.clone()
        );
        
        // 调用V2核心引擎
        self.core.check_risk(request).await
    }

    async fn get_risk_metrics(&self) -> SigmaXResult<RiskMetrics> {
        // 从V2核心引擎获取统计信息
        let stats = self.core.get_execution_stats().await;
        let rules_count = self.core.list_rules().await.len();
        
        Ok(RiskMetrics {
            total_checks: stats.total_checks,
            passed_checks: stats.total_checks - stats.failed_checks,
            failed_checks: stats.failed_checks,
            avg_execution_time_us: if stats.total_checks > 0 {
                stats.total_duration_us / stats.total_checks
            } else {
                0
            },
            cache_hit_rate: if stats.cache_hits + stats.cache_misses > 0 {
                stats.cache_hits as f64 / (stats.cache_hits + stats.cache_misses) as f64
            } else {
                0.0
            },
            active_rules_count: rules_count,
            last_updated: chrono::Utc::now(),
        })
    }

    async fn add_rule(&self, rule: Box<dyn RiskRule>) -> SigmaXResult<()> {
        self.core.add_rule(rule).await
    }

    async fn remove_rule(&self, rule_name: &str) -> SigmaXResult<bool> {
        self.core.remove_rule(rule_name).await
    }

    async fn list_rules(&self) -> Vec<String> {
        self.core.list_rules().await
    }

    async fn reload_rules(&self) -> SigmaXResult<()> {
        // V2核心引擎支持动态规则管理，无需特殊重载逻辑
        tracing::info!("风控规则重载完成（V2引擎支持动态管理）");
        Ok(())
    }
}

/// 兼容性适配器，用于将现有的RiskManager trait适配到新的统一接口
pub struct CompatibilityAdapter<T: sigmax_core::RiskManager> {
    legacy_manager: Arc<T>,
}

impl<T: sigmax_core::RiskManager> CompatibilityAdapter<T> {
    pub fn new(legacy_manager: Arc<T>) -> Self {
        Self { legacy_manager }
    }
}

#[async_trait]
impl<T: sigmax_core::RiskManager> RiskManager for CompatibilityAdapter<T> {
    async fn check_order_risk(
        &self, 
        order: &Order, 
        _context: &RiskContext
    ) -> SigmaXResult<RiskResult> {
        // 调用传统接口
        let passed = self.legacy_manager.check_order_risk(order).await?;
        
        // 转换为新的结果格式
        Ok(if passed {
            RiskResult::pass(uuid::Uuid::new_v4())
        } else {
            RiskResult::fail_with_message(uuid::Uuid::new_v4(), "Legacy risk check failed".to_string())
        })
    }

    async fn check_position_risk(
        &self, 
        balances: &[Balance], 
        _context: &RiskContext
    ) -> SigmaXResult<RiskResult> {
        // 调用传统接口
        let passed = self.legacy_manager.check_position_risk(balances).await?;
        
        // 转换为新的结果格式
        Ok(if passed {
            RiskResult::pass(uuid::Uuid::new_v4())
        } else {
            RiskResult::fail_with_message(uuid::Uuid::new_v4(), "Legacy position risk check failed".to_string())
        })
    }

    async fn get_risk_metrics(&self) -> SigmaXResult<RiskMetrics> {
        // 传统接口没有指标，返回默认值
        Ok(RiskMetrics {
            total_checks: 0,
            passed_checks: 0,
            failed_checks: 0,
            avg_execution_time_us: 0,
            cache_hit_rate: 0.0,
            active_rules_count: 0,
            last_updated: chrono::Utc::now(),
        })
    }

    async fn add_rule(&self, _rule: Box<dyn RiskRule>) -> SigmaXResult<()> {
        // 传统接口不支持动态规则管理
        Err(sigmax_core::SigmaXError::NotImplemented("Legacy manager does not support dynamic rule management".to_string()))
    }

    async fn remove_rule(&self, _rule_name: &str) -> SigmaXResult<bool> {
        // 传统接口不支持动态规则管理
        Err(sigmax_core::SigmaXError::NotImplemented("Legacy manager does not support dynamic rule management".to_string()))
    }

    async fn list_rules(&self) -> Vec<String> {
        // 传统接口不支持规则列表
        vec![]
    }

    async fn reload_rules(&self) -> SigmaXResult<()> {
        // 传统接口不支持规则重载
        Ok(())
    }
}
