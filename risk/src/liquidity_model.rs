//! 流动性风险模型
//! 
//! 实现市场冲击成本和流动性风险评估

use sigmax_core::{Amount, SigmaXResult, SigmaXError, TradingPair, OrderSide};
use rust_decimal::Decimal;
use std::collections::{HashMap, VecDeque};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

/// 流动性风险指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidityRiskMetrics {
    /// 整体流动性风险评分 (0-1, 1为最高风险)
    pub overall_liquidity_risk: f64,
    /// 市场冲击成本估计
    pub market_impact_cost: f64,
    /// 买卖价差风险
    pub bid_ask_spread_risk: f64,
    /// 成交量充足性评分
    pub volume_adequacy_score: f64,
    /// 流动性缓冲区建议
    pub liquidity_buffer_recommendation: f64,
    /// 各资产流动性评分
    pub asset_liquidity_scores: HashMap<String, f64>,
    /// 计算时间戳
    pub calculated_at: DateTime<Utc>,
}

/// 市场深度数据
#[derive(Debug, Clone)]
pub struct MarketDepth {
    /// 买单深度 (价格 -> 数量)
    pub bids: Vec<(Decimal, Decimal)>,
    /// 卖单深度 (价格 -> 数量)
    pub asks: Vec<(Decimal, Decimal)>,
    /// 最佳买价
    pub best_bid: Decimal,
    /// 最佳卖价
    pub best_ask: Decimal,
    /// 时间戳
    pub timestamp: DateTime<Utc>,
}

/// 流动性数据
#[derive(Debug, Clone)]
pub struct LiquidityData {
    /// 交易对
    pub trading_pair: TradingPair,
    /// 24小时成交量
    pub volume_24h: Amount,
    /// 历史成交量
    pub volume_history: VecDeque<Amount>,
    /// 买卖价差历史
    pub spread_history: VecDeque<Decimal>,
    /// 市场深度历史
    pub depth_history: VecDeque<MarketDepth>,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

/// 流动性风险计算器
pub struct LiquidityRiskCalculator {
    /// 各资产的流动性数据
    liquidity_data: HashMap<String, LiquidityData>,
    /// 最大历史长度
    max_history_length: usize,
    /// 流动性评估参数
    assessment_params: LiquidityAssessmentParams,
}

/// 流动性评估参数
#[derive(Debug, Clone)]
pub struct LiquidityAssessmentParams {
    /// 最小成交量阈值
    pub min_volume_threshold: Amount,
    /// 最大价差阈值
    pub max_spread_threshold: Decimal,
    /// 市场深度要求
    pub min_depth_requirement: Amount,
    /// 流动性缓冲区比例
    pub liquidity_buffer_ratio: f64,
}

impl Default for LiquidityAssessmentParams {
    fn default() -> Self {
        Self {
            min_volume_threshold: Decimal::from(10000), // 最小日成交量10,000 USDT
            max_spread_threshold: Decimal::new(5, 3),   // 最大价差0.5%
            min_depth_requirement: Decimal::from(5000), // 最小深度5,000 USDT
            liquidity_buffer_ratio: 0.2,               // 20%流动性缓冲
        }
    }
}

impl LiquidityRiskCalculator {
    /// 创建新的流动性风险计算器
    pub fn new(max_history_length: usize) -> Self {
        Self {
            liquidity_data: HashMap::new(),
            max_history_length,
            assessment_params: LiquidityAssessmentParams::default(),
        }
    }

    /// 创建带自定义参数的流动性风险计算器
    pub fn with_params(max_history_length: usize, params: LiquidityAssessmentParams) -> Self {
        Self {
            liquidity_data: HashMap::new(),
            max_history_length,
            assessment_params: params,
        }
    }

    /// 添加资产
    pub fn add_asset(&mut self, trading_pair: TradingPair) {
        let asset_key = format!("{}_{}", trading_pair.base, trading_pair.quote);
        self.liquidity_data.insert(asset_key, LiquidityData {
            trading_pair,
            volume_24h: Amount::ZERO,
            volume_history: VecDeque::new(),
            spread_history: VecDeque::new(),
            depth_history: VecDeque::new(),
            last_updated: Utc::now(),
        });
    }

    /// 更新市场数据
    pub fn update_market_data(
        &mut self,
        trading_pair: &TradingPair,
        volume_24h: Amount,
        spread: Decimal,
        market_depth: MarketDepth,
    ) -> SigmaXResult<()> {
        let asset_key = format!("{}_{}", trading_pair.base, trading_pair.quote);
        
        let data = self.liquidity_data.get_mut(&asset_key)
            .ok_or_else(|| SigmaXError::InvalidOperation(
                format!("资产 {} 未注册", asset_key)
            ))?;
        
        // 更新数据
        data.volume_24h = volume_24h;
        data.last_updated = Utc::now();
        
        // 添加历史数据
        data.volume_history.push_back(volume_24h);
        data.spread_history.push_back(spread);
        data.depth_history.push_back(market_depth);
        
        // 保持历史数据在指定长度内
        if data.volume_history.len() > self.max_history_length {
            data.volume_history.pop_front();
        }
        if data.spread_history.len() > self.max_history_length {
            data.spread_history.pop_front();
        }
        if data.depth_history.len() > self.max_history_length {
            data.depth_history.pop_front();
        }
        
        Ok(())
    }

    /// 计算流动性风险指标
    pub fn calculate_liquidity_risk(&self) -> SigmaXResult<LiquidityRiskMetrics> {
        if self.liquidity_data.is_empty() {
            return Err(SigmaXError::InvalidOperation(
                "没有流动性数据可供分析".to_string()
            ));
        }

        let mut asset_liquidity_scores = HashMap::new();
        let mut total_risk_score = 0.0;
        let mut total_weight = 0.0;

        // 计算各资产的流动性评分
        for (asset_key, data) in &self.liquidity_data {
            let score = self.calculate_asset_liquidity_score(data)?;
            asset_liquidity_scores.insert(asset_key.clone(), score);
            
            // 使用成交量作为权重
            let weight = data.volume_24h.to_string().parse::<f64>().unwrap_or(0.0);
            total_risk_score += score * weight;
            total_weight += weight;
        }

        let overall_liquidity_risk = if total_weight > 0.0 {
            total_risk_score / total_weight
        } else {
            0.5 // 默认中等风险
        };

        let market_impact_cost = self.calculate_market_impact_cost()?;
        let bid_ask_spread_risk = self.calculate_bid_ask_spread_risk()?;
        let volume_adequacy_score = self.calculate_volume_adequacy_score()?;
        let liquidity_buffer_recommendation = self.calculate_liquidity_buffer_recommendation()?;

        Ok(LiquidityRiskMetrics {
            overall_liquidity_risk,
            market_impact_cost,
            bid_ask_spread_risk,
            volume_adequacy_score,
            liquidity_buffer_recommendation,
            asset_liquidity_scores,
            calculated_at: Utc::now(),
        })
    }

    /// 计算单个资产的流动性评分
    fn calculate_asset_liquidity_score(&self, data: &LiquidityData) -> SigmaXResult<f64> {
        let mut score = 0.0;
        let mut factors = 0;

        // 1. 成交量评分 (权重: 40%)
        if !data.volume_history.is_empty() {
            let avg_volume = data.volume_history.iter().sum::<Amount>() / Decimal::from(data.volume_history.len());
            let volume_score = if avg_volume >= self.assessment_params.min_volume_threshold {
                0.0 // 低风险
            } else {
                let ratio = avg_volume / self.assessment_params.min_volume_threshold;
                1.0 - ratio.to_string().parse::<f64>().unwrap_or(0.0).min(1.0)
            };
            score += volume_score * 0.4;
            factors += 1;
        }

        // 2. 价差评分 (权重: 30%)
        if !data.spread_history.is_empty() {
            let avg_spread = data.spread_history.iter().sum::<Decimal>() / Decimal::from(data.spread_history.len());
            let spread_score = if avg_spread <= self.assessment_params.max_spread_threshold {
                0.0 // 低风险
            } else {
                let ratio = avg_spread / self.assessment_params.max_spread_threshold;
                ratio.to_string().parse::<f64>().unwrap_or(1.0).min(1.0)
            };
            score += spread_score * 0.3;
            factors += 1;
        }

        // 3. 市场深度评分 (权重: 30%)
        if !data.depth_history.is_empty() {
            let latest_depth = &data.depth_history[data.depth_history.len() - 1];
            let depth_score = self.calculate_depth_score(latest_depth)?;
            score += depth_score * 0.3;
            factors += 1;
        }

        if factors > 0 {
            Ok(score)
        } else {
            Ok(0.5) // 默认中等风险
        }
    }

    /// 计算市场深度评分
    fn calculate_depth_score(&self, depth: &MarketDepth) -> SigmaXResult<f64> {
        // 计算买卖双方的深度
        let bid_depth: Decimal = depth.bids.iter()
            .take(10) // 取前10档
            .map(|(price, quantity)| price * quantity)
            .sum();
        
        let ask_depth: Decimal = depth.asks.iter()
            .take(10) // 取前10档
            .map(|(price, quantity)| price * quantity)
            .sum();
        
        let total_depth = bid_depth + ask_depth;
        
        if total_depth >= self.assessment_params.min_depth_requirement {
            Ok(0.0) // 低风险
        } else {
            let ratio = total_depth / self.assessment_params.min_depth_requirement;
            Ok(1.0 - ratio.to_string().parse::<f64>().unwrap_or(0.0).min(1.0))
        }
    }

    /// 计算市场冲击成本
    fn calculate_market_impact_cost(&self) -> SigmaXResult<f64> {
        let mut total_impact = 0.0;
        let mut count = 0;

        for data in self.liquidity_data.values() {
            if let Some(latest_depth) = data.depth_history.back() {
                // 简化的市场冲击模型：基于价差和深度
                let spread = latest_depth.best_ask - latest_depth.best_bid;
                let mid_price = (latest_depth.best_ask + latest_depth.best_bid) / Decimal::from(2);
                
                let spread_ratio = if mid_price > Decimal::ZERO {
                    spread / mid_price
                } else {
                    Decimal::ZERO
                };
                
                // 市场冲击成本 = 价差的一半 + 深度影响
                let depth_impact = self.calculate_depth_impact(latest_depth)?;
                let impact_cost = spread_ratio.to_string().parse::<f64>().unwrap_or(0.0) / 2.0 + depth_impact;
                
                total_impact += impact_cost;
                count += 1;
            }
        }

        if count > 0 {
            Ok(total_impact / count as f64)
        } else {
            Ok(0.0)
        }
    }

    /// 计算深度影响
    fn calculate_depth_impact(&self, depth: &MarketDepth) -> SigmaXResult<f64> {
        // 简化模型：深度越浅，冲击越大
        let total_depth_value: Decimal = depth.bids.iter().chain(depth.asks.iter())
            .take(5) // 取前5档
            .map(|(price, quantity)| price * quantity)
            .sum();
        
        let depth_impact = if total_depth_value > Decimal::ZERO {
            let depth_f64 = total_depth_value.to_string().parse::<f64>().unwrap_or(0.0);
            // 深度影响与深度成反比
            1.0 / (1.0 + depth_f64 / 10000.0) // 标准化因子
        } else {
            1.0 // 最大影响
        };
        
        Ok(depth_impact * 0.01) // 转换为百分比
    }

    /// 计算买卖价差风险
    fn calculate_bid_ask_spread_risk(&self) -> SigmaXResult<f64> {
        let mut total_spread_risk = 0.0;
        let mut count = 0;

        for data in self.liquidity_data.values() {
            if !data.spread_history.is_empty() {
                let avg_spread = data.spread_history.iter().sum::<Decimal>() / Decimal::from(data.spread_history.len());
                let spread_risk = (avg_spread / self.assessment_params.max_spread_threshold)
                    .to_string().parse::<f64>().unwrap_or(0.0).min(1.0);
                
                total_spread_risk += spread_risk;
                count += 1;
            }
        }

        if count > 0 {
            Ok(total_spread_risk / count as f64)
        } else {
            Ok(0.0)
        }
    }

    /// 计算成交量充足性评分
    fn calculate_volume_adequacy_score(&self) -> SigmaXResult<f64> {
        let mut total_adequacy = 0.0;
        let mut count = 0;

        for data in self.liquidity_data.values() {
            if !data.volume_history.is_empty() {
                let avg_volume = data.volume_history.iter().sum::<Amount>() / Decimal::from(data.volume_history.len());
                let adequacy = (avg_volume / self.assessment_params.min_volume_threshold)
                    .to_string().parse::<f64>().unwrap_or(0.0).min(1.0);
                
                total_adequacy += adequacy;
                count += 1;
            }
        }

        if count > 0 {
            Ok(total_adequacy / count as f64)
        } else {
            Ok(0.0)
        }
    }

    /// 计算流动性缓冲区建议
    fn calculate_liquidity_buffer_recommendation(&self) -> SigmaXResult<f64> {
        // 基于整体流动性风险调整缓冲区建议
        let base_buffer = self.assessment_params.liquidity_buffer_ratio;
        
        // 计算平均流动性风险
        let mut total_risk = 0.0;
        let mut count = 0;

        for data in self.liquidity_data.values() {
            if let Ok(score) = self.calculate_asset_liquidity_score(data) {
                total_risk += score;
                count += 1;
            }
        }

        let avg_risk = if count > 0 {
            total_risk / count as f64
        } else {
            0.5
        };

        // 风险越高，建议的缓冲区越大
        let adjusted_buffer = base_buffer * (1.0 + avg_risk);
        
        Ok(adjusted_buffer.min(0.5)) // 最大50%缓冲区
    }

    /// 估算订单的市场冲击
    pub fn estimate_order_impact(
        &self,
        trading_pair: &TradingPair,
        side: OrderSide,
        quantity: Amount,
    ) -> SigmaXResult<f64> {
        let asset_key = format!("{}_{}", trading_pair.base, trading_pair.quote);
        
        let data = self.liquidity_data.get(&asset_key)
            .ok_or_else(|| SigmaXError::InvalidOperation(
                format!("资产 {} 的流动性数据不存在", asset_key)
            ))?;
        
        if let Some(latest_depth) = data.depth_history.back() {
            let relevant_orders = match side {
                OrderSide::Buy => &latest_depth.asks,
                OrderSide::Sell => &latest_depth.bids,
            };
            
            // 计算执行订单需要消耗的深度
            let mut remaining_quantity = quantity;
            let mut total_cost = Decimal::ZERO;
            
            for (price, available_quantity) in relevant_orders {
                if remaining_quantity <= Decimal::ZERO {
                    break;
                }
                
                let consumed_quantity = remaining_quantity.min(*available_quantity);
                total_cost += consumed_quantity * price;
                remaining_quantity -= consumed_quantity;
            }
            
            if remaining_quantity > Decimal::ZERO {
                // 订单无法完全执行，高冲击
                return Ok(0.1); // 10%冲击
            }
            
            // 计算平均执行价格与中间价的偏差
            let avg_execution_price = if quantity > Decimal::ZERO {
                total_cost / quantity
            } else {
                Decimal::ZERO
            };
            
            let mid_price = (latest_depth.best_bid + latest_depth.best_ask) / Decimal::from(2);
            
            let impact = if mid_price > Decimal::ZERO {
                ((avg_execution_price - mid_price).abs() / mid_price)
                    .to_string().parse::<f64>().unwrap_or(0.0)
            } else {
                0.0
            };
            
            Ok(impact)
        } else {
            Ok(0.05) // 默认5%冲击
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;

    #[test]
    fn test_liquidity_risk_calculation() {
        let mut calculator = LiquidityRiskCalculator::new(50);
        
        let btc_usdt = TradingPair::new("BTC".to_string(), "USDT".to_string());
        calculator.add_asset(btc_usdt.clone());
        
        // 模拟市场深度
        let market_depth = MarketDepth {
            bids: vec![(dec!(50000), dec!(1.0)), (dec!(49990), dec!(2.0))],
            asks: vec![(dec!(50010), dec!(1.5)), (dec!(50020), dec!(2.5))],
            best_bid: dec!(50000),
            best_ask: dec!(50010),
            timestamp: Utc::now(),
        };
        
        calculator.update_market_data(
            &btc_usdt,
            dec!(1000000), // 100万USDT日成交量
            dec!(0.002),   // 0.2%价差
            market_depth,
        ).unwrap();
        
        let metrics = calculator.calculate_liquidity_risk().unwrap();
        
        assert!(metrics.overall_liquidity_risk >= 0.0);
        assert!(metrics.overall_liquidity_risk <= 1.0);
        assert!(metrics.market_impact_cost >= 0.0);
        assert!(metrics.volume_adequacy_score >= 0.0);
    }
}
