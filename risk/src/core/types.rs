//! 风险管理核心类型定义

use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use sigmax_core::{Order, Balance, SigmaXResult};

/// 风险检查类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum RiskCheckType {
    /// 订单风险检查
    Order,
    /// 持仓风险检查
    Position,
    /// 投资组合风险检查
    Portfolio,
    /// 系统风险检查
    System,
}

/// 风险等级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, PartialOrd)]
pub enum RiskLevel {
    /// 低风险
    Low,
    /// 中等风险
    Medium,
    /// 高风险
    High,
    /// 极高风险
    Critical,
}

/// 风险检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCheckResult {
    /// 是否通过检查
    pub passed: bool,
    /// 风险等级
    pub risk_level: RiskLevel,
    /// 风险评分 (0.0-1.0)
    pub risk_score: f64,
    /// 检查类型
    pub check_type: RiskCheckType,
    /// 结果消息
    pub message: Option<String>,
    /// 警告信息
    pub warnings: Vec<String>,
    /// 建议信息
    pub suggestions: Vec<String>,
    /// 违反的规则
    pub violated_rules: Vec<String>,
    /// 检查时间
    pub checked_at: DateTime<Utc>,
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
}

impl RiskCheckResult {
    /// 创建通过检查的结果
    pub fn passed(check_type: RiskCheckType) -> Self {
        let now = Utc::now();
        Self {
            passed: true,
            risk_level: RiskLevel::Low,
            risk_score: 0.0,
            check_type,
            message: Some("风险检查通过".to_string()),
            warnings: vec![],
            suggestions: vec![],
            violated_rules: vec![],
            checked_at: now,
            timestamp: now,
            execution_time_ms: 0,
        }
    }

    /// 创建失败检查的结果
    pub fn failed(check_type: RiskCheckType, risk_level: RiskLevel, message: String) -> Self {
        let now = Utc::now();
        Self {
            passed: false,
            risk_level,
            risk_score: 1.0,
            check_type,
            message: Some(message),
            warnings: vec![],
            suggestions: vec![],
            violated_rules: vec![],
            checked_at: now,
            timestamp: now,
            execution_time_ms: 0,
        }
    }
}

/// 核心风险评估结果
///
/// 重命名以避免与 unified_engine::RiskCheckResult 冲突
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CoreRiskAssessment {
    /// 是否通过检查
    pub passed: bool,
    /// 风险等级
    pub risk_level: RiskLevel,
    /// 风险评分 (0.0-1.0)
    pub risk_score: f64,
    /// 检查类型
    pub check_type: RiskCheckType,
    /// 结果消息
    pub message: Option<String>,
    /// 警告信息
    pub warnings: Vec<String>,
    /// 建议信息
    pub suggestions: Vec<String>,
    /// 违反的规则
    pub violated_rules: Vec<String>,
    /// 检查时间戳
    pub timestamp: DateTime<Utc>,
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
}

impl CoreRiskAssessment {
    /// 创建通过的检查结果
    pub fn passed(check_type: RiskCheckType) -> Self {
        Self {
            passed: true,
            risk_level: RiskLevel::Low,
            risk_score: 0.0,
            check_type,
            message: Some("风险检查通过".to_string()),
            warnings: Vec::new(),
            suggestions: Vec::new(),
            violated_rules: Vec::new(),
            timestamp: Utc::now(),
            execution_time_ms: 0,
        }
    }

    /// 创建失败的检查结果
    pub fn failed(check_type: RiskCheckType, reason: String) -> Self {
        Self {
            passed: false,
            risk_level: RiskLevel::High,
            risk_score: 1.0,
            check_type,
            message: Some(reason.clone()),
            warnings: Vec::new(),
            suggestions: Vec::new(),
            violated_rules: vec![reason],
            timestamp: Utc::now(),
            execution_time_ms: 0,
        }
    }
}

/// 风险检查请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCheckRequest {
    /// 检查类型
    pub check_type: RiskCheckType,
    /// 订单信息（订单检查时使用）
    pub order: Option<Order>,
    /// 持仓信息（持仓检查时使用）
    pub balances: Option<Vec<Balance>>,
    /// 投资组合信息（投资组合检查时使用）
    pub portfolio: Option<PortfolioInfo>,
    /// 策略类型
    pub strategy_type: Option<String>,
    /// 额外参数
    pub parameters: HashMap<String, serde_json::Value>,
}

/// 投资组合信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioInfo {
    /// 投资组合ID
    pub id: String,
    /// 总价值
    pub total_value: f64,
    /// 持仓信息
    pub positions: HashMap<String, f64>,
    /// 日损益
    pub daily_pnl: f64,
    /// 最大回撤
    pub max_drawdown: f64,
}

/// 风险规则定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskRule {
    /// 规则ID
    pub id: Uuid,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: Option<String>,
    /// 规则类型
    pub rule_type: String,
    /// 规则参数
    pub parameters: HashMap<String, serde_json::Value>,
    /// 是否启用
    pub enabled: bool,
    /// 优先级
    pub priority: i32,
    /// 适用的策略类型
    pub strategy_types: Vec<String>,
    /// 适用的交易对
    pub trading_pairs: Vec<String>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 风险指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMetrics {
    /// VaR (95%)
    pub var_95: f64,
    /// VaR (99%)
    pub var_99: f64,
    /// CVaR (95%)
    pub cvar_95: f64,
    /// CVaR (99%)
    pub cvar_99: f64,
    /// 波动率
    pub volatility: f64,
    /// 最大回撤
    pub max_drawdown: f64,
    /// 夏普比率
    pub sharpe_ratio: f64,
    /// 贝塔系数
    pub beta: Option<f64>,
    /// 计算时间
    pub timestamp: DateTime<Utc>,
}

impl Default for RiskMetrics {
    fn default() -> Self {
        Self {
            var_95: 0.0,
            var_99: 0.0,
            cvar_95: 0.0,
            cvar_99: 0.0,
            volatility: 0.0,
            max_drawdown: 0.0,
            sharpe_ratio: 0.0,
            beta: None,
            timestamp: chrono::Utc::now(),
        }
    }
}

/// 风险预警
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAlert {
    /// 预警ID
    pub id: Uuid,
    /// 预警类型
    pub alert_type: String,
    /// 严重程度
    pub severity: RiskLevel,
    /// 预警消息
    pub message: String,
    /// 相关数据
    pub data: HashMap<String, serde_json::Value>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 是否已处理
    pub resolved: bool,
}

// 风险配置参数已移至 sigmax_core::config::risk 模块
// 使用 sigmax_core::config::risk::RiskManagementConfig 和相关类型
