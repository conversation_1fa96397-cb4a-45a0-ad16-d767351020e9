//! 引擎风控集成中心 - 重构版
//!
//! 统一的风控服务管理，符合设计原则：
//! - 高内聚，低耦合：风控集成逻辑集中
//! - 关注点分离：路由、工厂、容器职责分离
//! - 面向接口设计：统一的适配器接口
//! - 可测试性设计：依赖注入和Mock支持
//! - 简洁与可演化性：可插拔的适配器实现
//!
//! ## 重构后的架构组件
//!
//! ### 核心组件
//! - `RiskRouter`: 纯路由分发逻辑，无业务逻辑
//! - `AdapterFactory`: 适配器工厂，依赖注入管理
//! - `RiskServiceContainer`: IoC容器，服务生命周期管理
//! - `AdapterBase`: 适配器基础类，通用逻辑
//!
//! ### 适配器实现 (将在各引擎子模块中实现)
//! - `BacktestRiskAdapter`: 回测引擎专用，高吞吐量优化
//! - `LiveTradingRiskAdapter`: 实盘引擎专用，低延迟优化
//! - `WebApiRiskAdapter`: Web API专用，详细结果输出
//! - `StrategyRiskAdapter`: 策略模块专用，策略切换评估
//!
//! ## 设计原则体现
//!
//! 1. **单一职责**: 每个组件只负责一个核心功能
//! 2. **依赖倒置**: 依赖抽象接口而非具体实现
//! 3. **开闭原则**: 对扩展开放，对修改封闭
//! 4. **接口隔离**: 不同使用场景有专门的接口

// ============================================================================
// 🔧 核心组件模块
// ============================================================================

/// 路由器 - 纯路由分发逻辑
pub mod router;

/// 工厂 - 适配器创建和依赖注入
pub mod factory;

/// 容器 - IoC容器和服务管理
pub mod container;

/// 适配器 - 基础类和trait定义
pub mod adapters;

/// 缓存服务 - 缓存实现
pub mod cache;

/// 配置服务 - 配置管理
pub mod config;

/// 风控引擎 - 核心风控逻辑
pub mod engine;

/// 统一门面 - RiskServiceFacade
pub mod facade;

/// 指标 - 风控适配器指标
pub mod metrics;

/// 数据访问层 - 统一风控数据访问
pub mod repository;

// ============================================================================
// 🔄 向后兼容模块
// ============================================================================

/// 集成 - 现有集成逻辑 (重构版)
// pub mod integration; // 暂时注释掉，等待实现

// ============================================================================
// 📤 公共导出
// ============================================================================

// 核心组件导出
pub use router::RiskRouter;
pub use factory::AdapterFactory;
pub use container::RiskServiceContainer;
pub use adapters::{AdapterBase, AdapterConfig, AdapterMetrics};
pub use sigmax_core::EngineRiskAdapter;
pub use cache::{CacheService, MemoryCacheService, CacheConfig, RiskCacheService, RiskCacheStrategy, RiskCacheServiceConfig};
pub use config::{ConfigService, MemoryConfigService, ConfigChangeEvent};
pub use engine::{RiskEngine, BasicRiskEngine, RiskEngineConfig, RiskRule};
pub use facade::{RiskServiceFacade, FacadeConfig, RiskCheckMode, FacadeStats};
pub use metrics::{MetricsCollector, MemoryMetricsCollector, UnifiedRiskMetricsCollector, UnifiedMetricsConfig, AlertRule, Alert, AlertSeverity};
pub use repository::{UnifiedRiskRepository, SqlxUnifiedRiskRepository, RepositoryConfig, UnifiedRiskRule, RiskRuleExecution, SystemConfig, RiskStatistics};
// pub use integration::RiskIntegration; // 暂时注释掉，等待实现

// 导出类型将在结构体定义后进行

// 注意：core::traits暂时注释掉，等待core模块定义
// pub use core::traits::{
//     RiskEngine,
//     EngineRiskAdapter as EngineRiskAdapterTrait,
//     RiskManager,
//     CacheService,
//     MetricsCollector
// };

// ============================================================================
// 🔄 向后兼容导出 (逐步迁移)
// ============================================================================

// 重新导出现有的风控集成组件以保持兼容性
pub use crate::risk_integration::*;
pub use crate::unified_risk_service::*;

// ============================================================================
// 🎯 风控相关类型定义
// ============================================================================

use async_trait::async_trait;
use sigmax_core::{EngineType, SigmaXResult, SigmaXError, Order, Balance};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use std::sync::Arc;
use std::collections::HashMap;
use tracing::{info, debug};

/// 风控模式枚举
#[derive(Debug, Clone, PartialEq)]
pub enum RiskMode {
    /// 回测模式 - 高吞吐量，批量缓存
    Backtest,
    /// 实盘模式 - 低延迟，热缓存
    LiveTrading,
    /// Web API模式 - 详细结果，查询缓存
    WebApi,
    /// 策略模式 - 策略专用，切换评估
    Strategy,
}

impl From<EngineType> for RiskMode {
    fn from(engine_type: EngineType) -> Self {
        match engine_type {
            EngineType::Backtest => RiskMode::Backtest,
            EngineType::Live => RiskMode::LiveTrading,
            EngineType::Paper => RiskMode::LiveTrading,
            EngineType::Simulation => RiskMode::LiveTrading, // 模拟交易使用实盘模式
        }
    }
}

/// 风控检查请求
#[derive(Debug, Clone)]
pub struct RiskCheckRequest {
    pub order: Order,
    pub engine_type: EngineType,
    pub strategy_type: Option<String>,
    pub context: Option<RiskCheckContext>,
}

/// 风控检查上下文
#[derive(Debug, Clone)]
pub struct RiskCheckContext {
    pub balances: Option<Vec<Balance>>,
    // 注意：Position和MarketData类型暂时注释掉，等待core模块定义
    // pub current_positions: Option<Vec<Position>>,
    // pub market_data: Option<MarketData>,
    pub strategy_state: Option<serde_json::Value>,
}

impl RiskCheckRequest {
    pub fn new(order: Order, engine_type: EngineType) -> Self {
        Self {
            order,
            engine_type,
            strategy_type: None,
            context: None,
        }
    }

    pub fn with_strategy_type(mut self, strategy_type: String) -> Self {
        self.strategy_type = Some(strategy_type);
        self
    }

    pub fn with_context(mut self, context: RiskCheckContext) -> Self {
        self.context = Some(context);
        self
    }
}

/// 风控服务配置
#[derive(Debug, Clone)]
pub struct RiskServiceConfig {
    /// 启用缓存
    pub enable_cache: bool,
    /// 缓存TTL (秒)
    pub cache_ttl_secs: u64,
    /// 启用指标收集
    pub enable_metrics: bool,
    /// 批量处理大小 (回测模式)
    pub batch_size: usize,
    /// 熔断器阈值 (实盘模式)
    pub circuit_breaker_threshold: f64,
    /// 超时时间 (毫秒)
    pub timeout_ms: u64,
}

impl Default for RiskServiceConfig {
    fn default() -> Self {
        Self {
            enable_cache: true,
            cache_ttl_secs: 60,
            enable_metrics: true,
            batch_size: 100,
            circuit_breaker_threshold: 0.1, // 10%错误率
            timeout_ms: 5000, // 5秒
        }
    }
}

/// 风控服务构建器
pub struct RiskServiceBuilder {
    config: RiskServiceConfig,
    risk_engine: Option<Arc<dyn RiskEngine>>,
    cache_service: Option<Arc<dyn CacheService>>,
    metrics_collector: Option<Arc<dyn MetricsCollector>>,
}

impl RiskServiceBuilder {
    pub fn new() -> Self {
        Self {
            config: RiskServiceConfig::default(),
            risk_engine: None,
            cache_service: None,
            metrics_collector: None,
        }
    }

    pub fn with_config(mut self, config: RiskServiceConfig) -> Self {
        self.config = config;
        self
    }

    pub fn with_risk_engine(mut self, engine: Arc<dyn RiskEngine>) -> Self {
        self.risk_engine = Some(engine);
        self
    }

    pub fn with_cache_service(mut self, cache: Arc<dyn CacheService>) -> Self {
        self.cache_service = Some(cache);
        self
    }

    pub fn with_metrics_collector(mut self, metrics: Arc<dyn MetricsCollector>) -> Self {
        self.metrics_collector = Some(metrics);
        self
    }

    pub async fn build(self) -> SigmaXResult<RiskServiceContainer> {
        let risk_engine = self.risk_engine
            .ok_or_else(|| SigmaXError::Configuration("Risk engine is required".to_string()))?;

        let cache_service = self.cache_service
            .ok_or_else(|| SigmaXError::Configuration("Cache service is required".to_string()))?;

        let metrics_collector = self.metrics_collector
            .ok_or_else(|| SigmaXError::Configuration("Metrics collector is required".to_string()))?;

        RiskServiceContainer::new(
            risk_engine,
            cache_service,
            metrics_collector,
            self.config,
        ).await
    }
}

impl Default for RiskServiceBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 风控服务初始化
pub async fn init_risk_services() -> SigmaXResult<()> {
    tracing::info!("Initializing engine risk services");

    // 这里可以添加全局初始化逻辑
    // 例如：注册默认的适配器、设置全局配置等

    tracing::info!("Engine risk services initialized successfully");
    Ok(())
}

/// 投资组合快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioSnapshot {
    pub timestamp: DateTime<Utc>,
    pub balances: Vec<Balance>,
    pub total_value: rust_decimal::Decimal,
    pub engine_id: uuid::Uuid,
}
