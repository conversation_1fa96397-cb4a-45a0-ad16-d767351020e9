//! 风险检查集成模块
//!
//! 提供统一的风险检查机制，适用于回测和实时交易

use async_trait::async_trait;
use sigmax_core::{Order, RiskManager, SigmaXResult, Balance, PortfolioManager};
use std::sync::Arc;
use tracing::{debug, warn, info};

/// 风险检查类型
#[derive(Debug, Clone)]
pub enum RiskCheckType {
    /// 策略订单生成时的预检查
    StrategyOrderGeneration(String), // 策略名称
    /// 订单执行前的检查
    OrderExecution,
    /// 持仓风险检查
    PositionRisk,
    /// 系统级风险检查
    SystemRisk,
}

impl RiskCheckType {
    pub fn description(&self) -> String {
        match self {
            RiskCheckType::StrategyOrderGeneration(strategy_name) => {
                format!("策略{}订单生成", strategy_name)
            }
            RiskCheckType::OrderExecution => "订单执行".to_string(),
            RiskCheckType::PositionRisk => "持仓风险".to_string(),
            RiskCheckType::SystemRisk => "系统风险".to_string(),
        }
    }
}

/// 风险检查结果
#[derive(Debug, Clone)]
pub struct RiskCheckResult {
    pub passed: bool,
    pub check_type: RiskCheckType,
    pub message: Option<String>,
    pub risk_score: Option<f64>, // 0.0-1.0，1.0为最高风险
}

/// 统一风险检查器
pub struct UnifiedRiskChecker {
    risk_manager: Option<Arc<dyn RiskManager>>,
    portfolio_manager: Option<Arc<dyn PortfolioManager>>,
    /// 是否为回测模式
    is_backtest: bool,
    /// 风险检查统计
    check_stats: std::sync::Mutex<RiskCheckStats>,
}

/// 风险检查统计
#[derive(Debug, Default, Clone)]
pub struct RiskCheckStats {
    pub total_checks: u64,
    pub passed_checks: u64,
    pub failed_checks: u64,
    pub strategy_rejections: u64,
    pub execution_rejections: u64,
    pub position_warnings: u64,
}

impl UnifiedRiskChecker {
    /// 创建新的统一风险检查器
    pub fn new(
        risk_manager: Option<Arc<dyn RiskManager>>,
        portfolio_manager: Option<Arc<dyn PortfolioManager>>,
        is_backtest: bool,
    ) -> Self {
        Self {
            risk_manager,
            portfolio_manager,
            is_backtest,
            check_stats: std::sync::Mutex::new(RiskCheckStats::default()),
        }
    }

    /// 检查订单风险
    pub async fn check_order_risk(&self, order: &Order, check_type: RiskCheckType) -> SigmaXResult<RiskCheckResult> {
        let check_description = check_type.description();

        // 更新统计
        {
            let mut stats = self.check_stats.lock().unwrap();
            stats.total_checks += 1;
        }

        if let Some(risk_manager) = &self.risk_manager {
            match risk_manager.check_order_risk(order).await {
                Ok(risk_passed) => {
                    // 更新统计
                    {
                        let mut stats = self.check_stats.lock().unwrap();
                        if risk_passed {
                            stats.passed_checks += 1;
                        } else {
                            stats.failed_checks += 1;
                            match &check_type {
                                RiskCheckType::StrategyOrderGeneration(_) => stats.strategy_rejections += 1,
                                RiskCheckType::OrderExecution => stats.execution_rejections += 1,
                                _ => {}
                            }
                        }
                    }

                    if risk_passed {
                        debug!("{}风险检查通过: {:?} {} @ {:?}",
                               check_description, order.side, order.quantity, order.price);
                    } else {
                        warn!("{}风险检查失败: {:?} {} @ {:?}",
                              check_description, order.side, order.quantity, order.price);
                    }

                    Ok(RiskCheckResult {
                        passed: risk_passed,
                        check_type,
                        message: if risk_passed { None } else { Some("订单未通过风险检查".to_string()) },
                        risk_score: None, // 可以在未来扩展
                    })
                }
                Err(e) => {
                    warn!("{}风险检查过程中发生错误: {}", check_description, e);

                    // 更新统计
                    {
                        let mut stats = self.check_stats.lock().unwrap();
                        stats.failed_checks += 1;
                    }

                    Ok(RiskCheckResult {
                        passed: false,
                        check_type,
                        message: Some(format!("风险检查错误: {}", e)),
                        risk_score: Some(1.0), // 错误时设为最高风险
                    })
                }
            }
        } else {
            // 如果没有风险管理器，默认通过（向后兼容）
            debug!("未配置风险管理器，{}风险检查默认通过", check_description);

            // 更新统计
            {
                let mut stats = self.check_stats.lock().unwrap();
                stats.passed_checks += 1;
            }

            Ok(RiskCheckResult {
                passed: true,
                check_type,
                message: Some("未配置风险管理器，默认通过".to_string()),
                risk_score: Some(0.0),
            })
        }
    }

    /// 检查持仓风险
    pub async fn check_position_risk(&self) -> SigmaXResult<RiskCheckResult> {
        let check_type = RiskCheckType::PositionRisk;

        // 更新统计
        {
            let mut stats = self.check_stats.lock().unwrap();
            stats.total_checks += 1;
        }

        if let Some(risk_manager) = &self.risk_manager {
            if let Some(portfolio_manager) = &self.portfolio_manager {
                match portfolio_manager.get_balances().await {
                    Ok(balances) => {
                        let balances_vec: Vec<_> = balances.values().cloned().collect();
                        match risk_manager.check_position_risk(&balances_vec).await {
                            Ok(risk_passed) => {
                                // 更新统计
                                {
                                    let mut stats = self.check_stats.lock().unwrap();
                                    if risk_passed {
                                        stats.passed_checks += 1;
                                    } else {
                                        stats.failed_checks += 1;
                                        stats.position_warnings += 1;
                                    }
                                }

                                if !risk_passed {
                                    warn!("投资组合风险检查失败，当前持仓超出风险限制");
                                } else {
                                    debug!("投资组合风险检查通过");
                                }

                                Ok(RiskCheckResult {
                                    passed: risk_passed,
                                    check_type,
                                    message: if risk_passed { None } else { Some("持仓超出风险限制".to_string()) },
                                    risk_score: None,
                                })
                            }
                            Err(e) => {
                                warn!("投资组合风险检查过程中发生错误: {}", e);

                                // 更新统计
                                {
                                    let mut stats = self.check_stats.lock().unwrap();
                                    stats.failed_checks += 1;
                                }

                                Ok(RiskCheckResult {
                                    passed: false,
                                    check_type,
                                    message: Some(format!("持仓风险检查错误: {}", e)),
                                    risk_score: Some(1.0),
                                })
                            }
                        }
                    }
                    Err(e) => {
                        warn!("获取投资组合余额失败: {}", e);

                        // 更新统计
                        {
                            let mut stats = self.check_stats.lock().unwrap();
                            stats.failed_checks += 1;
                        }

                        Ok(RiskCheckResult {
                            passed: false,
                            check_type,
                            message: Some(format!("获取余额失败: {}", e)),
                            risk_score: Some(1.0),
                        })
                    }
                }
            } else {
                debug!("未配置投资组合管理器，跳过持仓风险检查");

                // 更新统计
                {
                    let mut stats = self.check_stats.lock().unwrap();
                    stats.passed_checks += 1;
                }

                Ok(RiskCheckResult {
                    passed: true,
                    check_type,
                    message: Some("未配置投资组合管理器".to_string()),
                    risk_score: Some(0.0),
                })
            }
        } else {
            debug!("未配置风险管理器，跳过持仓风险检查");

            // 更新统计
            {
                let mut stats = self.check_stats.lock().unwrap();
                stats.passed_checks += 1;
            }

            Ok(RiskCheckResult {
                passed: true,
                check_type,
                message: Some("未配置风险管理器".to_string()),
                risk_score: Some(0.0),
            })
        }
    }

    /// 获取风险检查统计信息
    pub fn get_risk_check_stats(&self) -> RiskCheckStats {
        let stats = self.check_stats.lock().unwrap();
        RiskCheckStats {
            total_checks: stats.total_checks,
            passed_checks: stats.passed_checks,
            failed_checks: stats.failed_checks,
            strategy_rejections: stats.strategy_rejections,
            execution_rejections: stats.execution_rejections,
            position_warnings: stats.position_warnings,
        }
    }

    /// 重置统计信息
    pub fn reset_stats(&self) {
        let mut stats = self.check_stats.lock().unwrap();
        *stats = RiskCheckStats::default();
    }

    /// 是否为回测模式
    pub fn is_backtest(&self) -> bool {
        self.is_backtest
    }

    /// 打印风险检查统计报告
    pub fn print_risk_report(&self) {
        let stats = self.get_risk_check_stats();
        let success_rate = if stats.total_checks > 0 {
            (stats.passed_checks as f64 / stats.total_checks as f64) * 100.0
        } else {
            0.0
        };

        info!("=== 风险检查统计报告 ===");
        info!("模式: {}", if self.is_backtest { "回测" } else { "实时交易" });
        info!("总检查次数: {}", stats.total_checks);
        info!("通过次数: {}", stats.passed_checks);
        info!("失败次数: {}", stats.failed_checks);
        info!("成功率: {:.2}%", success_rate);
        info!("策略订单拒绝: {}", stats.strategy_rejections);
        info!("执行订单拒绝: {}", stats.execution_rejections);
        info!("持仓风险警告: {}", stats.position_warnings);
        info!("========================");
    }
}

/// 风险检查集成特征
#[async_trait]
pub trait RiskCheckIntegration {
    /// 执行订单风险检查
    async fn check_order(&self, order: &Order, check_type: RiskCheckType) -> SigmaXResult<bool>;

    /// 执行持仓风险检查
    async fn check_position(&self) -> SigmaXResult<bool>;

    /// 获取风险检查器
    fn get_risk_checker(&self) -> Option<&UnifiedRiskChecker>;
}
