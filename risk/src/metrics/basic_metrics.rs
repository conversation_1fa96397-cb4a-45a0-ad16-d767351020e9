//! 基础风险指标计算模块
//! 
//! 实现基础的风险度量指标，包括VaR、CVaR、波动率等

use sigmax_core::{Amount, SigmaXResult};
use rust_decimal::{Decimal, MathematicalOps};
use std::collections::VecDeque;
use serde::{Serialize, Deserialize};

/// 基础风险指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BasicRiskMetrics {
    /// VaR 95%
    pub var_95: Amount,
    /// VaR 99%
    pub var_99: Amount,
    /// CVaR 95%
    pub cvar_95: Amount,
    /// CVaR 99%
    pub cvar_99: Amount,
    /// 波动率
    pub volatility: Decimal,
    /// 下行波动率
    pub downside_volatility: Decimal,
    /// 上行波动率
    pub upside_volatility: Decimal,
    /// 波动率偏度
    pub volatility_skew: Decimal,
}

/// 基础风险指标计算器
pub struct BasicRiskCalculator<'a> {
    returns_history: &'a VecDeque<Decimal>,
    current_portfolio_value: Amount,
    risk_free_rate: Decimal,
}

impl<'a> BasicRiskCalculator<'a> {
    pub fn new(
        returns_history: &'a VecDeque<Decimal>,
        current_portfolio_value: Amount,
        risk_free_rate: Decimal,
    ) -> Self {
        Self {
            returns_history,
            current_portfolio_value,
            risk_free_rate,
        }
    }

    /// 计算基础风险指标
    pub fn calculate(&self) -> SigmaXResult<BasicRiskMetrics> {
        Ok(BasicRiskMetrics {
            var_95: self.calculate_var(0.05)?,
            var_99: self.calculate_var(0.01)?,
            cvar_95: self.calculate_cvar(0.05)?,
            cvar_99: self.calculate_cvar(0.01)?,
            volatility: self.calculate_volatility()?,
            downside_volatility: self.calculate_downside_volatility()?,
            upside_volatility: self.calculate_upside_volatility()?,
            volatility_skew: self.calculate_volatility_skew()?,
        })
    }

    /// 计算VaR
    pub fn calculate_var(&self, alpha: f64) -> SigmaXResult<Amount> {
        if self.returns_history.is_empty() {
            return Ok(Amount::ZERO);
        }

        let mut sorted_returns: Vec<Decimal> = self.returns_history.iter().cloned().collect();
        sorted_returns.sort();
        
        let index = (alpha * sorted_returns.len() as f64) as usize;
        let var_return = sorted_returns.get(index).copied().unwrap_or(Decimal::ZERO);
        
        Ok(self.current_portfolio_value * var_return.abs())
    }

    /// 计算CVaR
    pub fn calculate_cvar(&self, alpha: f64) -> SigmaXResult<Amount> {
        if self.returns_history.is_empty() {
            return Ok(Amount::ZERO);
        }

        let mut losses: Vec<Decimal> = self.returns_history
            .iter()
            .map(|r| -r)
            .collect();
        
        losses.sort();
        
        let var_index = ((1.0 - alpha) * losses.len() as f64) as usize;
        
        if var_index >= losses.len() {
            return Ok(Amount::ZERO);
        }
        
        let tail_losses: Vec<Decimal> = losses[var_index..].to_vec();
        
        if tail_losses.is_empty() {
            return Ok(Amount::ZERO);
        }
        
        let cvar_rate = tail_losses.iter().sum::<Decimal>() / Decimal::from(tail_losses.len());
        
        Ok(self.current_portfolio_value * cvar_rate)
    }

    /// 计算波动率
    pub fn calculate_volatility(&self) -> SigmaXResult<Decimal> {
        if self.returns_history.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let mean = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        
        let variance = self.returns_history
            .iter()
            .map(|r| (r - mean).powi(2))
            .sum::<Decimal>() / Decimal::from(self.returns_history.len() - 1);
        
        Ok(variance.sqrt().unwrap_or(Decimal::ZERO))
    }

    /// 计算下行波动率
    pub fn calculate_downside_volatility(&self) -> SigmaXResult<Decimal> {
        if self.returns_history.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let target_return = self.risk_free_rate / Decimal::from(252); // 日化无风险利率
        
        let downside_returns: Vec<Decimal> = self.returns_history
            .iter()
            .filter(|&&r| r < target_return)
            .map(|&r| (r - target_return).powi(2))
            .collect();

        if downside_returns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let downside_variance = downside_returns.iter().sum::<Decimal>() / Decimal::from(downside_returns.len());
        
        Ok(downside_variance.sqrt().unwrap_or(Decimal::ZERO))
    }

    /// 计算上行波动率
    pub fn calculate_upside_volatility(&self) -> SigmaXResult<Decimal> {
        if self.returns_history.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let target_return = self.risk_free_rate / Decimal::from(252);
        
        let upside_returns: Vec<Decimal> = self.returns_history
            .iter()
            .filter(|&&r| r > target_return)
            .map(|&r| (r - target_return).powi(2))
            .collect();

        if upside_returns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let upside_variance = upside_returns.iter().sum::<Decimal>() / Decimal::from(upside_returns.len());
        
        Ok(upside_variance.sqrt().unwrap_or(Decimal::ZERO))
    }

    /// 计算波动率偏度
    pub fn calculate_volatility_skew(&self) -> SigmaXResult<Decimal> {
        let downside_vol = self.calculate_downside_volatility()?;
        let upside_vol = self.calculate_upside_volatility()?;
        
        if upside_vol == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        Ok(downside_vol / upside_vol)
    }
}
