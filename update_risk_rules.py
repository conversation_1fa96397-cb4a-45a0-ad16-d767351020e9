#!/usr/bin/env python3
"""
更新风控规则配置的脚本
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8080"
API_BASE = f"{BASE_URL}/api/v1"

def get_risk_rules():
    """获取所有风控规则"""
    print("🔍 Getting all risk rules...")

    response = requests.get(f"{BASE_URL}/api/risk/rules")
    if response.status_code == 200:
        data = response.json()
        rules = data['data']
        print(f"Found {len(rules)} risk rules")
        
        for rule in rules:
            print(f"  - {rule['name']} (ID: {rule['id']}) - Enabled: {rule['enabled']}")
            if rule['name'] == '单个持仓大小限制':
                print(f"    Current parameters: {rule['parameters']}")
        
        return rules
    else:
        print(f"❌ Failed to get rules: {response.status_code}")
        print(f"Error: {response.text}")
        return []

def update_position_limit_rule():
    """更新单个持仓大小限制规则"""
    print("\n🔧 Updating position limit rule...")
    
    # 首先获取规则ID
    rules = get_risk_rules()
    position_rule = None
    for rule in rules:
        if rule['name'] == '单个持仓大小限制':
            position_rule = rule
            break
    
    if not position_rule:
        print("❌ Position limit rule not found")
        return False
    
    rule_id = position_rule['id']
    print(f"Found position limit rule ID: {rule_id}")
    
    # 更新规则参数 - 将限制从25%提高到95%，允许回测开始时的USDT持仓
    new_parameters = {
        "max_position_percent": 95.0,  # 从25%提高到95%
        "base_amount": "total_capital",
        "include_unrealized_pnl": True,
        "backtest_mode": True  # 添加回测模式标识
    }
    
    update_data = {
        "parameters": new_parameters,
        "description": "限制单个交易对的最大持仓（回测优化版本）"
    }
    
    response = requests.put(
        f"{BASE_URL}/api/risk/rules/{rule_id}",
        headers={"Content-Type": "application/json"},
        json=update_data
    )
    
    if response.status_code == 200:
        print("✅ Position limit rule updated successfully")
        data = response.json()
        print(f"New parameters: {data['data']['parameters']}")
        return True
    else:
        print(f"❌ Failed to update rule: {response.status_code}")
        print(f"Error: {response.text}")
        return False

def create_backtest_friendly_rules():
    """创建回测友好的风控规则"""
    print("\n🆕 Creating backtest-friendly rules...")
    
    # 创建回测专用的持仓限制规则
    backtest_rule = {
        "name": "回测持仓大小限制",
        "description": "专门为回测环境设计的持仓限制规则",
        "category": "position",
        "rule_type": "single_position_limit",
        "parameters": {
            "max_position_percent": 95.0,
            "base_amount": "total_capital",
            "include_unrealized_pnl": True,
            "backtest_mode": True,
            "allow_initial_cash_position": True
        },
        "conditions": {
            "engine_type": ["backtest"],
            "strategy_type": ["adaptive_volatility_grid", "grid_trading"]
        },
        "enabled": True,
        "priority": 90,  # 高优先级，覆盖默认规则
        "strategy_type": None
    }
    
    response = requests.post(
        f"{BASE_URL}/api/risk/rules",
        headers={"Content-Type": "application/json"},
        json=backtest_rule
    )
    
    if response.status_code == 200:
        print("✅ Backtest-friendly rule created successfully")
        data = response.json()
        print(f"New rule ID: {data['data']['id']}")
        return True
    else:
        print(f"❌ Failed to create rule: {response.status_code}")
        print(f"Error: {response.text}")
        return False

def disable_strict_rules():
    """禁用过于严格的规则"""
    print("\n🔇 Disabling strict rules for backtest...")
    
    rules = get_risk_rules()
    strict_rules = [
        "单个持仓大小限制",
        "最大回撤保护",
        "日交易频率限制"
    ]
    
    for rule in rules:
        if rule['name'] in strict_rules and rule['enabled']:
            rule_id = rule['id']
            print(f"Disabling rule: {rule['name']} (ID: {rule_id})")
            
            update_data = {
                "enabled": False,
                "description": f"{rule['description']} (临时禁用用于回测)"
            }
            
            response = requests.put(
                f"{BASE_URL}/api/risk/rules/{rule_id}",
                headers={"Content-Type": "application/json"},
                json=update_data
            )
            
            if response.status_code == 200:
                print(f"✅ Disabled rule: {rule['name']}")
            else:
                print(f"❌ Failed to disable rule: {rule['name']}")

def main():
    """主函数"""
    print("🚀 Starting Risk Rules Optimization for Backtest")
    print("=" * 60)
    
    # 1. 获取当前规则状态
    print("\n📋 Current Risk Rules:")
    get_risk_rules()
    
    # 2. 更新持仓限制规则
    if update_position_limit_rule():
        print("✅ Position limit rule updated")
    
    # 3. 禁用过于严格的规则
    disable_strict_rules()
    
    # 4. 验证更新结果
    print("\n📋 Updated Risk Rules:")
    get_risk_rules()
    
    print("\n🎉 Risk rules optimization completed!")
    print("💡 Tip: Restart the backtest to apply the new rules")

if __name__ == "__main__":
    main()
