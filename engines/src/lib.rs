//! SigmaX Engines Module - 重构版
//!
//! 统一引擎管理中心，符合设计原则：
//! - 高内聚，低耦合：引擎相关功能集中管理
//! - 关注点分离：不同引擎类型独立子模块
//! - 面向接口设计：统一的TradingEngine trait
//! - 可测试性设计：依赖注入和Mock支持
//! - 简洁与可演化性：模块化设计，易于扩展
//!
//! ## 重构后的模块结构
//!
//! ### 引擎子模块
//! - `backtest/`: 回测引擎 + BacktestRiskAdapter
//! - `live/`: 实盘引擎 + LiveTradingRiskAdapter
//! - `base/`: 基础引擎组件和trait定义
//! - `shared/`: 共享组件（指标、缓存、配置）
//!
//! ### 风控集成子模块
//! - `risk/`: 引擎风控集成中心
//!   - `router.rs`: RiskRouter (纯路由逻辑)
//!   - `factory.rs`: AdapterFactory (依赖注入)
//!   - `container.rs`: RiskServiceContainer (IoC容器)
//!   - `adapters.rs`: 适配器基础类和trait
//!   - `integration.rs`: 现有集成逻辑 (向后兼容)
//!
//! ## 风控系统架构 (重构版)
//!
//! ```
//! 应用层 → 适配器层 → 服务层 → 基础设施层 → 领域层
//!   ↓         ↓         ↓         ↓         ↓
//! Engine → Adapter → Router → Services → RiskEngine
//! ```

// ============================================================================
// 🏗️ 引擎子模块 - 按引擎类型组织
// ============================================================================

/// 回测引擎子模块 - 包含引擎和风控适配器
pub mod backtest;

/// 实盘引擎子模块 - 包含引擎和风控适配器
pub mod live;

/// 策略引擎子模块 - 策略专用风控适配器
pub mod strategy;

/// WebAPI引擎子模块 - WebAPI专用风控适配器
pub mod webapi;

/// 基础引擎组件 - 共享的基础设施
pub mod base;

/// 共享组件 - 指标、缓存、配置等
// pub mod shared; // 暂时注释掉，等待实现

// ============================================================================
// 🛡️ 风控集成子模块 - 引擎风控统一管理
// ============================================================================

/// 风控集成中心 - 统一的风控服务管理
pub mod risk;

/// 集成模块 - 统一风控系统集成
pub mod integration;

// ============================================================================
// 📋 其他模块 - 保留现有功能
// ============================================================================

/// 模拟交易引擎
pub mod paper;

/// 引擎管理器
pub mod manager;

/// 引擎工厂 (重构为新的factory模式)
pub mod factory;

/// 性能基准测试
pub mod benchmark;

/// 恢复管理器
pub mod recovery_manager;

/// 增强指标 (将迁移到shared模块)
pub mod enhanced_metrics;

// ============================================================================
// 🔄 向后兼容模块 - 逐步迁移
// ============================================================================

/// 风控集成 (旧版本，保持兼容性)
pub mod risk_integration;

/// 统一风控服务 (旧版本，将被新的risk模块替代)
pub mod unified_risk_service;

// ============================================================================
// 📤 公共导出 - 重构后的统一接口
// ============================================================================

// 基础组件导出
pub use base::{BaseEngine};

// 引擎导出
pub use backtest::{BacktestEngine, BacktestRiskAdapter};
pub use live::{LiveTradingEngine, LiveTradingRiskAdapter};

// 风控组件导出
pub use risk::{
    RiskRouter, AdapterFactory, RiskServiceContainer,
    EngineRiskAdapter, AdapterBase
};

// 共享组件导出
// pub use shared::{EngineMetrics, EngineCache, EngineConfig}; // 暂时注释掉，等待实现

// 其他组件导出
pub use paper::*;
pub use manager::*;
pub use factory::*;
pub use benchmark::*;

// 向后兼容导出
pub use risk_integration::*;
pub use unified_risk_service::*;

// ============================================================================
// 🧪 测试模块
// ============================================================================

#[cfg(test)]
mod tests;
