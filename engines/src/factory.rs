//! 引擎工厂

use sigmax_core::{EngineType, EngineConfig, SigmaXResult, ServiceContainer, TradingEngine};
use crate::{
    backtest::BacktestEngine,
    live::LiveTradingEngine,
    paper::PaperEngine,
};
use std::sync::Arc;

/// 引擎工厂
pub struct EngineFactory;

impl EngineFactory {
    /// 创建回测引擎
    pub async fn create_backtest_engine(
        config: EngineConfig,
        service_container: Arc<dyn ServiceContainer>,
    ) -> SigmaXResult<Arc<dyn TradingEngine>> {
        let engine = BacktestEngine::new(
            uuid::Uuid::new_v4(),
            config,
            service_container,
        ).await?;
        Ok(Arc::new(engine))
    }

    /// 创建实盘引擎
    pub async fn create_live_engine(
        config: EngineConfig,
        service_container: Arc<dyn ServiceContainer>,
    ) -> SigmaXResult<Arc<dyn TradingEngine>> {
        let engine = LiveTradingEngine::new(
            crate::live::LiveTradingConfig::default(), // 使用默认配置
        ).await?;
        Ok(Arc::new(engine))
    }

    /// 创建纸上交易引擎
    pub async fn create_paper_engine(
        config: EngineConfig,
        service_container: Arc<dyn ServiceContainer>,
    ) -> SigmaXResult<Arc<dyn TradingEngine>> {
        let engine = PaperEngine::new(
            uuid::Uuid::new_v4(),
            config,
            service_container,
        ).await?;
        Ok(Arc::new(engine))
    }

    /// 根据类型创建引擎
    pub async fn create_engine(
        engine_type: EngineType,
        config: EngineConfig,
        service_container: Arc<dyn ServiceContainer>,
    ) -> SigmaXResult<Arc<dyn TradingEngine>> {
        match engine_type {
            EngineType::Backtest => Self::create_backtest_engine(config, service_container).await,
            EngineType::Live => Self::create_live_engine(config, service_container).await,
            EngineType::Paper => Self::create_paper_engine(config, service_container).await,
        }
    }

    /// 获取支持的引擎类型
    pub fn get_supported_engine_types() -> Vec<EngineType> {
        vec![EngineType::Backtest, EngineType::Live, EngineType::Paper]
    }

    /// 获取引擎类型描述
    pub fn get_engine_description(engine_type: &EngineType) -> &'static str {
        match engine_type {
            EngineType::Backtest => "回测引擎：使用历史数据测试交易策略",
            EngineType::Live => "实盘引擎：连接真实交易所进行实盘交易",
            EngineType::Paper => "纸上交易引擎：使用实时数据但不进行真实交易",
            EngineType::Simulation => "模拟引擎：完全模拟的交易环境",
        }
    }

    /// 验证引擎配置
    pub fn validate_engine_config(
        engine_type: &EngineType,
        config: &EngineConfig,
    ) -> SigmaXResult<()> {
        // 验证基本配置
        if config.trading_pairs.is_empty() {
            return Err(sigmax_core::SigmaXError::Config(
                "Engine requires at least one trading pair".to_string()
            ));
        }

        if config.initial_capital <= sigmax_core::Amount::ZERO {
            return Err(sigmax_core::SigmaXError::Config(
                "Engine requires positive initial capital".to_string()
            ));
        }

        // 验证引擎类型特定配置
        match engine_type {
            EngineType::Backtest => {
                // 回测引擎需要历史数据配置
                if config.exchange_config.is_none() {
                    return Err(sigmax_core::SigmaXError::Config(
                        "Backtest engine requires exchange config for data source".to_string()
                    ));
                }
            }
            EngineType::Live => {
                // 实盘引擎需要交易所配置
                if config.exchange_config.is_none() {
                    return Err(sigmax_core::SigmaXError::Config(
                        "Live engine requires exchange config".to_string()
                    ));
                }
            }
            EngineType::Paper => {
                // 纸上交易引擎可以使用模拟配置
                // 不需要特殊验证
            }
            EngineType::Simulation => {
                // 模拟引擎不需要特殊验证
                // 完全在内存中运行
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests;
