//! 异步风险检查服务
//! 
//! 提供高性能的异步风险检查，减少对交易路径的阻塞

use std::sync::Arc;
use tokio::sync::{mpsc, oneshot};
use tokio::time::{Duration, timeout};
use uuid::Uuid;
use chrono::{Utc, Timelike};
use tracing::{debug, warn, info, error};
use sigmax_core::{Order, Balance, SigmaXResult, SigmaXError};

use crate::unified_interface::{UnifiedRiskInterface, RiskContext};
use crate::unified_engine::RiskCheckResult;
use crate::data_cache::{RiskDataCache, CacheManager};

/// 风险检查请求
#[derive(Debug)]
struct RiskCheckRequest {
    /// 请求ID
    id: Uuid,
    /// 风险上下文
    context: RiskContext,
    /// 缓存键
    cache_key: Option<String>,
    /// 响应通道
    response_tx: oneshot::Sender<SigmaXResult<RiskCheckResult>>,
    /// 优先级
    priority: u8,
    /// 超时（毫秒）
    timeout_ms: u64,
}

/// 异步风险检查服务
pub struct AsyncRiskService {
    /// 风控引擎
    engine: Arc<dyn UnifiedRiskInterface>,
    /// 请求发送通道
    request_tx: mpsc::Sender<RiskCheckRequest>,
    /// 缓存管理器
    cache_manager: Option<CacheManager>,
}

impl AsyncRiskService {
    /// 创建新的异步风险检查服务
    pub fn new(engine: Arc<dyn UnifiedRiskInterface>) -> Self {
        Self::new_with_options(engine, 100, None)
    }
    
    /// 创建新的异步风险检查服务（带选项）
    pub fn new_with_options(
        engine: Arc<dyn UnifiedRiskInterface>,
        queue_size: usize,
        cache_manager: Option<CacheManager>,
    ) -> Self {
        let (tx, rx) = mpsc::channel(queue_size);
        
        // 启动处理任务
        Self::spawn_processor(engine.clone(), rx, cache_manager.clone());
        
        Self {
            engine,
            request_tx: tx,
            cache_manager,
        }
    }
    
    /// 启动处理任务
    fn spawn_processor(
        engine: Arc<dyn UnifiedRiskInterface>,
        mut rx: mpsc::Receiver<RiskCheckRequest>,
        cache_manager: Option<CacheManager>,
    ) {
        tokio::spawn(async move {
            info!("启动异步风险检查处理任务");
            
            while let Some(request) = rx.recv().await {
                let engine = engine.clone();
                let cache_manager = cache_manager.clone();
                
                // 为每个请求创建单独的任务
                tokio::spawn(async move {
                    debug!("处理风险检查请求 {}", request.id);
                    
                    // 检查缓存
                    if let Some(cache_key) = &request.cache_key {
                        if let Some(cache) = &cache_manager {
                            if let Some(result) = cache.cache().get_risk_check_result(cache_key).await {
                                debug!("从缓存获取风险检查结果 {}", request.id);
                                let _ = request.response_tx.send(Ok(result));
                                return;
                            }
                        }
                    }
                    
                    // 执行风险检查
                    let result = match timeout(
                        Duration::from_millis(request.timeout_ms),
                        engine.check_risk(request.context.clone())
                    ).await {
                        Ok(result) => result,
                        Err(_) => Err(SigmaXError::Internal("风险检查超时".to_string())),
                    };
                    
                    // 更新缓存
                    if let Ok(ref check_result) = result {
                        if let Some(cache_key) = &request.cache_key {
                            if let Some(cache) = &cache_manager {
                                let _ = cache.cache().set_risk_check_result(cache_key, check_result.clone()).await;
                            }
                        }
                    }
                    
                    // 发送响应
                    let _ = request.response_tx.send(result);
                });
            }
            
            warn!("异步风险检查处理任务已退出");
        });
    }
    
    /// 提交风险检查请求
    pub async fn submit_check(
        &self,
        context: RiskContext,
        priority: Option<u8>,
        timeout_ms: Option<u64>,
    ) -> SigmaXResult<oneshot::Receiver<SigmaXResult<RiskCheckResult>>> {
        let (tx, rx) = oneshot::channel();
        
        // 生成缓存键
        let cache_key = Self::generate_cache_key(&context);
        
        // 创建请求
        let request = RiskCheckRequest {
            id: Uuid::new_v4(),
            context,
            cache_key: Some(cache_key),
            response_tx: tx,
            priority: priority.unwrap_or(1),
            timeout_ms: timeout_ms.unwrap_or(5000),
        };
        
        // 提交请求
        match self.request_tx.send(request).await {
            Ok(_) => Ok(rx),
            Err(_) => Err(SigmaXError::Internal("风险检查队列已满".to_string())),
        }
    }
    
    /// 检查订单风险（异步）
    pub async fn check_order_risk_async(
        &self,
        order: Order,
        strategy_type: Option<String>,
        priority: Option<u8>,
        timeout_ms: Option<u64>,
    ) -> SigmaXResult<oneshot::Receiver<SigmaXResult<RiskCheckResult>>> {
        let context = RiskContext::new()
            .with_order(order)
            .with_strategy_type(strategy_type.unwrap_or_default());
            
        self.submit_check(context, priority, timeout_ms).await
    }
    
    /// 检查持仓风险（异步）
    pub async fn check_position_risk_async(
        &self,
        balances: Vec<Balance>,
        strategy_type: Option<String>,
        priority: Option<u8>,
        timeout_ms: Option<u64>,
    ) -> SigmaXResult<oneshot::Receiver<SigmaXResult<RiskCheckResult>>> {
        let context = RiskContext::new()
            .with_balances(balances)
            .with_strategy_type(strategy_type.unwrap_or_default());
            
        self.submit_check(context, priority, timeout_ms).await
    }
    
    /// 检查订单风险（同步）
    pub async fn check_order_risk(
        &self,
        order: Order,
        strategy_type: Option<String>,
        timeout_ms: Option<u64>,
    ) -> SigmaXResult<RiskCheckResult> {
        let rx = self.check_order_risk_async(order, strategy_type, None, timeout_ms).await?;
        
        match rx.await {
            Ok(result) => result,
            Err(_) => Err(SigmaXError::Internal("风险检查服务已关闭".to_string())),
        }
    }
    
    /// 检查持仓风险（同步）
    pub async fn check_position_risk(
        &self,
        balances: Vec<Balance>,
        strategy_type: Option<String>,
        timeout_ms: Option<u64>,
    ) -> SigmaXResult<RiskCheckResult> {
        let rx = self.check_position_risk_async(balances, strategy_type, None, timeout_ms).await?;
        
        match rx.await {
            Ok(result) => result,
            Err(_) => Err(SigmaXError::Internal("风险检查服务已关闭".to_string())),
        }
    }
    
    /// 生成缓存键
    fn generate_cache_key(context: &RiskContext) -> String {
        let mut key_parts = Vec::new();
        
        // 添加策略类型
        if let Some(strategy_type) = &context.strategy_type {
            key_parts.push(format!("strategy:{}", strategy_type));
        }
        
        // 添加交易对
        if let Some(trading_pair) = &context.trading_pair {
            key_parts.push(format!("pair:{}", trading_pair));
        }
        
        // 添加订单信息
        if let Some(order) = &context.order {
            key_parts.push(format!("order:{:?}:{:?}:{}",
                order.side,
                order.order_type,
                order.quantity
            ));
        }
        
        // 添加时间戳（精确到分钟）
        let now = Utc::now();
        key_parts.push(format!("time:{}:{}:{}",
            now.date_naive(),
            now.hour(),
            now.minute()
        ));
        
        key_parts.join(":")
    }
}

/// 扩展 RiskDataCache 以支持风险检查结果缓存
impl RiskDataCache {
    /// 风险检查结果缓存
    fn risk_check_cache() -> &'static tokio::sync::RwLock<std::collections::HashMap<String, (RiskCheckResult, chrono::DateTime<chrono::Utc>)>> {
        use std::sync::OnceLock;
        static CACHE: OnceLock<tokio::sync::RwLock<std::collections::HashMap<String, (RiskCheckResult, chrono::DateTime<chrono::Utc>)>>> = OnceLock::new();
        CACHE.get_or_init(|| tokio::sync::RwLock::new(std::collections::HashMap::new()))
    }
    
    /// 获取风险检查结果
    pub async fn get_risk_check_result(&self, key: &str) -> Option<RiskCheckResult> {
        let cache = Self::risk_check_cache().read().await;
        
        if let Some((result, expires_at)) = cache.get(key) {
            if chrono::Utc::now() < *expires_at {
                return Some(result.clone());
            }
        }
        
        None
    }
    
    /// 设置风险检查结果
    pub async fn set_risk_check_result(&self, key: &str, result: RiskCheckResult) {
        let mut cache = Self::risk_check_cache().write().await;
        let expires_at = chrono::Utc::now() + chrono::Duration::seconds(60);
        cache.insert(key.to_string(), (result, expires_at));
        
        // 清理过期项
        let now = chrono::Utc::now();
        cache.retain(|_, (_, expires_at)| now < *expires_at);
    }
}