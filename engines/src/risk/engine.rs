//! 风控引擎实现

use async_trait::async_trait;
use sigmax_core::{Order, Balance, SigmaXResult, RiskCheckResult};
use sigmax_risk::{RuleResult};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use num_traits::ToPrimitive;

/// 引擎风控指标 (与金融风险指标不同，这是引擎运行指标)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineRiskMetrics {
    /// 总检查次数
    pub total_checks: u64,
    /// 通过检查次数
    pub passed_checks: u64,
    /// 失败检查次数
    pub failed_checks: u64,
    /// 平均检查时间 (毫秒)
    pub avg_check_time_ms: f64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

impl EngineRiskMetrics {
    pub fn new() -> Self {
        Self {
            total_checks: 0,
            passed_checks: 0,
            failed_checks: 0,
            avg_check_time_ms: 0.0,
            last_updated: Utc::now(),
        }
    }
}

/// 风控引擎trait
#[async_trait]
pub trait RiskEngine: Send + Sync {
    /// 检查订单风控
    async fn check_order_risk(&self, order: &Order, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查持仓风控
    async fn check_position_risk(&self, balances: &[Balance], strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;
    
    /// 重新加载规则
    async fn reload_rules(&self) -> SigmaXResult<()>;
    
    /// 获取风控指标
    async fn get_risk_metrics(&self) -> SigmaXResult<EngineRiskMetrics>;
}

/// 基础风控引擎实现
pub struct BasicRiskEngine {
    /// 风控规则
    rules: Arc<RwLock<Vec<Box<dyn RiskRule>>>>,
    /// 统计信息
    metrics: Arc<RwLock<EngineRiskMetrics>>,
    /// 调用计数器
    call_count: AtomicU64,
    /// 配置
    config: RiskEngineConfig,
}

/// 风控引擎配置
#[derive(Debug, Clone)]
pub struct RiskEngineConfig {
    pub max_position_ratio: f64,
    pub max_daily_loss_ratio: f64,
    pub max_order_value: f64,
    pub enable_position_check: bool,
    pub enable_order_check: bool,
    pub enable_balance_check: bool,
}

impl Default for RiskEngineConfig {
    fn default() -> Self {
        Self {
            max_position_ratio: 0.8,
            max_daily_loss_ratio: 0.05,
            max_order_value: 10000.0,
            enable_position_check: true,
            enable_order_check: true,
            enable_balance_check: true,
        }
    }
}

/// 风控规则trait
#[async_trait]
pub trait RiskRule: Send + Sync {
    /// 规则名称
    fn name(&self) -> &str;
    
    /// 检查订单
    async fn check_order(&self, order: &Order, config: &RiskEngineConfig) -> SigmaXResult<RuleResult>;
    
    /// 检查持仓
    async fn check_position(&self, balances: &[Balance], config: &RiskEngineConfig) -> SigmaXResult<RuleResult>;
    
    /// 规则是否启用
    fn is_enabled(&self) -> bool;
}

impl BasicRiskEngine {
    /// 创建新的风控引擎
    pub async fn new(config: RiskEngineConfig) -> SigmaXResult<Self> {
        info!("Creating BasicRiskEngine with config: {:?}", config);
        
        let mut rules: Vec<Box<dyn RiskRule>> = Vec::new();
        
        // 添加默认规则
        rules.push(Box::new(MaxOrderValueRule::new(config.max_order_value)));
        rules.push(Box::new(MaxPositionRatioRule::new(config.max_position_ratio)));
        rules.push(Box::new(MaxDailyLossRule::new(config.max_daily_loss_ratio)));
        
        Ok(Self {
            rules: Arc::new(RwLock::new(rules)),
            metrics: Arc::new(RwLock::new(EngineRiskMetrics::new())),
            call_count: AtomicU64::new(0),
            config,
        })
    }
    
    /// 添加风控规则
    pub async fn add_rule(&self, rule: Box<dyn RiskRule>) {
        let mut rules = self.rules.write().await;
        rules.push(rule);
        info!("Added risk rule: {}", rules.last().unwrap().name());
    }
    
    /// 移除风控规则
    pub async fn remove_rule(&self, rule_name: &str) -> bool {
        let mut rules = self.rules.write().await;
        if let Some(pos) = rules.iter().position(|r| r.name() == rule_name) {
            rules.remove(pos);
            info!("Removed risk rule: {}", rule_name);
            true
        } else {
            false
        }
    }
}

#[async_trait]
impl RiskEngine for BasicRiskEngine {
    async fn check_order_risk(&self, order: &Order, _strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        let start_time = std::time::Instant::now();
        self.call_count.fetch_add(1, Ordering::SeqCst);
        
        debug!("Checking order risk for order: {:?}", order.id);
        
        if !self.config.enable_order_check {
            return Ok(RiskCheckResult::pass());
        }
        
        let rules = self.rules.read().await;
        let mut rule_results = Vec::new();
        let mut overall_passed = true;
        let mut risk_score: f64 = 0.0;
        let mut details = HashMap::new();
        
        for rule in rules.iter() {
            if rule.is_enabled() {
                match rule.check_order(order, &self.config).await {
                    Ok(result) => {
                        if !result.is_pass() {
                            overall_passed = false;
                            risk_score += 0.2; // 每个失败的规则增加风险分数
                        }
                        rule_results.push(result);
                    }
                    Err(e) => {
                        warn!("Risk rule {} failed with error: {}", rule.name(), e);
                        overall_passed = false;
                        risk_score += 0.3;
                        rule_results.push(RuleResult::Error(format!("Rule execution failed: {}", e)));
                    }
                }
            }
        }
        
        let execution_time = start_time.elapsed().as_millis() as f64;
        
        // 更新统计信息
        {
            let mut metrics = self.metrics.write().await;
            metrics.total_checks += 1;
            if overall_passed {
                metrics.passed_checks += 1;
            } else {
                metrics.failed_checks += 1;
            }
            
            // 更新平均检查时间
            let total_time = metrics.avg_check_time_ms * (metrics.total_checks - 1) as f64 + execution_time;
            metrics.avg_check_time_ms = total_time / metrics.total_checks as f64;
        }
        
        let result = RiskCheckResult {
            passed: overall_passed,
            risk_score: Some(risk_score.min(1.0)),
            failed_rules: rule_results.iter()
                .filter(|r| !r.is_pass())
                .map(|r| sigmax_core::FailedRule {
                    rule_id: "unknown".to_string(),
                    rule_name: "rule".to_string(),
                    failure_reason: r.message().unwrap_or("Unknown failure").to_string(),
                    priority: 1,
                })
                .collect(),
            execution_time_ms: total_time as u64,
            rules_executed: rule_results.len(),
            metadata: HashMap::new(),
        };
        
        debug!("Order risk check completed: passed={}, score={:.2}, time={:.2}ms", 
               result.passed, result.risk_score.unwrap_or(0.0), execution_time);
        
        Ok(result)
    }
    
    async fn check_position_risk(&self, balances: &[Balance], _strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        let start_time = std::time::Instant::now();
        self.call_count.fetch_add(1, Ordering::SeqCst);
        
        debug!("Checking position risk for {} balances", balances.len());
        
        if !self.config.enable_position_check {
            return Ok(RiskCheckResult::pass());
        }
        
        let rules = self.rules.read().await;
        let mut rule_results = Vec::new();
        let mut overall_passed = true;
        let mut risk_score: f64 = 0.0;
        
        for rule in rules.iter() {
            if rule.is_enabled() {
                match rule.check_position(balances, &self.config).await {
                    Ok(result) => {
                        if !result.is_pass() {
                            overall_passed = false;
                            risk_score += 0.2;
                        }
                        rule_results.push(result);
                    }
                    Err(e) => {
                        warn!("Position risk rule {} failed with error: {}", rule.name(), e);
                        overall_passed = false;
                        risk_score += 0.3;
                    }
                }
            }
        }
        
        let execution_time = start_time.elapsed().as_millis() as f64;
        
        // 更新统计信息
        {
            let mut metrics = self.metrics.write().await;
            metrics.total_checks += 1;
            if overall_passed {
                metrics.passed_checks += 1;
            } else {
                metrics.failed_checks += 1;
            }
        }
        
        let result = RiskCheckResult {
            passed: overall_passed,
            risk_score: Some(risk_score.min(1.0)),
            failed_rules: rule_results.iter()
                .filter(|r| !r.is_pass())
                .map(|r| sigmax_core::FailedRule {
                    rule_id: "unknown".to_string(),
                    rule_name: "position_rule".to_string(),
                    failure_reason: r.message().unwrap_or("Unknown failure").to_string(),
                    priority: 1,
                })
                .collect(),
            execution_time_ms: execution_time as u64,
            rules_executed: rule_results.len(),
            metadata: HashMap::new(),
        };
        
        debug!("Position risk check completed: passed={}, score={:.2}, time={:.2}ms", 
               result.passed, result.risk_score.unwrap_or(0.0), execution_time);
        
        Ok(result)
    }
    
    async fn reload_rules(&self) -> SigmaXResult<()> {
        info!("Reloading risk rules");
        // 这里可以从配置文件或数据库重新加载规则
        // 目前是占位符实现
        Ok(())
    }
    
    async fn get_risk_metrics(&self) -> SigmaXResult<EngineRiskMetrics> {
        let metrics = self.metrics.read().await;
        Ok(metrics.clone())
    }
}

/// 最大订单价值规则
pub struct MaxOrderValueRule {
    max_value: f64,
    enabled: bool,
}

impl MaxOrderValueRule {
    pub fn new(max_value: f64) -> Self {
        Self {
            max_value,
            enabled: true,
        }
    }
}

#[async_trait]
impl RiskRule for MaxOrderValueRule {
    fn name(&self) -> &str {
        "max_order_value"
    }
    
    async fn check_order(&self, order: &Order, _config: &RiskEngineConfig) -> SigmaXResult<RuleResult> {
        let start_time = std::time::Instant::now();
        
        let order_value = if let Some(price) = &order.price {
            order.quantity.to_f64().unwrap_or(0.0) * price.to_f64().unwrap_or(0.0)
        } else {
            // 对于市价单，使用估算价格
            order.quantity.to_f64().unwrap_or(0.0) * 50000.0 // 假设价格
        };
        
        let passed = order_value <= self.max_value;
        let message = if passed {
            None
        } else {
            Some(format!("Order value {:.2} exceeds maximum {:.2}", order_value, self.max_value))
        };
        
        if passed {
            Ok(RuleResult::Pass)
        } else {
            Ok(RuleResult::Fail(message.unwrap_or_else(|| "Rule failed".to_string())))
        }
    }
    
    async fn check_position(&self, _balances: &[Balance], _config: &RiskEngineConfig) -> SigmaXResult<RuleResult> {
        // 这个规则不检查持仓
        Ok(RuleResult::Pass)
    }
    
    fn is_enabled(&self) -> bool {
        self.enabled
    }
}

/// 最大持仓比例规则
pub struct MaxPositionRatioRule {
    max_ratio: f64,
    enabled: bool,
}

impl MaxPositionRatioRule {
    pub fn new(max_ratio: f64) -> Self {
        Self {
            max_ratio,
            enabled: true,
        }
    }
}

#[async_trait]
impl RiskRule for MaxPositionRatioRule {
    fn name(&self) -> &str {
        "max_position_ratio"
    }
    
    async fn check_order(&self, _order: &Order, _config: &RiskEngineConfig) -> SigmaXResult<RuleResult> {
        // 这个规则主要检查持仓，对订单检查简化
        Ok(RuleResult::Pass)
    }
    
    async fn check_position(&self, balances: &[Balance], _config: &RiskEngineConfig) -> SigmaXResult<RuleResult> {
        let start_time = std::time::Instant::now();
        
        // 计算总资产价值
        let total_value: f64 = balances.iter()
            .map(|b| (b.free + b.locked).to_f64().unwrap_or(0.0))
            .sum();
        
        // 简化实现：假设所有资产都符合要求
        let passed = true;
        
        if passed {
            Ok(RuleResult::Pass)
        } else {
            Ok(RuleResult::Fail("Position ratio exceeded".to_string()))
        }
    }
    
    fn is_enabled(&self) -> bool {
        self.enabled
    }
}

/// 最大日损失规则
pub struct MaxDailyLossRule {
    max_loss_ratio: f64,
    enabled: bool,
}

impl MaxDailyLossRule {
    pub fn new(max_loss_ratio: f64) -> Self {
        Self {
            max_loss_ratio,
            enabled: true,
        }
    }
}

#[async_trait]
impl RiskRule for MaxDailyLossRule {
    fn name(&self) -> &str {
        "max_daily_loss"
    }
    
    async fn check_order(&self, _order: &Order, _config: &RiskEngineConfig) -> SigmaXResult<RuleResult> {
        // 简化实现：总是通过
        Ok(RuleResult::Pass)
    }
    
    async fn check_position(&self, _balances: &[Balance], _config: &RiskEngineConfig) -> SigmaXResult<RuleResult> {
        // 简化实现：总是通过
        Ok(RuleResult::Pass)
    }
    
    fn is_enabled(&self) -> bool {
        self.enabled
    }
}
