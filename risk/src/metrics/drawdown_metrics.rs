//! 回撤分析指标计算模块
//! 
//! 实现各种回撤相关的风险指标

use sigmax_core::{Amount, SigmaXResult};
use rust_decimal::{Decimal, MathematicalOps};
use std::collections::VecDeque;
use serde::{Serialize, Deserialize};

/// 回撤指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DrawdownMetrics {
    /// 最大回撤
    pub max_drawdown: Decimal,
    /// 当前回撤
    pub current_drawdown: Decimal,
    /// 最大回撤持续时间
    pub max_drawdown_duration: u32,
    /// 平均回撤
    pub average_drawdown: Decimal,
    /// 恢复因子
    pub recovery_factor: Decimal,
    /// 溃疡指数
    pub ulcer_index: Decimal,
    /// 痛苦指数
    pub pain_index: Decimal,
}

/// 回撤指标计算器
pub struct DrawdownCalculator<'a> {
    equity_curve: &'a VecDeque<Amount>,
    returns_history: &'a VecDeque<Decimal>,
}

impl<'a> DrawdownCalculator<'a> {
    pub fn new(
        equity_curve: &'a VecDeque<Amount>,
        returns_history: &'a VecDeque<Decimal>,
    ) -> Self {
        Self {
            equity_curve,
            returns_history,
        }
    }

    /// 计算所有回撤指标
    pub fn calculate(&self) -> SigmaXResult<DrawdownMetrics> {
        Ok(DrawdownMetrics {
            max_drawdown: self.calculate_max_drawdown()?,
            current_drawdown: self.calculate_current_drawdown()?,
            max_drawdown_duration: self.calculate_max_drawdown_duration()?,
            average_drawdown: self.calculate_average_drawdown()?,
            recovery_factor: self.calculate_recovery_factor()?,
            ulcer_index: self.calculate_ulcer_index()?,
            pain_index: self.calculate_pain_index()?,
        })
    }

    /// 计算最大回撤
    pub fn calculate_max_drawdown(&self) -> SigmaXResult<Decimal> {
        if self.equity_curve.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let mut max_drawdown = Decimal::ZERO;
        let mut peak = self.equity_curve[0];

        for &value in self.equity_curve.iter().skip(1) {
            if value > peak {
                peak = value;
            } else if peak > Decimal::ZERO {
                let drawdown = (peak - value) / peak;
                if drawdown > max_drawdown {
                    max_drawdown = drawdown;
                }
            }
        }

        Ok(max_drawdown)
    }

    /// 计算当前回撤
    pub fn calculate_current_drawdown(&self) -> SigmaXResult<Decimal> {
        if self.equity_curve.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let current_value = self.equity_curve.back().copied().unwrap_or(Decimal::ZERO);
        let peak = self.equity_curve.iter().max().copied().unwrap_or(Decimal::ZERO);

        if peak == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }

        Ok((peak - current_value) / peak)
    }

    /// 计算最大回撤持续时间
    pub fn calculate_max_drawdown_duration(&self) -> SigmaXResult<u32> {
        if self.equity_curve.is_empty() {
            return Ok(0);
        }

        let mut max_duration = 0u32;
        let mut current_duration = 0u32;
        let mut peak = self.equity_curve[0];

        for &value in self.equity_curve.iter().skip(1) {
            if value > peak {
                peak = value;
                current_duration = 0;
            } else {
                current_duration += 1;
                if current_duration > max_duration {
                    max_duration = current_duration;
                }
            }
        }

        Ok(max_duration)
    }

    /// 计算平均回撤
    pub fn calculate_average_drawdown(&self) -> SigmaXResult<Decimal> {
        if self.equity_curve.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let mut drawdowns = Vec::new();
        let mut peak = self.equity_curve[0];

        for &value in self.equity_curve.iter().skip(1) {
            if value > peak {
                peak = value;
            } else if peak > Decimal::ZERO {
                let drawdown = (peak - value) / peak;
                drawdowns.push(drawdown);
            }
        }

        if drawdowns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        Ok(drawdowns.iter().sum::<Decimal>() / Decimal::from(drawdowns.len()))
    }

    /// 计算恢复因子
    pub fn calculate_recovery_factor(&self) -> SigmaXResult<Decimal> {
        let total_return = if self.equity_curve.len() >= 2 {
            let initial = self.equity_curve.front().copied().unwrap_or(Decimal::ZERO);
            let final_val = self.equity_curve.back().copied().unwrap_or(Decimal::ZERO);
            if initial > Decimal::ZERO {
                (final_val - initial) / initial
            } else {
                Decimal::ZERO
            }
        } else {
            Decimal::ZERO
        };

        let max_dd = self.calculate_max_drawdown()?;

        if max_dd == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }

        Ok(total_return / max_dd)
    }

    /// 计算溃疡指数
    pub fn calculate_ulcer_index(&self) -> SigmaXResult<Decimal> {
        if self.equity_curve.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let mut squared_drawdowns = Vec::new();
        let mut peak = self.equity_curve[0];

        for &value in self.equity_curve.iter().skip(1) {
            if value > peak {
                peak = value;
            } else if peak > Decimal::ZERO {
                let drawdown = (peak - value) / peak;
                squared_drawdowns.push(drawdown.powi(2));
            }
        }

        if squared_drawdowns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let mean_squared_dd = squared_drawdowns.iter().sum::<Decimal>() / Decimal::from(squared_drawdowns.len());
        
        Ok(mean_squared_dd.sqrt().unwrap_or(Decimal::ZERO))
    }

    /// 计算痛苦指数
    pub fn calculate_pain_index(&self) -> SigmaXResult<Decimal> {
        if self.equity_curve.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let mut total_pain = Decimal::ZERO;
        let mut peak = self.equity_curve[0];
        let mut periods_count = 0;

        for &value in self.equity_curve.iter().skip(1) {
            if value > peak {
                peak = value;
            } else if peak > Decimal::ZERO {
                let drawdown = (peak - value) / peak;
                total_pain += drawdown;
            }
            periods_count += 1;
        }

        if periods_count == 0 {
            return Ok(Decimal::ZERO);
        }

        // 痛苦指数 = 平均回撤百分比
        Ok(total_pain / Decimal::from(periods_count) * Decimal::from(100))
    }

    /// 获取回撤序列（用于进一步分析）
    pub fn get_drawdown_series(&self) -> Vec<Decimal> {
        if self.equity_curve.is_empty() {
            return Vec::new();
        }

        let mut drawdowns = Vec::new();
        let mut peak = self.equity_curve[0];

        drawdowns.push(Decimal::ZERO); // 第一个点的回撤为0

        for &value in self.equity_curve.iter().skip(1) {
            if value > peak {
                peak = value;
                drawdowns.push(Decimal::ZERO);
            } else if peak > Decimal::ZERO {
                let drawdown = (peak - value) / peak;
                drawdowns.push(drawdown);
            } else {
                drawdowns.push(Decimal::ZERO);
            }
        }

        drawdowns
    }

    /// 计算回撤分布统计
    pub fn calculate_drawdown_distribution(&self) -> DrawdownDistribution {
        let drawdowns = self.get_drawdown_series();
        
        if drawdowns.is_empty() {
            return DrawdownDistribution::default();
        }

        let mut non_zero_drawdowns: Vec<Decimal> = drawdowns
            .into_iter()
            .filter(|&dd| dd > Decimal::ZERO)
            .collect();

        if non_zero_drawdowns.is_empty() {
            return DrawdownDistribution::default();
        }

        non_zero_drawdowns.sort();

        let count = non_zero_drawdowns.len();
        let mean = non_zero_drawdowns.iter().sum::<Decimal>() / Decimal::from(count);
        let median = if count % 2 == 0 {
            (non_zero_drawdowns[count / 2 - 1] + non_zero_drawdowns[count / 2]) / Decimal::from(2)
        } else {
            non_zero_drawdowns[count / 2]
        };

        let percentile_95 = non_zero_drawdowns[(count as f64 * 0.95) as usize];
        let percentile_99 = non_zero_drawdowns[(count as f64 * 0.99) as usize];

        DrawdownDistribution {
            mean,
            median,
            percentile_95,
            percentile_99,
            max: non_zero_drawdowns.last().copied().unwrap_or(Decimal::ZERO),
            count: count as u32,
        }
    }
}

/// 回撤分布统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DrawdownDistribution {
    /// 平均回撤
    pub mean: Decimal,
    /// 中位数回撤
    pub median: Decimal,
    /// 95%分位数回撤
    pub percentile_95: Decimal,
    /// 99%分位数回撤
    pub percentile_99: Decimal,
    /// 最大回撤
    pub max: Decimal,
    /// 回撤次数
    pub count: u32,
}

impl Default for DrawdownDistribution {
    fn default() -> Self {
        Self {
            mean: Decimal::ZERO,
            median: Decimal::ZERO,
            percentile_95: Decimal::ZERO,
            percentile_99: Decimal::ZERO,
            max: Decimal::ZERO,
            count: 0,
        }
    }
}
