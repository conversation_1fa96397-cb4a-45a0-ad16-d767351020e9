//! 统一风控规则引擎
//! 
//! 设计理念：
//! 1. 一切皆规则：消除配置与规则的概念重复
//! 2. 参数化规则：规则包含自己的参数，无需外部配置
//! 3. 优先级驱动：通过优先级控制执行顺序
//! 4. 简单开关：通过enabled字段控制规则启用

use async_trait::async_trait;
use sigmax_core::{Order, Balance, SigmaXResult, SigmaXError, RiskManager as RiskManagerTrait, TradingPair, Quantity};
use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::HashMap;
use serde::{Serialize, Deserialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use tracing::{debug, warn, info, error};

/// 规则执行结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RuleResult {
    /// 规则检查通过
    Pass,
    /// 规则检查失败
    Fail(String),
    /// 规则执行出错
    Error(String),
    /// 规则被跳过（条件不满足）
    Skip(String),
}

impl RuleResult {
    pub fn is_pass(&self) -> bool {
        matches!(self, RuleResult::Pass)
    }
    
    pub fn is_fail(&self) -> bool {
        matches!(self, RuleResult::Fail(_))
    }
    
    pub fn message(&self) -> Option<&str> {
        match self {
            RuleResult::Pass => None,
            RuleResult::Fail(msg) | RuleResult::Error(msg) | RuleResult::Skip(msg) => Some(msg),
        }
    }
}

/// 风控检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCheckResult {
    /// 是否通过检查
    pub passed: bool,
    /// 执行的规则数量
    pub rules_executed: usize,
    /// 失败的规则
    pub failed_rules: Vec<FailedRule>,
    /// 跳过的规则
    pub skipped_rules: Vec<String>,
    /// 总执行时间（毫秒）
    pub execution_time_ms: u64,
    /// 检查时间
    pub checked_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FailedRule {
    pub rule_id: Uuid,
    pub rule_name: String,
    pub rule_type: String,
    pub failure_reason: String,
}

impl RiskCheckResult {
    pub fn passed() -> Self {
        Self {
            passed: true,
            rules_executed: 0,
            failed_rules: Vec::new(),
            skipped_rules: Vec::new(),
            execution_time_ms: 0,
            checked_at: Utc::now(),
        }
    }
    
    pub fn failed(failed_rule: FailedRule) -> Self {
        Self {
            passed: false,
            rules_executed: 1,
            failed_rules: vec![failed_rule],
            skipped_rules: Vec::new(),
            execution_time_ms: 0,
            checked_at: Utc::now(),
        }
    }
}

/// 统一风控规则定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnifiedRiskRule {
    /// 规则ID
    pub id: Uuid,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: Option<String>,
    /// 规则分类
    pub category: String,
    /// 规则类型
    pub rule_type: String,
    /// 规则参数
    pub parameters: serde_json::Value,
    /// 执行条件
    pub conditions: Option<serde_json::Value>,
    /// 是否启用
    pub enabled: bool,
    /// 执行优先级
    pub priority: i32,
    /// 适用策略类型
    pub strategy_type: Option<String>,
    /// 适用交易对
    pub trading_pairs: Vec<String>,
    /// 执行次数
    pub execution_count: u64,
    /// 成功次数
    pub success_count: u64,
    /// 失败次数
    pub failure_count: u64,
    /// 最后执行时间
    pub last_executed_at: Option<DateTime<Utc>>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 规则执行上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleExecutionContext {
    /// 订单信息
    pub order: Option<Order>,
    /// 持仓信息
    pub balances: Option<Vec<Balance>>,
    /// 策略类型
    pub strategy_type: Option<String>,
    /// 交易对
    pub trading_pair: Option<String>,
    /// 额外上下文数据
    pub extra_data: HashMap<String, serde_json::Value>,
}

/// 统一风控规则引擎
use std::fmt;

/// 统一风控规则引擎

pub struct UnifiedRiskEngine {
    /// 规则列表（按优先级排序）
    rules: Arc<RwLock<Vec<UnifiedRiskRule>>>,
    /// 规则缓存（按类型索引）
    rule_cache: Arc<RwLock<HashMap<String, Vec<UnifiedRiskRule>>>>,
    /// 执行统计
    execution_stats: Arc<RwLock<ExecutionStats>>,
    /// 规则仓储
    repository: Arc<dyn UnifiedRiskRepository>,
}

impl fmt::Debug for UnifiedRiskEngine {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("UnifiedRiskEngine")
         .field("rules", &self.rules)
         .field("rule_cache", &self.rule_cache)
         .field("execution_stats", &self.execution_stats)
         .finish_non_exhaustive()
    }
}

#[derive(Debug, Default)]
pub struct ExecutionStats {
    pub total_checks: u64,
    pub passed_checks: u64,
    pub failed_checks: u64,
    pub total_execution_time_ms: u64,
    pub rules_execution_count: HashMap<String, u64>,
}

/// 统一风控仓储接口
#[async_trait]
pub trait UnifiedRiskRepository: Send + Sync {
    /// 根据策略类型获取策略ID列表
    async fn get_strategy_ids_by_type(&self, strategy_type: &str) -> SigmaXResult<Vec<Uuid>>;

    /// 获取指定策略今日的订单数量
    async fn get_trade_count_for_strategies_today(&self, strategy_ids: &[Uuid]) -> SigmaXResult<u64>;

    /// 获取指定策略今日的订单数量（支持排除已取消订单）
    async fn get_trade_count_for_strategies_today_with_filter(&self, strategy_ids: &[Uuid], exclude_cancelled: bool) -> SigmaXResult<u64>;

    /// 获取策略的历史资产净值
    async fn get_historical_net_asset_values(&self, strategy_id: Uuid) -> SigmaXResult<Vec<rust_decimal::Decimal>>;

    /// 获取所有启用的规则
    async fn get_enabled_rules(&self) -> SigmaXResult<Vec<UnifiedRiskRule>>;

    /// 根据策略类型获取规则
    async fn get_rules_by_strategy(&self, strategy_type: &str) -> SigmaXResult<Vec<UnifiedRiskRule>>;

    /// 根据规则类型获取规则
    async fn get_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Vec<UnifiedRiskRule>>;

    /// 保存规则执行记录
    async fn save_execution_record(&self, record: &RuleExecutionRecord) -> SigmaXResult<()>;

    /// 更新规则统计
    async fn update_rule_stats(&self, rule_id: Uuid, result: &RuleResult) -> SigmaXResult<()>;

    /// 获取规则执行历史
    async fn get_execution_history(
        &self,
        rule_id: Uuid,
        page: usize,
        per_page: usize,
        result_filter: Option<&str>,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
    ) -> SigmaXResult<(Vec<ExecutionHistoryRecord>, usize)>;

    /// 获取单个规则
    async fn get_rule(&self, rule_id: Uuid) -> SigmaXResult<Option<UnifiedRiskRule>>;

    /// 更新规则
    async fn update_rule(&self, rule: &UnifiedRiskRule) -> SigmaXResult<()>;

    /// 根据过滤条件获取规则列表
    async fn get_rules_with_filter(
        &self,
        filter: &RuleFilter,
        page: u64,
        per_page: u64,
    ) -> SigmaXResult<Vec<UnifiedRiskRule>>;

    /// 统计符合过滤条件的规则数量
    async fn count_rules_with_filter(&self, filter: &RuleFilter) -> SigmaXResult<u64>;

    // ===== rule_helpers 需要的新方法 =====

    /// 获取滑动窗口小时交易次数
    async fn get_sliding_hour_trade_count(&self, strategy_type: Option<&str>) -> SigmaXResult<u64>;

    /// 获取当前小时交易次数
    async fn get_current_hour_trade_count(&self, strategy_type: Option<&str>) -> SigmaXResult<u64>;

    /// 获取指定交易对的最后订单时间
    async fn get_last_order_time_for_pair(
        &self,
        trading_pair: &str,
        strategy_type: Option<&str>
    ) -> SigmaXResult<Option<DateTime<Utc>>>;

    /// 获取全局最后订单时间
    async fn get_last_order_time_global(&self, strategy_type: Option<&str>) -> SigmaXResult<Option<DateTime<Utc>>>;

    /// 获取历史价格数据
    async fn get_historical_prices(&self, trading_pair: &str, periods: usize) -> SigmaXResult<Vec<f64>>;

    /// 获取投资组合回撤数据
    async fn get_portfolio_drawdown_data(&self, strategy_ids: &[Uuid]) -> SigmaXResult<(f64, f64, f64)>; // (initial, current, peak)

    /// 获取持仓价值数据
    async fn get_position_values(&self, strategy_ids: &[Uuid]) -> SigmaXResult<f64>;

    /// 获取当前余额数据
    async fn get_current_balances(&self, exchange_id: &str) -> SigmaXResult<Vec<(String, f64, f64)>>; // (asset, free, locked)

    // ===== 新增的回撤计算相关方法 =====

    /// 获取投资组合快照数据用于回撤计算
    async fn get_portfolio_snapshots(&self, strategy_ids: &[Uuid], limit: usize) -> SigmaXResult<Vec<(DateTime<Utc>, f64)>>;

    /// 获取策略的投资组合ID
    async fn get_portfolio_id_by_strategy(&self, strategy_id: Uuid) -> SigmaXResult<Option<Uuid>>;

    /// 创建投资组合快照
    async fn create_portfolio_snapshot(&self, portfolio_id: Uuid, strategy_id: Option<Uuid>, total_value: f64, cash_balance: f64, position_value: f64) -> SigmaXResult<()>;

    /// 获取投资组合的历史峰值
    async fn get_portfolio_peak_value(&self, portfolio_id: Uuid) -> SigmaXResult<f64>;

    /// 更新投资组合的峰值和回撤
    async fn update_portfolio_drawdown(&self, portfolio_id: Uuid, current_value: f64, peak_value: f64, drawdown: f64) -> SigmaXResult<()>;

    // ===== 市场流动性相关方法 =====

    /// 获取市场流动性数据
    async fn get_market_liquidity_data(&self, trading_pair: &str, exchange_id: Option<i32>) -> SigmaXResult<Option<(f64, f64, f64)>>; // (liquidity_score, spread, volume_24h)

    /// 获取交易对的最新流动性评分
    async fn get_latest_liquidity_score(&self, trading_pair: &str) -> SigmaXResult<f64>;

    // ===== 交易时间相关方法 =====

    /// 检查交易对在指定交易所是否开放交易
    async fn is_trading_pair_open(&self, trading_pair: &str, exchange_name: Option<&str>) -> SigmaXResult<bool>;

    /// 获取交易所的交易时间配置
    async fn get_exchange_trading_hours(&self, exchange_name: &str, trading_pair: Option<&str>) -> SigmaXResult<Option<(bool, String)>>; // (is_24_7, timezone)

    // ===== 风险预算相关方法 =====

    /// 获取风险预算配置
    async fn get_risk_budget_config(&self, strategy_type: Option<&str>) -> SigmaXResult<Option<(f64, f64, f64)>>; // (total_budget, used_budget, usage_percentage)

    /// 获取策略的风险预算分配
    async fn get_strategy_risk_allocation(&self, strategy_type: &str) -> SigmaXResult<Option<f64>>;

    /// 计算当前风险预算使用情况
    async fn calculate_current_risk_usage(&self, strategy_ids: &[Uuid]) -> SigmaXResult<f64>;

    /// 创建风险预算使用记录
    async fn create_risk_budget_usage_record(&self, strategy_type: Option<&str>, strategy_id: Option<Uuid>, total_budget: f64, used_budget: f64, usage_percentage: f64) -> SigmaXResult<()>;
}

#[derive(Debug, Clone, Default)]
pub struct RuleFilter {
    pub category: Option<String>,
    pub rule_type: Option<String>,
    pub enabled: Option<bool>,
    pub strategy_type: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleExecutionRecord {
    pub rule_id: Uuid,
    pub execution_context: RuleExecutionContext,
    pub result: RuleResult,
    pub execution_time_ms: u64,
    pub executed_at: DateTime<Utc>,
}

/// 执行历史记录（用于API响应）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionHistoryRecord {
    /// 记录ID
    pub id: Uuid,
    /// 规则ID
    pub rule_id: Uuid,
    /// 执行上下文
    pub execution_context: serde_json::Value,
    /// 执行结果
    pub result: String,
    /// 结果消息
    pub message: Option<String>,
    /// 执行耗时（毫秒）
    pub execution_time_ms: Option<i32>,
    /// 执行时间
    pub executed_at: DateTime<Utc>,
}

impl UnifiedRiskEngine {
    /// 创建新的统一风控引擎
    pub async fn new(repository: Arc<dyn UnifiedRiskRepository>) -> SigmaXResult<Self> {
        let engine = Self {
            rules: Arc::new(RwLock::new(Vec::new())),
            rule_cache: Arc::new(RwLock::new(HashMap::new())),
            execution_stats: Arc::new(RwLock::new(ExecutionStats::default())),
            repository,
        };
        
        // 初始化时加载规则
        engine.reload_rules().await?;
        
        Ok(engine)
    }

    /// 获取repository引用（用于rule_helpers访问）
    pub fn repository(&self) -> &Arc<dyn UnifiedRiskRepository> {
        &self.repository
    }



    /// 重新加载规则
    pub async fn reload_rules(&self) -> SigmaXResult<()> {
        info!("🔄 重新加载风控规则...");
        
        let mut rules = self.repository.get_enabled_rules().await?;
        
        // 按优先级排序（优先级高的先执行）
        rules.sort_by(|a, b| b.priority.cmp(&a.priority));
        
        // 更新规则列表
        {
            let mut rules_guard = self.rules.write().await;
            *rules_guard = rules.clone();
        }
        
        // 更新规则缓存
        {
            let mut cache = self.rule_cache.write().await;
            cache.clear();
            
            for rule in &rules {
                cache.entry(rule.rule_type.clone())
                     .or_insert_with(Vec::new)
                     .push(rule.clone());
            }
        }
        
        info!("✅ 成功加载 {} 条风控规则", rules.len());
        Ok(())
    }


    /// 检查订单风险
    pub async fn check_order_risk(&self, order: &Order, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        let start_time = std::time::Instant::now();
        
        let context = RuleExecutionContext {
            order: Some(order.clone()),
            balances: None,
            strategy_type: strategy_type.map(|s| s.to_string()),
            trading_pair: Some(format!("{}_{}", order.trading_pair.base, order.trading_pair.quote)),
            extra_data: HashMap::new(),
        };
        
        let result = self.execute_rules(&context).await?;
        
        // 更新统计
        {
            let mut stats = self.execution_stats.write().await;
            stats.total_checks += 1;
            if result.passed {
                stats.passed_checks += 1;
            } else {
                stats.failed_checks += 1;
            }
            stats.total_execution_time_ms += result.execution_time_ms;
        }
        
        debug!("订单风控检查完成: {} 规则执行, 耗时 {}ms, 结果: {}", 
               result.rules_executed, result.execution_time_ms, 
               if result.passed { "通过" } else { "失败" });
        
        Ok(result)
    }
    
    /// 检查持仓风险
    pub async fn check_position_risk(&self, balances: &[Balance], strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        let context = RuleExecutionContext {
            order: None,
            balances: Some(balances.to_vec()),
            strategy_type: strategy_type.map(|s| s.to_string()),
            trading_pair: None,
            extra_data: HashMap::new(),
        };

        self.execute_rules(&context).await
    }

    /// 执行规则检查
    async fn execute_rules(&self, context: &RuleExecutionContext) -> SigmaXResult<RiskCheckResult> {
        let start_time = std::time::Instant::now();
        let mut result = RiskCheckResult::passed();

        let rules = self.rules.read().await;

        for rule in rules.iter() {
            // 检查规则是否适用
            if !self.is_rule_applicable(rule, context) {
                result.skipped_rules.push(format!("{}: 不适用", rule.name));
                continue;
            }

            // 执行规则
            let rule_start = std::time::Instant::now();
            let rule_result = self.execute_single_rule(rule, context).await;
            let rule_duration = rule_start.elapsed().as_millis() as u64;

            result.rules_executed += 1;

            // 记录执行结果
            let execution_record = RuleExecutionRecord {
                rule_id: rule.id,
                execution_context: context.clone(),
                result: rule_result.clone(),
                execution_time_ms: rule_duration,
                executed_at: Utc::now(),
            };

            // 异步保存执行记录（不阻塞主流程）
            let repo = self.repository.clone();
            let record = execution_record.clone();
            tokio::spawn(async move {
                if let Err(e) = repo.save_execution_record(&record).await {
                    warn!("保存规则执行记录失败: {}", e);
                }
            });

            // 更新规则统计
            let repo = self.repository.clone();
            let rule_id = rule.id;
            let result_clone = rule_result.clone();
            tokio::spawn(async move {
                if let Err(e) = repo.update_rule_stats(rule_id, &result_clone).await {
                    warn!("更新规则统计失败: {}", e);
                }
            });

            // 处理规则结果
            match rule_result {
                RuleResult::Pass => {
                    debug!("规则 {} 检查通过", rule.name);
                }
                RuleResult::Fail(reason) => {
                    warn!("规则 {} 检查失败: {}", rule.name, reason);
                    result.passed = false;
                    result.failed_rules.push(FailedRule {
                        rule_id: rule.id,
                        rule_name: rule.name.clone(),
                        rule_type: rule.rule_type.clone(),
                        failure_reason: reason,
                    });

                    // 如果是高优先级规则失败，立即停止检查
                    if rule.priority >= 90 {
                        break;
                    }
                }
                RuleResult::Error(error) => {
                    error!("规则 {} 执行出错: {}", rule.name, error);
                    result.passed = false;
                    result.failed_rules.push(FailedRule {
                        rule_id: rule.id,
                        rule_name: rule.name.clone(),
                        rule_type: rule.rule_type.clone(),
                        failure_reason: format!("执行错误: {}", error),
                    });
                }
                RuleResult::Skip(reason) => {
                    debug!("规则 {} 被跳过: {}", rule.name, reason);
                    result.skipped_rules.push(format!("{}: {}", rule.name, reason));
                }
            }
        }

        result.execution_time_ms = start_time.elapsed().as_millis() as u64;
        result.checked_at = Utc::now();

        Ok(result)
    }

    /// 检查规则是否适用于当前上下文
    fn is_rule_applicable(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> bool {
        // 检查策略类型匹配
        if let Some(rule_strategy) = &rule.strategy_type {
            if let Some(context_strategy) = &context.strategy_type {
                if rule_strategy != context_strategy {
                    return false;
                }
            } else {
                // 规则指定了策略类型，但上下文没有策略类型
                return false;
            }
        }

        // 检查交易对匹配
        if !rule.trading_pairs.is_empty() {
            if let Some(trading_pair) = &context.trading_pair {
                if !rule.trading_pairs.contains(trading_pair) {
                    return false;
                }
            } else {
                // 规则指定了交易对，但上下文没有交易对
                return false;
            }
        }

        // 检查执行条件
        if let Some(conditions) = &rule.conditions {
            if !self.evaluate_conditions(conditions, context) {
                return false;
            }
        }

        true
    }

    /// 评估规则条件
    fn evaluate_conditions(&self, conditions: &serde_json::Value, _context: &RuleExecutionContext) -> bool {
        // TODO: 实现复杂的条件评估逻辑
        // 例如：时间条件、市场条件、持仓条件等

        // 暂时返回true，后续可以扩展
        true
    }

    /// 执行单个规则
    async fn execute_single_rule(&self, rule: &UnifiedRiskRule, context: &RuleExecutionContext) -> RuleResult {
        match rule.rule_type.as_str() {
            "order_amount_limit" => self.check_order_amount_limit(rule, context).await,
            "order_quantity_limit" => self.check_order_quantity_limit(rule, context).await,
            "max_drawdown_protection" => self.check_max_drawdown_protection(rule, context).await,
            "daily_trading_frequency" => self.check_daily_trading_frequency(rule, context).await,
            "hourly_trading_frequency" => self.check_hourly_trading_frequency(rule, context).await,
            "min_order_interval" => self.check_min_order_interval(rule, context).await,
            "trading_time_window" => self.check_trading_time_window(rule, context).await,
            "single_position_limit" => self.check_single_position_limit(rule, context).await,
            "max_leverage_limit" => self.check_max_leverage_limit(rule, context).await,
            "volatility_threshold" => self.check_volatility_threshold(rule, context).await,
            "min_liquidity_requirement" => self.check_min_liquidity_requirement(rule, context).await,
            _ => {
                warn!("未知的规则类型: {}", rule.rule_type);
                RuleResult::Error(format!("未知的规则类型: {}", rule.rule_type))
            }
        }
    }

    /// 获取执行统计
    pub async fn get_execution_stats(&self) -> ExecutionStats {
        let stats = self.execution_stats.read().await;
        ExecutionStats {
            total_checks: stats.total_checks,
            passed_checks: stats.passed_checks,
            failed_checks: stats.failed_checks,
            total_execution_time_ms: stats.total_execution_time_ms,
            rules_execution_count: stats.rules_execution_count.clone(),
        }
    }
}

// 以下代码仅在测试时编译
#[cfg(test)]
pub mod tests {
    use super::*;

    /// 模拟的统一风控仓储（用于单元测试）
    pub struct MockUnifiedRiskRepository {
        rules: Arc<RwLock<Vec<UnifiedRiskRule>>>,
        execution_records: Arc<RwLock<Vec<RuleExecutionRecord>>>,
    }

    impl MockUnifiedRiskRepository {
        pub fn new() -> Self {
            Self {
                rules: Arc::new(RwLock::new(Vec::new())),
                execution_records: Arc::new(RwLock::new(Vec::new())),
            }
        }

        pub async fn with_rules(rules: Vec<UnifiedRiskRule>) -> Self {
            let repo = Self::new();
            *repo.rules.write().await = rules;
            repo
        }
    }

    #[async_trait]
    impl UnifiedRiskRepository for MockUnifiedRiskRepository {
        async fn get_strategy_ids_by_type(&self, strategy_type: &str) -> SigmaXResult<Vec<Uuid>> {
            let rules = self.rules.read().await;
            let mut ids = Vec::new();
            // This is a mock, so we'll just find the first rule with a matching type
            // and return its ID, assuming it's a valid UUID for a strategy.
            if let Some(rule) = rules.iter().find(|r| r.strategy_type.as_deref() == Some(strategy_type)) {
                ids.push(rule.id); 
            }
            Ok(ids)
        }

        async fn get_trade_count_for_strategies_today(&self, strategy_ids: &[Uuid]) -> SigmaXResult<u64> {
            // Mock implementation: return a fixed number for testing.
            Ok(5)
        }

        async fn get_enabled_rules(&self) -> SigmaXResult<Vec<UnifiedRiskRule>> {
            let rules = self.rules.read().await;
            Ok(rules.iter().filter(|r| r.enabled).cloned().collect())
        }

        async fn get_rules_by_strategy(&self, strategy_type: &str) -> SigmaXResult<Vec<UnifiedRiskRule>> {
            let rules = self.rules.read().await;
            Ok(rules.iter()
                .filter(|r| r.enabled && (r.strategy_type.as_deref() == Some(strategy_type) || r.strategy_type.is_none()))
                .cloned()
                .collect())
        }

        async fn get_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Vec<UnifiedRiskRule>> {
            let rules = self.rules.read().await;
            Ok(rules.iter()
                .filter(|r| r.enabled && r.rule_type == rule_type)
                .cloned()
                .collect())
        }

        async fn save_execution_record(&self, record: &RuleExecutionRecord) -> SigmaXResult<()> {
            let mut records = self.execution_records.write().await;
            records.push(record.clone());
            debug!("保存规则执行记录: {}", record.rule_id);
            Ok(())
        }

        async fn update_rule_stats(&self, rule_id: Uuid, result: &RuleResult) -> SigmaXResult<()> {
            debug!("更新规则统计: {} -> {:?}", rule_id, result);
            // 在模拟模式下，我们只记录日志，不实际更新数据库
            Ok(())
        }

        async fn get_execution_history(
            &self,
            _rule_id: Uuid,
            _page: usize,
            _per_page: usize,
            _result_filter: Option<&str>,
            _start_time: Option<DateTime<Utc>>,
            _end_time: Option<DateTime<Utc>>,
        ) -> SigmaXResult<(Vec<ExecutionHistoryRecord>, usize)> {
            // 返回空的执行历史
            Ok((Vec::new(), 0))
        }

        async fn get_rule(&self, rule_id: Uuid) -> SigmaXResult<Option<UnifiedRiskRule>> {
            let rules = self.rules.read().await;
            Ok(rules.iter().find(|r| r.id == rule_id).cloned())
        }

        async fn update_rule(&self, rule: &UnifiedRiskRule) -> SigmaXResult<()> {
            let mut rules = self.rules.write().await;
            if let Some(existing_rule) = rules.iter_mut().find(|r| r.id == rule.id) {
                *existing_rule = rule.clone();
            }
            Ok(())
        }

        async fn get_rules_with_filter(
            &self,
            filter: &RuleFilter,
            _page: u64,
            _per_page: u64,
        ) -> SigmaXResult<Vec<UnifiedRiskRule>> {
            let rules = self.rules.read().await;
            let filtered_rules: Vec<UnifiedRiskRule> = rules.iter()
                .filter(|rule| {
                    if let Some(category) = &filter.category {
                        if rule.category != *category {
                            return false;
                        }
                    }
                    if let Some(rule_type) = &filter.rule_type {
                        if rule.rule_type != *rule_type {
                            return false;
                        }
                    }
                    if let Some(enabled) = filter.enabled {
                        if rule.enabled != enabled {
                            return false;
                        }
                    }
                    if let Some(strategy_type) = &filter.strategy_type {
                        if rule.strategy_type.as_deref() != Some(strategy_type) && rule.strategy_type.is_some() {
                            return false;
                        }
                    }
                    true
                })
                .cloned()
                .collect();
            Ok(filtered_rules)
        }

        async fn count_rules_with_filter(&self, filter: &RuleFilter) -> SigmaXResult<u64> {
            let rules = self.get_rules_with_filter(filter, 1, 1000).await?;
            Ok(rules.len() as u64)
        }

        // ===== 新增的缺失方法实现 =====

        async fn get_trade_count_for_strategies_today_with_filter(&self, _strategy_ids: &[Uuid], _exclude_cancelled: bool) -> SigmaXResult<u64> {
            Ok(4) // Mock 值
        }

        async fn get_historical_net_asset_values(&self, _strategy_id: Uuid) -> SigmaXResult<Vec<rust_decimal::Decimal>> {
            Ok(vec![rust_decimal::Decimal::new(100000, 2), rust_decimal::Decimal::new(95000, 2)])
        }

        async fn get_sliding_hour_trade_count(&self, _strategy_type: Option<&str>) -> SigmaXResult<u64> {
            Ok(3)
        }

        async fn get_current_hour_trade_count(&self, _strategy_type: Option<&str>) -> SigmaXResult<u64> {
            Ok(2)
        }

        async fn get_last_order_time_for_pair(&self, _trading_pair: &str, _strategy_type: Option<&str>) -> SigmaXResult<Option<DateTime<Utc>>> {
            Ok(Some(Utc::now()))
        }

        async fn get_last_order_time_global(&self, _strategy_type: Option<&str>) -> SigmaXResult<Option<DateTime<Utc>>> {
            Ok(Some(Utc::now()))
        }

        async fn get_historical_prices(&self, _trading_pair: &str, _periods: usize) -> SigmaXResult<Vec<f64>> {
            Ok(vec![50000.0, 49000.0, 48000.0, 49500.0, 51000.0])
        }

        async fn get_portfolio_drawdown_data(&self, _strategy_ids: &[Uuid]) -> SigmaXResult<(f64, f64, f64)> {
            Ok((100000.0, 95000.0, 105000.0)) // (initial, current, peak)
        }

        async fn get_position_values(&self, _strategy_ids: &[Uuid]) -> SigmaXResult<f64> {
            Ok(50000.0)
        }

        async fn get_current_balances(&self, _exchange_id: &str) -> SigmaXResult<Vec<(String, f64, f64)>> {
            Ok(vec![("USDT".to_string(), 50000.0, 0.0), ("BTC".to_string(), 1.0, 0.0)])
        }

        async fn get_portfolio_snapshots(&self, _strategy_ids: &[Uuid], _limit: usize) -> SigmaXResult<Vec<(DateTime<Utc>, f64)>> {
            Ok(vec![(Utc::now(), 95000.0)])
        }

        async fn get_portfolio_id_by_strategy(&self, _strategy_id: Uuid) -> SigmaXResult<Option<Uuid>> {
            Ok(Some(Uuid::new_v4()))
        }

        async fn create_portfolio_snapshot(&self, _portfolio_id: Uuid, _strategy_id: Option<Uuid>, _total_value: f64, _cash_balance: f64, _position_value: f64) -> SigmaXResult<()> {
            Ok(())
        }

        async fn get_portfolio_peak_value(&self, _portfolio_id: Uuid) -> SigmaXResult<f64> {
            Ok(105000.0)
        }

        async fn update_portfolio_drawdown(&self, _portfolio_id: Uuid, _current_value: f64, _peak_value: f64, _drawdown: f64) -> SigmaXResult<()> {
            Ok(())
        }

        async fn get_market_liquidity_data(&self, _trading_pair: &str, _exchange_id: Option<i32>) -> SigmaXResult<Option<(f64, f64, f64)>> {
            Ok(Some((85.0, 0.1, 1000000.0))) // (liquidity_score, spread, volume_24h)
        }

        async fn get_latest_liquidity_score(&self, _trading_pair: &str) -> SigmaXResult<f64> {
            Ok(85.0)
        }

        async fn is_trading_pair_open(&self, _trading_pair: &str, _exchange_name: Option<&str>) -> SigmaXResult<bool> {
            Ok(true)
        }

        async fn get_exchange_trading_hours(&self, _exchange_name: &str, _trading_pair: Option<&str>) -> SigmaXResult<Option<(bool, String)>> {
            Ok(Some((true, "UTC".to_string())))
        }

        async fn get_risk_budget_config(&self, _strategy_type: Option<&str>) -> SigmaXResult<Option<(f64, f64, f64)>> {
            Ok(Some((100000.0, 65500.0, 65.5))) // (total_budget, used_budget, usage_percentage)
        }

        async fn get_strategy_risk_allocation(&self, _strategy_type: &str) -> SigmaXResult<Option<f64>> {
            Ok(Some(50000.0))
        }

        async fn calculate_current_risk_usage(&self, _strategy_ids: &[Uuid]) -> SigmaXResult<f64> {
            Ok(32750.0)
        }

        async fn create_risk_budget_usage_record(&self, _strategy_type: Option<&str>, _strategy_id: Option<Uuid>, _total_budget: f64, _used_budget: f64, _usage_percentage: f64) -> SigmaXResult<()> {
            Ok(())
        }
    }

    impl UnifiedRiskEngine {
        /// 创建用于单元测试的模拟风控引擎
        pub async fn new_for_unit_test(rules: Vec<UnifiedRiskRule>) -> SigmaXResult<Self> {
            info!("🚀 初始化统一风控引擎（单元测试模式）");

            let repository = Arc::new(MockUnifiedRiskRepository::with_rules(rules).await);

            let engine = Self {
                repository,
                rules: Arc::new(RwLock::new(Vec::new())),
                rule_cache: Arc::new(RwLock::new(HashMap::new())),
                execution_stats: Arc::new(RwLock::new(ExecutionStats::default())),
            };

            engine.reload_rules().await?;

            let rules_count = engine.rules.read().await.len();
            info!("✅ 统一风控引擎初始化成功（单元测试模式），加载了 {} 条规则", rules_count);
            Ok(engine)
        }
    }

    #[tokio::test]
    #[ignore = "此为集成测试，需要真实的数据库环境"]
    async fn test_engine_with_real_database() -> SigmaXResult<()> {
        // 该测试展示了如何使用真实的数据库进行集成测试。
        //
        // 运行此测试前，请确保：
        // 1. 有一个正在运行的PostgreSQL实例。
        // 2. 设置了环境变量 `TEST_DATABASE_URL`，例如：
        //    export TEST_DATABASE_URL="postgresql://user:pass@localhost:5432/test_db"
        // 3. 相关的数据库表已通过迁移创建。

        use crate::repositories::unified_risk_repository::SqlUnifiedRiskRepository;
        use sigmax_database::DatabaseManager;
        use sigmax_core::DatabaseConfig;

        let db_url = std::env::var("TEST_DATABASE_URL")
            .expect("请设置 TEST_DATABASE_URL 环境变量以运行集成测试");

        let db_config = DatabaseConfig {
            url: db_url,
            max_connections: 1,
            connection_timeout: 5,
            auto_migrate: true, // 在测试中可以启用自动迁移
        };

        let db_manager = Arc::new(DatabaseManager::new(db_config).await?);
        let real_repo = Arc::new(SqlUnifiedRiskRepository::new(db_manager));

        // TODO: 在此处可以向测试数据库中插入一些测试规则

        // 使用真实的Repository创建引擎
        let engine = UnifiedRiskEngine::new(real_repo).await?;

        // 执行断言
        // 例如，检查引擎是否成功加载了您插入的规则
        let rules = engine.rules.read().await;
        // assert_eq!(rules.len(), 1); // 假设您插入了一条规则

        Ok(())
    }
}

/// 为 UnifiedRiskEngine 实现 RiskManager trait，使其可以完全替代传统风控管理器
#[async_trait]
impl RiskManagerTrait for UnifiedRiskEngine {
    /// 检查订单风险
    async fn check_order_risk(&self, order: &Order) -> SigmaXResult<bool> {
        debug!("统一风控引擎检查订单风险: {:?} {} @ {:?}",
               order.side, order.quantity, order.price);

        // 调用内部的详细检查方法
        let result = UnifiedRiskEngine::check_order_risk(self, order, None).await?;

        if result.passed {
            debug!("统一风控引擎订单检查通过");
            Ok(true)
        } else {
            warn!("统一风控引擎订单检查失败: {:?}",
                  result.failed_rules.iter().map(|r| &r.failure_reason).collect::<Vec<_>>());
            Ok(false)
        }
    }

    /// 检查持仓风险
    async fn check_position_risk(&self, balances: &[Balance]) -> SigmaXResult<bool> {
        debug!("统一风控引擎检查持仓风险");

        // 调用内部的详细检查方法
        let result = UnifiedRiskEngine::check_position_risk(self, balances, None).await?;

        if result.passed {
            debug!("统一风控引擎持仓检查通过");
            Ok(true)
        } else {
            warn!("统一风控引擎持仓检查失败: {:?}",
                  result.failed_rules.iter().map(|r| &r.failure_reason).collect::<Vec<_>>());
            Ok(false)
        }
    }

    /// 获取最大允许订单大小
    ///
    /// 查找逻辑:
    /// 1. 优先查找与交易对精确匹配的规则。
    /// 2. 如果没有找到，则查找适用于所有交易对的全局规则。
    /// 3. 如果都没有找到，返回一个安全的默认值 (0)。
    async fn get_max_order_size(&self, trading_pair: &TradingPair) -> SigmaXResult<Quantity> {
        debug!("获取最大订单大小限制: {:?}", trading_pair);

        let rules = self.rules.read().await;
        let pair_str = format!("{}_{}", trading_pair.base, trading_pair.quote);

        let mut global_limit: Option<i64> = None;

        // 规则已按优先级排序，因此第一个匹配项就是最高优先级的匹配项
        for rule in rules.iter() {
            if rule.rule_type == "order_quantity_limit" && rule.enabled {
                // 尝试将参数解析为 i64
                let limit = rule.parameters.get("max_quantity").and_then(|v| v.as_i64());

                if !rule.trading_pairs.is_empty() {
                    // 检查特定交易对规则
                    if rule.trading_pairs.contains(&pair_str) {
                        if let Some(val) = limit {
                            info!("找到 {:?} 的特定最大订单数量限制: {}", trading_pair, val);
                            // TODO: 从 trading_pair 或配置中获取正确的精度
                            let precision = 8; // 临时使用8位精度
                            return Ok(Quantity::new(val, precision));
                        }
                    }
                } else {
                    // 这是一个全局规则，先记录下来，以防没有找到特定规则
                    if global_limit.is_none() {
                        global_limit = limit;
                    }
                }
            }
        }

        // 如果没有找到特定规则，但找到了全局规则
        if let Some(val) = global_limit {
            info!("未找到 {:?} 的特定限制，使用全局最大订单数量限制: {}", trading_pair, val);
            // TODO: 从 trading_pair 或配置中获取正确的精度
            let precision = 8; // 临时使用8位精度
            return Ok(Quantity::new(val, precision));
        }

        // 如果特定和全局规则都未找到
        warn!("未找到 {:?} 的最大订单数量限制规则，返回安全默认值 0", trading_pair);
        Ok(Quantity::new(0, 8)) // 返回一个安全的默认值 0
    }
}
