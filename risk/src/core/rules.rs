//! 风险规则定义和执行
//!
//! 提供风险规则的核心逻辑

use async_trait::async_trait;
use sigmax_core::SigmaXResult;

use super::types::{RiskRule, RiskCheckRequest, RiskCheckResult};

/// 规则执行器特征
#[async_trait]
pub trait RuleExecutor: Send + Sync {
    /// 执行规则
    async fn execute(&self, rule: &RiskRule, request: &RiskCheckRequest) -> SigmaXResult<RiskCheckResult>;
}

/// 默认规则执行器
pub struct DefaultRuleExecutor;

#[async_trait]
impl RuleExecutor for DefaultRuleExecutor {
    async fn execute(&self, _rule: &RiskRule, request: &RiskCheckRequest) -> SigmaXResult<RiskCheckResult> {
        // 默认实现：总是通过
        Ok(RiskCheckResult::passed(request.check_type.clone()))
    }
}
