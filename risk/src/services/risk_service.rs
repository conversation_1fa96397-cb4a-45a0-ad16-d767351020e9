//! 风险检查服务
//!
//! 提供高级风险检查业务逻辑

use async_trait::async_trait;
use std::sync::Arc;
use tracing::{info, debug, warn};
use sigmax_core::{Order, Balance, SigmaXResult};

use crate::core::{
    RiskEngine, RiskCheckRequest, RiskCheckResult, RiskCheckType,
    RiskMetrics, PortfolioInfo
};
use sigmax_core::config::risk::RiskManagementConfig;

/// 风险检查服务
pub struct RiskService {
    /// 风险引擎
    engine: Arc<dyn RiskEngine>,
}

impl RiskService {
    /// 创建新的风险服务
    pub fn new(engine: Arc<dyn RiskEngine>) -> Self {
        Self { engine }
    }
    
    /// 批量风险检查
    pub async fn check_batch_risk(&self, requests: Vec<RiskCheckRequest>) -> SigmaXResult<Vec<RiskCheckResult>> {
        info!("执行批量风险检查，请求数量: {}", requests.len());
        
        let mut results = Vec::new();
        for request in requests {
            match self.engine.check_risk(request).await {
                Ok(result) => results.push(result),
                Err(e) => {
                    warn!("批量检查中的单个请求失败: {}", e);
                    // 可以选择继续处理其他请求或者直接返回错误
                    return Err(e);
                }
            }
        }
        
        Ok(results)
    }
    
    /// 智能风险评估
    pub async fn smart_risk_assessment(&self, order: &Order, context: RiskAssessmentContext) -> SigmaXResult<SmartRiskResult> {
        debug!("执行智能风险评估");
        
        // 基础风险检查
        let basic_result = self.engine.check_order_risk(order, context.strategy_type.as_deref()).await?;
        
        // 获取当前风险指标
        let metrics = self.engine.get_risk_metrics().await?;
        
        // 计算风险调整建议
        let adjustments = self.calculate_risk_adjustments(&basic_result, &metrics, &context).await?;
        
        Ok(SmartRiskResult {
            basic_check: basic_result.clone(),
            current_metrics: metrics.clone(),
            adjustments,
            recommendation: self.generate_recommendation(&basic_result, &metrics).await?,
        })
    }
    
    /// 计算风险调整建议
    async fn calculate_risk_adjustments(
        &self, 
        result: &RiskCheckResult, 
        metrics: &RiskMetrics,
        context: &RiskAssessmentContext
    ) -> SigmaXResult<Vec<RiskAdjustment>> {
        let mut adjustments = Vec::new();
        
        // 基于当前风险水平提供调整建议
        if result.risk_score > 0.7 {
            adjustments.push(RiskAdjustment {
                adjustment_type: "reduce_position_size".to_string(),
                description: "建议减少仓位大小".to_string(),
                suggested_value: Some(0.5),
                priority: 1,
            });
        }
        
        if metrics.max_drawdown > 0.15 {
            adjustments.push(RiskAdjustment {
                adjustment_type: "tighten_stop_loss".to_string(),
                description: "建议收紧止损".to_string(),
                suggested_value: Some(0.02),
                priority: 2,
            });
        }
        
        Ok(adjustments)
    }
    
    /// 生成风险建议
    async fn generate_recommendation(&self, result: &RiskCheckResult, metrics: &RiskMetrics) -> SigmaXResult<String> {
        if !result.passed {
            return Ok("建议暂停交易，等待风险水平降低".to_string());
        }
        
        if result.risk_score > 0.5 {
            return Ok("建议谨慎交易，密切监控风险指标".to_string());
        }
        
        if metrics.sharpe_ratio < 1.0 {
            return Ok("建议优化策略参数，提高风险调整收益".to_string());
        }
        
        Ok("当前风险水平可接受，可以正常交易".to_string())
    }
}

/// 风险评估上下文
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct RiskAssessmentContext {
    /// 策略类型
    pub strategy_type: Option<String>,
    /// 市场条件
    pub market_condition: Option<String>,
    /// 历史表现
    pub historical_performance: Option<f64>,
    /// 用户风险偏好
    pub risk_tolerance: Option<String>,
}

/// 智能风险评估结果
#[derive(Debug, Clone, serde::Serialize)]
pub struct SmartRiskResult {
    /// 基础检查结果
    pub basic_check: crate::core::RiskCheckResult,
    /// 当前风险指标
    pub current_metrics: RiskMetrics,
    /// 风险调整建议
    pub adjustments: Vec<RiskAdjustment>,
    /// 总体建议
    pub recommendation: String,
}

/// 风险调整建议
#[derive(Debug, Clone, serde::Serialize)]
pub struct RiskAdjustment {
    /// 调整类型
    pub adjustment_type: String,
    /// 描述
    pub description: String,
    /// 建议值
    pub suggested_value: Option<f64>,
    /// 优先级
    pub priority: i32,
}

/// 风险服务特征
#[async_trait]
pub trait RiskServiceTrait: Send + Sync {
    /// 检查订单风险
    async fn check_order_risk(&self, order: &Order, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查持仓风险
    async fn check_position_risk(&self, balances: &[Balance], strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查投资组合风险
    async fn check_portfolio_risk(&self, portfolio: &PortfolioInfo, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;
    
    /// 批量风险检查
    async fn check_batch_risk(&self, requests: Vec<RiskCheckRequest>) -> SigmaXResult<Vec<RiskCheckResult>>;
    
    /// 智能风险评估
    async fn smart_risk_assessment(&self, order: &Order, context: RiskAssessmentContext) -> SigmaXResult<SmartRiskResult>;
    
    /// 获取风险指标
    async fn get_risk_metrics(&self) -> SigmaXResult<RiskMetrics>;
    
    /// 更新风险参数
    async fn update_risk_parameters(&self, parameters: sigmax_core::config::risk::RiskConfigParameters) -> SigmaXResult<()>;
}

#[async_trait]
impl RiskServiceTrait for RiskService {
    async fn check_order_risk(&self, order: &Order, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        self.engine.check_order_risk(order, strategy_type).await
    }
    
    async fn check_position_risk(&self, balances: &[Balance], strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        self.engine.check_position_risk(balances, strategy_type).await
    }
    
    async fn check_portfolio_risk(&self, portfolio: &PortfolioInfo, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        self.engine.check_portfolio_risk(portfolio, strategy_type).await
    }
    
    async fn check_batch_risk(&self, requests: Vec<RiskCheckRequest>) -> SigmaXResult<Vec<RiskCheckResult>> {
        self.check_batch_risk(requests).await
    }
    
    async fn smart_risk_assessment(&self, order: &Order, context: RiskAssessmentContext) -> SigmaXResult<SmartRiskResult> {
        self.smart_risk_assessment(order, context).await
    }
    
    async fn get_risk_metrics(&self) -> SigmaXResult<RiskMetrics> {
        self.engine.get_risk_metrics().await
    }
    
    async fn update_risk_parameters(&self, parameters: sigmax_core::config::risk::RiskConfigParameters) -> SigmaXResult<()> {
        self.engine.update_risk_parameters(parameters).await
    }
}
