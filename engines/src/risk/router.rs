//! 风控路由器 - 纯路由分发逻辑
//! 
//! 设计原则：
//! - 单一职责：只负责请求路由，无业务逻辑
//! - 高性能：轻量级设计，最小化延迟
//! - 可扩展：支持动态添加新的适配器类型

use async_trait::async_trait;
use sigmax_core::{EngineType, Order, Balance, SigmaXResult};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Instant;
use tracing::{debug, error, warn};

use super::{RiskCheckRequest, RiskMode};
use sigmax_core::AdapterMetrics;
use sigmax_core::EngineRiskAdapter;

/// 风控路由器 - 纯路由逻辑
pub struct RiskRouter {
    /// 适配器映射表
    adapters: HashMap<EngineType, Arc<dyn EngineRiskAdapter>>,
    /// 指标收集器
    metrics: Option<Arc<dyn crate::risk::MetricsCollector>>,
}

impl RiskRouter {
    /// 创建新的风控路由器
    pub fn new() -> Self {
        Self {
            adapters: HashMap::new(),
            metrics: None,
        }
    }
    
    /// 设置指标收集器
    pub fn with_metrics(mut self, metrics: Arc<dyn crate::risk::MetricsCollector>) -> Self {
        self.metrics = Some(metrics);
        self
    }
    
    /// 注册适配器
    pub fn register_adapter(&mut self, engine_type: EngineType, adapter: Arc<dyn EngineRiskAdapter>) {
        debug!("Registering risk adapter for engine type: {:?}", engine_type);
        self.adapters.insert(engine_type, adapter);
    }
    
    /// 移除适配器
    pub fn unregister_adapter(&mut self, engine_type: &EngineType) -> Option<Arc<dyn EngineRiskAdapter>> {
        debug!("Unregistering risk adapter for engine type: {:?}", engine_type);
        self.adapters.remove(engine_type)
    }
    
    /// 获取适配器
    pub fn get_adapter(&self, engine_type: &EngineType) -> Option<&Arc<dyn EngineRiskAdapter>> {
        self.adapters.get(engine_type)
    }
    
    /// 列出所有注册的引擎类型
    pub fn list_engine_types(&self) -> Vec<EngineType> {
        self.adapters.keys().cloned().collect()
    }
    
    /// 检查是否支持指定的引擎类型
    pub fn supports_engine_type(&self, engine_type: &EngineType) -> bool {
        self.adapters.contains_key(engine_type)
    }
    
    /// 路由风控检查请求
    pub async fn route_risk_check(&self, request: RiskCheckRequest) -> SigmaXResult<bool> {
        let start_time = Instant::now();
        
        // 获取对应的适配器
        let adapter = self.adapters.get(&request.engine_type)
            .ok_or_else(|| {
                error!("No risk adapter found for engine type: {:?}", request.engine_type);
                sigmax_core::SigmaXError::Configuration(
                    format!("Unsupported engine type: {:?}", request.engine_type)
                )
            })?;
        
        // 执行风控检查
        let result = adapter.check_order_risk(&request.order).await;
        
        // 记录指标
        let elapsed = start_time.elapsed();
        self.record_routing_metrics(&request.engine_type, &result, elapsed).await;
        
        match result {
            Ok(passed) => {
                debug!(
                    "Risk check completed for engine {:?}: passed={}, duration={:?}",
                    request.engine_type, passed, elapsed
                );
                Ok(passed)
            }
            Err(e) => {
                error!(
                    "Risk check failed for engine {:?}: {}, duration={:?}",
                    request.engine_type, e, elapsed
                );
                Err(e)
            }
        }
    }
    
    /// 路由持仓风控检查
    pub async fn route_position_risk_check(
        &self, 
        engine_type: EngineType, 
        balances: &[Balance]
    ) -> SigmaXResult<bool> {
        let start_time = Instant::now();
        
        let adapter = self.adapters.get(&engine_type)
            .ok_or_else(|| {
                error!("No risk adapter found for engine type: {:?}", engine_type);
                sigmax_core::SigmaXError::Configuration(
                    format!("Unsupported engine type: {:?}", engine_type)
                )
            })?;
        
        let result = adapter.check_position_risk(balances).await;
        
        // 记录指标
        let elapsed = start_time.elapsed();
        if let Some(metrics) = &self.metrics {
            metrics.record_latency("position_risk_check", elapsed).await;
        }
        
        match result {
            Ok(passed) => {
                debug!(
                    "Position risk check completed for engine {:?}: passed={}, duration={:?}",
                    engine_type, passed, elapsed
                );
                Ok(passed)
            }
            Err(e) => {
                error!(
                    "Position risk check failed for engine {:?}: {}, duration={:?}",
                    engine_type, e, elapsed
                );
                Err(e)
            }
        }
    }
    
    /// 获取适配器指标
    pub async fn get_adapter_metrics(&self, engine_type: &EngineType) -> SigmaXResult<Option<AdapterMetrics>> {
        if let Some(adapter) = self.adapters.get(engine_type) {
            match adapter.get_metrics().await {
                Ok(metrics) => Ok(Some(metrics)),
                Err(e) => {
                    warn!("Failed to get metrics for engine {:?}: {}", engine_type, e);
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }
    
    /// 获取所有适配器的指标
    pub async fn get_all_adapter_metrics(&self) -> HashMap<EngineType, AdapterMetrics> {
        let mut all_metrics = HashMap::new();
        
        for (engine_type, adapter) in &self.adapters {
            match adapter.get_metrics().await {
                Ok(metrics) => {
                    all_metrics.insert(*engine_type, metrics);
                }
                Err(e) => {
                    warn!("Failed to get metrics for engine {:?}: {}", engine_type, e);
                }
            }
        }
        
        all_metrics
    }
    
    /// 记录路由指标
    async fn record_routing_metrics(
        &self,
        engine_type: &EngineType,
        result: &SigmaXResult<bool>,
        duration: std::time::Duration,
    ) {
        if let Some(metrics) = &self.metrics {
            // 记录延迟
            metrics.record_latency("risk_routing", duration).await;
            
            // 记录风控检查结果
            match result {
                Ok(passed) => {
                    metrics.record_risk_check(*passed).await;
                }
                Err(_) => {
                    // 错误情况记录为失败
                    metrics.record_risk_check(false).await;
                }
            }
        }
    }
}

impl Default for RiskRouter {
    fn default() -> Self {
        Self::new()
    }
}

/// 路由器构建器
pub struct RiskRouterBuilder {
    router: RiskRouter,
}

impl RiskRouterBuilder {
    pub fn new() -> Self {
        Self {
            router: RiskRouter::new(),
        }
    }
    
    pub fn with_metrics(mut self, metrics: Arc<dyn crate::risk::MetricsCollector>) -> Self {
        self.router = self.router.with_metrics(metrics);
        self
    }
    
    pub fn register_adapter(mut self, engine_type: EngineType, adapter: Arc<dyn EngineRiskAdapter>) -> Self {
        self.router.register_adapter(engine_type, adapter);
        self
    }
    
    pub fn build(self) -> RiskRouter {
        self.router
    }
}

impl Default for RiskRouterBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use core::EngineType;
    use std::sync::Arc;
    use async_trait::async_trait;

    // Mock适配器用于测试
    struct MockRiskAdapter {
        engine_type: EngineType,
        should_pass: bool,
    }

    #[async_trait]
    impl EngineRiskAdapter for MockRiskAdapter {
        async fn check_order_risk(&self, _order: &Order) -> SigmaXResult<bool> {
            Ok(self.should_pass)
        }
        
        async fn check_position_risk(&self, _balances: &[Balance]) -> SigmaXResult<bool> {
            Ok(self.should_pass)
        }
        
        fn engine_type(&self) -> EngineType {
            self.engine_type
        }
        
        async fn get_metrics(&self) -> SigmaXResult<RiskAdapterMetrics> {
            Ok(RiskAdapterMetrics {
                engine_type: self.engine_type,
                cache_hit_rate: 0.8,
                avg_latency_ms: 5.0,
                throughput_rps: 1000.0,
                error_rate: 0.01,
            })
        }
    }

    #[tokio::test]
    async fn test_risk_router_basic_operations() {
        let mut router = RiskRouter::new();
        
        // 注册适配器
        let adapter = Arc::new(MockRiskAdapter {
            engine_type: EngineType::Backtest,
            should_pass: true,
        });
        router.register_adapter(EngineType::Backtest, adapter);
        
        // 检查支持的引擎类型
        assert!(router.supports_engine_type(&EngineType::Backtest));
        assert!(!router.supports_engine_type(&EngineType::Live));
        
        // 列出引擎类型
        let types = router.list_engine_types();
        assert_eq!(types.len(), 1);
        assert!(types.contains(&EngineType::Backtest));
    }

    #[tokio::test]
    async fn test_risk_router_request_routing() {
        let mut router = RiskRouter::new();
        
        let adapter = Arc::new(MockRiskAdapter {
            engine_type: EngineType::Backtest,
            should_pass: true,
        });
        router.register_adapter(EngineType::Backtest, adapter);
        
        // 创建测试请求
        let order = Order::default(); // 假设有默认实现
        let request = RiskCheckRequest::new(order, EngineType::Backtest);
        
        // 路由请求
        let result = router.route_risk_check(request).await;
        assert!(result.is_ok());
        assert!(result.unwrap());
    }

    #[tokio::test]
    async fn test_risk_router_unsupported_engine() {
        let router = RiskRouter::new();
        
        let order = Order::default();
        let request = RiskCheckRequest::new(order, EngineType::Live);
        
        let result = router.route_risk_check(request).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_risk_router_builder() {
        let adapter = Arc::new(MockRiskAdapter {
            engine_type: EngineType::Backtest,
            should_pass: true,
        });
        
        let router = RiskRouterBuilder::new()
            .register_adapter(EngineType::Backtest, adapter)
            .build();
        
        assert!(router.supports_engine_type(&EngineType::Backtest));
    }
}
