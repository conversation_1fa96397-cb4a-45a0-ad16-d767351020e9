//! 风险数据缓存系统
//! 
//! 提供高性能的风险数据缓存，减少数据库访问

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc, Duration};
use uuid::Uuid;
use tracing::{debug, warn};

use sigmax_core::SigmaXResult;
use crate::core::types::RiskMetrics;

/// 缓存项
#[derive(Debug, Clone)]
struct CacheItem<T> {
    /// 数据
    data: T,
    /// 过期时间
    expires_at: DateTime<Utc>,
}

impl<T> CacheItem<T> {
    /// 创建新的缓存项
    fn new(data: T, ttl_seconds: i64) -> Self {
        Self {
            data,
            expires_at: Utc::now() + Duration::seconds(ttl_seconds),
        }
    }
    
    /// 检查是否过期
    fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at
    }
}

/// 通用缓存
#[derive(Debug)]
pub struct Cache<K, V> 
where 
    K: std::hash::Hash + Eq + Clone + Send + Sync + 'static,
    V: Clone + Send + Sync + 'static,
{
    /// 缓存数据
    data: RwLock<HashMap<K, CacheItem<V>>>,
    /// 默认TTL（秒）
    default_ttl: i64,
    /// 最大缓存项数
    max_items: usize,
}

impl<K, V> Cache<K, V> 
where 
    K: std::hash::Hash + Eq + Clone + Send + Sync + 'static,
    V: Clone + Send + Sync + 'static,
{
    /// 创建新的缓存
    pub fn new(max_items: usize, default_ttl: i64) -> Self {
        Self {
            data: RwLock::new(HashMap::new()),
            default_ttl,
            max_items,
        }
    }
    
    /// 获取缓存项
    pub async fn get(&self, key: &K) -> Option<V> {
        let data = self.data.read().await;
        
        if let Some(item) = data.get(key) {
            if item.is_expired() {
                return None;
            }
            
            return Some(item.data.clone());
        }
        
        None
    }
    
    /// 设置缓存项
    pub async fn set(&self, key: K, value: V, ttl_seconds: Option<i64>) {
        let mut data = self.data.write().await;
        
        // 如果达到最大缓存项数，清理过期项
        if data.len() >= self.max_items && !data.contains_key(&key) {
            self.cleanup_expired(&mut data).await;
            
            // 如果仍然达到最大缓存项数，移除最旧的项
            if data.len() >= self.max_items {
                self.remove_oldest(&mut data).await;
            }
        }
        
        // 设置缓存项
        let ttl = ttl_seconds.unwrap_or(self.default_ttl);
        data.insert(key, CacheItem::new(value, ttl));
    }
    
    /// 移除缓存项
    pub async fn remove(&self, key: &K) -> Option<V> {
        let mut data = self.data.write().await;
        
        data.remove(key).map(|item| item.data)
    }
    
    /// 清理过期项
    async fn cleanup_expired(&self, data: &mut HashMap<K, CacheItem<V>>) {
        let expired_keys: Vec<K> = data.iter()
            .filter(|(_, item)| item.is_expired())
            .map(|(key, _)| key.clone())
            .collect();
            
        for key in expired_keys {
            data.remove(&key);
        }
    }
    
    /// 移除最旧的项
    async fn remove_oldest(&self, data: &mut HashMap<K, CacheItem<V>>) {
        if let Some(oldest_key) = data.iter()
            .min_by_key(|(_, item)| item.expires_at)
            .map(|(key, _)| key.clone()) {
            
            data.remove(&oldest_key);
        }
    }
    
    /// 清理所有过期项
    pub async fn cleanup(&self) {
        let mut data = self.data.write().await;
        self.cleanup_expired(&mut data).await;
    }
    
    /// 清空缓存
    pub async fn clear(&self) {
        let mut data = self.data.write().await;
        data.clear();
    }
    
    /// 获取缓存项数量
    pub async fn len(&self) -> usize {
        let data = self.data.read().await;
        data.len()
    }
    
    /// 检查缓存是否为空
    pub async fn is_empty(&self) -> bool {
        let data = self.data.read().await;
        data.is_empty()
    }
}

/// 风险数据缓存
#[derive(Debug)]
pub struct RiskDataCache {
    /// 市场数据缓存 (交易对 -> 价格列表)
    market_data_cache: Cache<String, Vec<f64>>,
    
    /// 流动性数据缓存 (交易对 -> (流动性评分, 价差, 24h交易量))
    liquidity_cache: Cache<String, (f64, f64, f64)>,
    
    /// 回撤数据缓存 (策略类型 -> (初始资金, 当前资金, 峰值资金))
    drawdown_cache: Cache<String, (f64, f64, f64)>,
    
    /// 风险指标缓存 (策略ID -> 风险指标)
    metrics_cache: Cache<Uuid, RiskMetrics>,
    
    /// 交易次数缓存 (策略类型 -> 今日交易次数)
    trade_count_cache: Cache<String, u64>,
    
    /// 最后订单时间缓存 (交易对 -> 最后订单时间)
    last_order_time_cache: Cache<String, DateTime<Utc>>,
}

impl RiskDataCache {
    /// 创建新的风险数据缓存
    pub fn new() -> Self {
        Self {
            // 市场数据缓存 - 5分钟过期
            market_data_cache: Cache::new(100, 300),
            
            // 流动性数据缓存 - 10分钟过期
            liquidity_cache: Cache::new(100, 600),
            
            // 回撤数据缓存 - 1分钟过期
            drawdown_cache: Cache::new(100, 60),
            
            // 风险指标缓存 - 5分钟过期
            metrics_cache: Cache::new(100, 300),
            
            // 交易次数缓存 - 30秒过期
            trade_count_cache: Cache::new(100, 30),
            
            // 最后订单时间缓存 - 10秒过期
            last_order_time_cache: Cache::new(100, 10),
        }
    }
    
    /// 获取市场数据
    pub async fn get_market_data(&self, trading_pair: &str) -> Option<Vec<f64>> {
        self.market_data_cache.get(&trading_pair.to_string()).await
    }
    
    /// 设置市场数据
    pub async fn set_market_data(&self, trading_pair: &str, data: Vec<f64>) {
        self.market_data_cache.set(trading_pair.to_string(), data, None).await;
    }
    
    /// 获取流动性数据
    pub async fn get_liquidity_data(&self, trading_pair: &str) -> Option<(f64, f64, f64)> {
        self.liquidity_cache.get(&trading_pair.to_string()).await
    }
    
    /// 设置流动性数据
    pub async fn set_liquidity_data(&self, trading_pair: &str, data: (f64, f64, f64)) {
        self.liquidity_cache.set(trading_pair.to_string(), data, None).await;
    }
    
    /// 获取回撤数据
    pub async fn get_drawdown_data(&self, strategy_type: &str) -> Option<(f64, f64, f64)> {
        self.drawdown_cache.get(&strategy_type.to_string()).await
    }
    
    /// 设置回撤数据
    pub async fn set_drawdown_data(&self, strategy_type: &str, data: (f64, f64, f64)) {
        self.drawdown_cache.set(strategy_type.to_string(), data, None).await;
    }
    
    /// 获取风险指标
    pub async fn get_risk_metrics(&self, strategy_id: Uuid) -> Option<RiskMetrics> {
        self.metrics_cache.get(&strategy_id).await
    }
    
    /// 设置风险指标
    pub async fn set_risk_metrics(&self, strategy_id: Uuid, metrics: RiskMetrics) {
        self.metrics_cache.set(strategy_id, metrics, None).await;
    }
    
    /// 获取今日交易次数
    pub async fn get_trade_count(&self, strategy_type: &str) -> Option<u64> {
        self.trade_count_cache.get(&strategy_type.to_string()).await
    }
    
    /// 设置今日交易次数
    pub async fn set_trade_count(&self, strategy_type: &str, count: u64) {
        self.trade_count_cache.set(strategy_type.to_string(), count, None).await;
    }
    
    /// 获取最后订单时间
    pub async fn get_last_order_time(&self, key: &str) -> Option<DateTime<Utc>> {
        self.last_order_time_cache.get(&key.to_string()).await
    }
    
    /// 设置最后订单时间
    pub async fn set_last_order_time(&self, key: &str, time: DateTime<Utc>) {
        self.last_order_time_cache.set(key.to_string(), time, None).await;
    }
    
    /// 清理所有过期缓存
    pub async fn cleanup(&self) {
        self.market_data_cache.cleanup().await;
        self.liquidity_cache.cleanup().await;
        self.drawdown_cache.cleanup().await;
        self.metrics_cache.cleanup().await;
        self.trade_count_cache.cleanup().await;
        self.last_order_time_cache.cleanup().await;
    }
    
    /// 清空所有缓存
    pub async fn clear(&self) {
        self.market_data_cache.clear().await;
        self.liquidity_cache.clear().await;
        self.drawdown_cache.clear().await;
        self.metrics_cache.clear().await;
        self.trade_count_cache.clear().await;
        self.last_order_time_cache.clear().await;
    }
}

impl Default for RiskDataCache {
    fn default() -> Self {
        Self::new()
    }
}

/// 缓存管理器
#[derive(Debug, Clone)]
pub struct CacheManager {
    /// 风险数据缓存
    cache: Arc<RiskDataCache>,
}

impl CacheManager {
    /// 创建新的缓存管理器
    pub fn new(cache: Arc<RiskDataCache>) -> Self {
        Self { cache }
    }
    
    /// 获取风险数据缓存
    pub fn cache(&self) -> &Arc<RiskDataCache> {
        &self.cache
    }
    
    /// 启动缓存清理任务
    pub fn start_cleanup_task(&self, interval_seconds: u64) {
        let cache = self.cache.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(interval_seconds));
            
            loop {
                interval.tick().await;
                debug!("执行缓存清理");
                cache.cleanup().await;
            }
        });
    }
}

/// 缓存数据提供者特征
#[async_trait::async_trait]
pub trait CachedDataProvider: Send + Sync {
    /// 获取市场数据
    async fn get_market_data(&self, trading_pair: &str, periods: usize) -> SigmaXResult<Vec<f64>>;
    
    /// 获取流动性数据
    async fn get_liquidity_data(&self, trading_pair: &str) -> SigmaXResult<(f64, f64, f64)>;
    
    /// 获取回撤数据
    async fn get_drawdown_data(&self, strategy_type: &str) -> SigmaXResult<(f64, f64, f64)>;
    
    /// 获取风险指标
    async fn get_risk_metrics(&self, strategy_id: Uuid) -> SigmaXResult<RiskMetrics>;
    
    /// 获取今日交易次数
    async fn get_trade_count(&self, strategy_type: &str, exclude_cancelled: bool) -> SigmaXResult<u64>;
    
    /// 获取最后订单时间
    async fn get_last_order_time(&self, trading_pair: Option<&str>, strategy_type: Option<&str>) -> SigmaXResult<Option<DateTime<Utc>>>;
}

/// 缓存数据提供者实现
pub struct CachedDataProviderImpl<T> {
    /// 底层数据提供者
    provider: Arc<T>,
    /// 缓存管理器
    cache_manager: CacheManager,
}

impl<T> CachedDataProviderImpl<T> {
    /// 创建新的缓存数据提供者
    pub fn new(provider: Arc<T>, cache_manager: CacheManager) -> Self {
        Self {
            provider,
            cache_manager,
        }
    }
}

#[async_trait::async_trait]
impl<T> CachedDataProvider for CachedDataProviderImpl<T>
where
    T: UnifiedRiskRepositoryExt + Send + Sync + 'static,
{
    async fn get_market_data(&self, trading_pair: &str, periods: usize) -> SigmaXResult<Vec<f64>> {
        // 尝试从缓存获取
        let cache_key = format!("{}:{}", trading_pair, periods);
        if let Some(data) = self.cache_manager.cache().get_market_data(&cache_key).await {
            return Ok(data);
        }
        
        // 从数据库获取
        let data = self.provider.get_historical_prices(trading_pair, periods).await?;
        
        // 更新缓存
        self.cache_manager.cache().set_market_data(&cache_key, data.clone()).await;
        
        Ok(data)
    }
    
    async fn get_liquidity_data(&self, trading_pair: &str) -> SigmaXResult<(f64, f64, f64)> {
        // 尝试从缓存获取
        if let Some(data) = self.cache_manager.cache().get_liquidity_data(trading_pair).await {
            return Ok(data);
        }
        
        // 从数据库获取
        let data = match self.provider.get_market_liquidity_data(trading_pair, None).await? {
            Some(data) => data,
            None => (0.0, 0.0, 0.0),
        };
        
        // 更新缓存
        self.cache_manager.cache().set_liquidity_data(trading_pair, data).await;
        
        Ok(data)
    }
    
    async fn get_drawdown_data(&self, strategy_type: &str) -> SigmaXResult<(f64, f64, f64)> {
        // 尝试从缓存获取
        if let Some(data) = self.cache_manager.cache().get_drawdown_data(strategy_type).await {
            return Ok(data);
        }
        
        // 获取策略ID
        let strategy_ids = self.provider.get_strategy_ids_by_type(strategy_type).await?;
        
        if strategy_ids.is_empty() {
            return Ok((0.0, 0.0, 0.0));
        }
        
        // 从数据库获取
        let data = self.provider.get_portfolio_drawdown_data(&strategy_ids).await?;
        
        // 更新缓存
        self.cache_manager.cache().set_drawdown_data(strategy_type, data).await;
        
        Ok(data)
    }
    
    async fn get_risk_metrics(&self, strategy_id: Uuid) -> SigmaXResult<RiskMetrics> {
        // 尝试从缓存获取
        if let Some(metrics) = self.cache_manager.cache().get_risk_metrics(strategy_id).await {
            return Ok(metrics);
        }
        
        // 创建默认风险指标
        let metrics = RiskMetrics::default();
        
        // 更新缓存
        self.cache_manager.cache().set_risk_metrics(strategy_id, metrics.clone()).await;
        
        Ok(metrics)
    }
    
    async fn get_trade_count(&self, strategy_type: &str, exclude_cancelled: bool) -> SigmaXResult<u64> {
        // 尝试从缓存获取
        let cache_key = format!("{}:{}", strategy_type, exclude_cancelled);
        if let Some(count) = self.cache_manager.cache().get_trade_count(&cache_key).await {
            return Ok(count);
        }
        
        // 获取策略ID
        let strategy_ids = self.provider.get_strategy_ids_by_type(strategy_type).await?;
        
        if strategy_ids.is_empty() {
            return Ok(0);
        }
        
        // 从数据库获取
        let count = self.provider.get_trade_count_for_strategies_today_with_filter(&strategy_ids, exclude_cancelled).await?;
        
        // 更新缓存
        self.cache_manager.cache().set_trade_count(&cache_key, count).await;
        
        Ok(count)
    }
    
    async fn get_last_order_time(&self, trading_pair: Option<&str>, strategy_type: Option<&str>) -> SigmaXResult<Option<DateTime<Utc>>> {
        // 生成缓存键
        let cache_key = match (trading_pair, strategy_type) {
            (Some(pair), Some(strategy)) => format!("{}:{}", pair, strategy),
            (Some(pair), None) => format!("{}:global", pair),
            (None, Some(strategy)) => format!("global:{}", strategy),
            (None, None) => "global:global".to_string(),
        };
        
        // 尝试从缓存获取
        if let Some(time) = self.cache_manager.cache().get_last_order_time(&cache_key).await {
            return Ok(Some(time));
        }
        
        // 从数据库获取
        let time = match trading_pair {
            Some(pair) => self.provider.get_last_order_time_for_pair(pair, strategy_type).await?,
            None => self.provider.get_last_order_time_global(strategy_type).await?,
        };
        
        // 更新缓存
        if let Some(time_value) = time {
            self.cache_manager.cache().set_last_order_time(&cache_key, time_value).await;
        }
        
        Ok(time)
    }
}

/// 扩展的统一风控仓储接口
/// 
/// 用于缓存数据提供者
pub trait UnifiedRiskRepositoryExt: crate::unified_engine::UnifiedRiskRepository {}

// 为所有实现了 UnifiedRiskRepository 的类型自动实现 UnifiedRiskRepositoryExt
impl<T: crate::unified_engine::UnifiedRiskRepository + ?Sized> UnifiedRiskRepositoryExt for T {}