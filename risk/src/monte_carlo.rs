//! 蒙特卡洛模拟
//! 
//! 实现随机情景生成和风险评估

use sigmax_core::{SigmaXResult, SigmaXError};
use rust_decimal::Decimal;
use std::collections::HashMap;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use tracing::{info, warn, debug};
use rand::{Rng, SeedableRng};
use rand_chacha::ChaCha8Rng;

/// 蒙特卡洛模拟结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonteCarloResult {
    /// 模拟ID
    pub simulation_id: Uuid,
    /// 模拟参数
    pub simulation_params: MonteCarloParams,
    /// 模拟统计
    pub statistics: SimulationStatistics,
    /// 风险指标
    pub risk_metrics: MonteCarloRiskMetrics,
    /// 分位数结果
    pub percentile_results: HashMap<String, f64>,
    /// 路径统计
    pub path_statistics: PathStatistics,
    /// 模拟时间
    pub simulated_at: DateTime<Utc>,
    /// 计算时间（毫秒）
    pub computation_time_ms: u64,
}

/// 蒙特卡洛参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonteCarloParams {
    /// 模拟次数
    pub num_simulations: u32,
    /// 时间步长（天）
    pub time_steps: u32,
    /// 时间范围（天）
    pub time_horizon_days: u32,
    /// 置信度水平
    pub confidence_levels: Vec<f64>,
    /// 随机种子
    pub random_seed: Option<u64>,
    /// 模型类型
    pub model_type: StochasticModel,
    /// 是否包含跳跃
    pub include_jumps: bool,
    /// 是否包含相关性
    pub include_correlation: bool,
}

/// 随机模型类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StochasticModel {
    /// 几何布朗运动
    GeometricBrownianMotion,
    /// 跳跃扩散模型
    JumpDiffusion,
    /// Heston随机波动率模型
    HestonStochasticVolatility,
    /// 均值回归模型
    MeanReversion,
    /// GARCH模型
    GARCH,
}

/// 模拟统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimulationStatistics {
    /// 平均收益率
    pub mean_return: f64,
    /// 收益率标准差
    pub return_std: f64,
    /// 偏度
    pub skewness: f64,
    /// 峰度
    pub kurtosis: f64,
    /// 最大收益
    pub max_return: f64,
    /// 最小收益
    pub min_return: f64,
    /// 正收益概率
    pub positive_return_probability: f64,
}

/// 蒙特卡洛风险指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonteCarloRiskMetrics {
    /// VaR (各置信度)
    pub var_estimates: HashMap<String, f64>,
    /// CVaR (各置信度)
    pub cvar_estimates: HashMap<String, f64>,
    /// 期望短缺
    pub expected_shortfall: f64,
    /// 最大回撤分布
    pub max_drawdown_distribution: DrawdownDistribution,
    /// 破产概率
    pub ruin_probability: f64,
    /// 达到目标概率
    pub target_achievement_probability: f64,
}

/// 回撤分布
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DrawdownDistribution {
    /// 平均最大回撤
    pub mean_max_drawdown: f64,
    /// 最大回撤标准差
    pub max_drawdown_std: f64,
    /// 最大回撤分位数
    pub max_drawdown_percentiles: HashMap<String, f64>,
}

/// 路径统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PathStatistics {
    /// 平均路径长度
    pub average_path_length: f64,
    /// 趋势持续性
    pub trend_persistence: f64,
    /// 波动聚集性
    pub volatility_clustering: f64,
    /// 均值回归强度
    pub mean_reversion_strength: f64,
}

/// 资产参数
#[derive(Debug, Clone)]
pub struct AssetParameters {
    /// 资产名称
    pub asset_name: String,
    /// 初始价格
    pub initial_price: Decimal,
    /// 预期收益率（年化）
    pub expected_return: f64,
    /// 波动率（年化）
    pub volatility: f64,
    /// 跳跃强度（每年）
    pub jump_intensity: f64,
    /// 跳跃大小均值
    pub jump_size_mean: f64,
    /// 跳跃大小标准差
    pub jump_size_std: f64,
    /// 均值回归速度
    pub mean_reversion_speed: f64,
    /// 长期均值
    pub long_term_mean: f64,
}

/// 相关性矩阵
#[derive(Debug, Clone)]
pub struct CorrelationMatrix {
    /// 资产名称列表
    pub asset_names: Vec<String>,
    /// 相关性矩阵
    pub correlation_matrix: Vec<Vec<f64>>,
}

/// 蒙特卡洛模拟引擎
pub struct MonteCarloSimulator {
    /// 随机数生成器
    rng: ChaCha8Rng,
    /// 资产参数
    asset_parameters: HashMap<String, AssetParameters>,
    /// 相关性矩阵
    correlation_matrix: Option<CorrelationMatrix>,
}

impl MonteCarloSimulator {
    /// 创建新的蒙特卡洛模拟器
    pub fn new(seed: Option<u64>) -> Self {
        let rng = match seed {
            Some(s) => ChaCha8Rng::seed_from_u64(s),
            None => ChaCha8Rng::from_entropy(),
        };
        
        Self {
            rng,
            asset_parameters: HashMap::new(),
            correlation_matrix: None,
        }
    }

    /// 添加资产参数
    pub fn add_asset_parameters(&mut self, params: AssetParameters) {
        self.asset_parameters.insert(params.asset_name.clone(), params);
    }

    /// 设置相关性矩阵
    pub fn set_correlation_matrix(&mut self, correlation_matrix: CorrelationMatrix) {
        self.correlation_matrix = Some(correlation_matrix);
    }

    /// 运行蒙特卡洛模拟
    pub fn run_simulation(&mut self, params: MonteCarloParams) -> SigmaXResult<MonteCarloResult> {
        let start_time = std::time::Instant::now();
        
        if self.asset_parameters.is_empty() {
            return Err(SigmaXError::InvalidOperation(
                "没有设置资产参数".to_string()
            ));
        }
        
        info!("开始蒙特卡洛模拟: {} 次模拟, {} 天时间范围", 
              params.num_simulations, params.time_horizon_days);
        
        // 生成所有模拟路径
        let simulation_paths = self.generate_simulation_paths(&params)?;
        
        // 计算统计指标
        let statistics = self.calculate_simulation_statistics(&simulation_paths)?;
        
        // 计算风险指标
        let risk_metrics = self.calculate_risk_metrics(&simulation_paths, &params)?;
        
        // 计算分位数结果
        let percentile_results = self.calculate_percentile_results(&simulation_paths, &params)?;
        
        // 计算路径统计
        let path_statistics = self.calculate_path_statistics(&simulation_paths)?;
        
        let computation_time_ms = start_time.elapsed().as_millis() as u64;
        
        let result = MonteCarloResult {
            simulation_id: Uuid::new_v4(),
            simulation_params: params,
            statistics,
            risk_metrics,
            percentile_results,
            path_statistics,
            simulated_at: Utc::now(),
            computation_time_ms,
        };
        
        info!("蒙特卡洛模拟完成，耗时 {} ms", computation_time_ms);
        
        Ok(result)
    }

    /// 生成模拟路径
    fn generate_simulation_paths(&mut self, params: &MonteCarloParams) -> SigmaXResult<Vec<Vec<f64>>> {
        let mut all_paths = Vec::new();
        let dt = 1.0 / 365.0; // 日时间步长
        let num_steps = params.time_horizon_days as usize;
        
        for simulation_idx in 0..params.num_simulations {
            if simulation_idx % 1000 == 0 {
                debug!("生成模拟路径: {}/{}", simulation_idx, params.num_simulations);
            }
            
            let path = match params.model_type {
                StochasticModel::GeometricBrownianMotion => {
                    self.generate_gbm_path(num_steps, dt, params)?
                }
                StochasticModel::JumpDiffusion => {
                    self.generate_jump_diffusion_path(num_steps, dt, params)?
                }
                StochasticModel::MeanReversion => {
                    self.generate_mean_reversion_path(num_steps, dt, params)?
                }
                _ => {
                    warn!("模型类型 {:?} 暂未实现，使用几何布朗运动", params.model_type);
                    self.generate_gbm_path(num_steps, dt, params)?
                }
            };
            
            all_paths.push(path);
        }
        
        Ok(all_paths)
    }

    /// 生成几何布朗运动路径
    fn generate_gbm_path(&mut self, num_steps: usize, dt: f64, _params: &MonteCarloParams) -> SigmaXResult<Vec<f64>> {
        // 简化实现：使用第一个资产的参数
        let asset_param = self.asset_parameters.values().next()
            .ok_or_else(|| SigmaXError::InvalidOperation("没有资产参数".to_string()))?;
        
        let mut path = vec![0.0; num_steps + 1]; // 对数收益率路径
        let mu = asset_param.expected_return;
        let sigma = asset_param.volatility;
        
        for i in 1..=num_steps {
            let z = self.generate_normal_random();
            let drift = (mu - 0.5 * sigma * sigma) * dt;
            let diffusion = sigma * (dt.sqrt()) * z;
            path[i] = path[i-1] + drift + diffusion;
        }
        
        // 转换为价格收益率
        let returns: Vec<f64> = path.windows(2)
            .map(|window| (window[1] - window[0]).exp() - 1.0)
            .collect();
        
        Ok(returns)
    }

    /// 生成跳跃扩散路径
    fn generate_jump_diffusion_path(&mut self, num_steps: usize, dt: f64, _params: &MonteCarloParams) -> SigmaXResult<Vec<f64>> {
        let asset_param = self.asset_parameters.values().next()
            .ok_or_else(|| SigmaXError::InvalidOperation("没有资产参数".to_string()))?;
        
        let mut path = vec![0.0; num_steps + 1];
        let mu = asset_param.expected_return;
        let sigma = asset_param.volatility;
        let lambda = asset_param.jump_intensity;
        let jump_mean = asset_param.jump_size_mean;
        let jump_std = asset_param.jump_size_std;
        
        for i in 1..=num_steps {
            // 扩散部分
            let z = self.generate_normal_random();
            let drift = (mu - 0.5 * sigma * sigma) * dt;
            let diffusion = sigma * (dt.sqrt()) * z;
            
            // 跳跃部分
            let jump_prob = lambda * dt;
            let jump = if self.rng.gen::<f64>() < jump_prob {
                let jump_size = self.generate_normal_random() * jump_std + jump_mean;
                jump_size
            } else {
                0.0
            };
            
            path[i] = path[i-1] + drift + diffusion + jump;
        }
        
        let returns: Vec<f64> = path.windows(2)
            .map(|window| (window[1] - window[0]).exp() - 1.0)
            .collect();
        
        Ok(returns)
    }

    /// 生成均值回归路径
    fn generate_mean_reversion_path(&mut self, num_steps: usize, dt: f64, _params: &MonteCarloParams) -> SigmaXResult<Vec<f64>> {
        let asset_param = self.asset_parameters.values().next()
            .ok_or_else(|| SigmaXError::InvalidOperation("没有资产参数".to_string()))?;
        
        let mut path = vec![asset_param.long_term_mean; num_steps + 1];
        let kappa = asset_param.mean_reversion_speed;
        let theta = asset_param.long_term_mean;
        let sigma = asset_param.volatility;
        
        for i in 1..=num_steps {
            let z = self.generate_normal_random();
            let mean_reversion = kappa * (theta - path[i-1]) * dt;
            let diffusion = sigma * (dt.sqrt()) * z;
            path[i] = path[i-1] + mean_reversion + diffusion;
        }
        
        let returns: Vec<f64> = path.windows(2)
            .map(|window| window[1] / window[0] - 1.0)
            .collect();
        
        Ok(returns)
    }

    /// 生成标准正态随机数
    fn generate_normal_random(&mut self) -> f64 {
        // Box-Muller变换
        static mut SPARE: Option<f64> = None;
        static mut HAS_SPARE: bool = false;
        
        unsafe {
            if HAS_SPARE {
                HAS_SPARE = false;
                return SPARE.unwrap();
            }
            
            HAS_SPARE = true;
            let u = self.rng.gen::<f64>();
            let v = self.rng.gen::<f64>();
            let mag = (-2.0 * u.ln()).sqrt();
            SPARE = Some(mag * (2.0 * std::f64::consts::PI * v).cos());
            mag * (2.0 * std::f64::consts::PI * v).sin()
        }
    }

    /// 计算模拟统计
    fn calculate_simulation_statistics(&self, paths: &[Vec<f64>]) -> SigmaXResult<SimulationStatistics> {
        if paths.is_empty() {
            return Err(SigmaXError::InvalidOperation("没有模拟路径".to_string()));
        }
        
        // 计算每条路径的总收益率
        let total_returns: Vec<f64> = paths.iter()
            .map(|path| {
                path.iter().fold(1.0, |acc, &r| acc * (1.0 + r)) - 1.0
            })
            .collect();
        
        let n = total_returns.len() as f64;
        let mean_return = total_returns.iter().sum::<f64>() / n;
        
        let variance = total_returns.iter()
            .map(|&r| (r - mean_return).powi(2))
            .sum::<f64>() / (n - 1.0);
        let return_std = variance.sqrt();
        
        // 计算偏度和峰度
        let skewness = total_returns.iter()
            .map(|&r| ((r - mean_return) / return_std).powi(3))
            .sum::<f64>() / n;
        
        let kurtosis = total_returns.iter()
            .map(|&r| ((r - mean_return) / return_std).powi(4))
            .sum::<f64>() / n - 3.0; // 超额峰度
        
        let max_return = total_returns.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let min_return = total_returns.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        
        let positive_return_probability = total_returns.iter()
            .filter(|&&r| r > 0.0)
            .count() as f64 / n;
        
        Ok(SimulationStatistics {
            mean_return,
            return_std,
            skewness,
            kurtosis,
            max_return,
            min_return,
            positive_return_probability,
        })
    }

    /// 计算风险指标
    fn calculate_risk_metrics(&self, paths: &[Vec<f64>], params: &MonteCarloParams) -> SigmaXResult<MonteCarloRiskMetrics> {
        let total_returns: Vec<f64> = paths.iter()
            .map(|path| {
                path.iter().fold(1.0, |acc, &r| acc * (1.0 + r)) - 1.0
            })
            .collect();
        
        let mut sorted_returns = total_returns.clone();
        sorted_returns.sort_by(|a, b| a.partial_cmp(b).unwrap());
        
        let mut var_estimates = HashMap::new();
        let mut cvar_estimates = HashMap::new();
        
        for &confidence_level in &params.confidence_levels {
            let alpha = 1.0 - confidence_level;
            let var_index = (alpha * sorted_returns.len() as f64) as usize;
            
            if var_index < sorted_returns.len() {
                let var = -sorted_returns[var_index];
                var_estimates.insert(format!("{:.1}%", confidence_level * 100.0), var);
                
                // CVaR计算
                let tail_returns = &sorted_returns[0..var_index];
                let cvar = if !tail_returns.is_empty() {
                    -tail_returns.iter().sum::<f64>() / tail_returns.len() as f64
                } else {
                    var
                };
                cvar_estimates.insert(format!("{:.1}%", confidence_level * 100.0), cvar);
            }
        }
        
        let expected_shortfall = if !sorted_returns.is_empty() {
            let worst_5_percent = (sorted_returns.len() as f64 * 0.05) as usize;
            -sorted_returns[0..worst_5_percent.max(1)].iter().sum::<f64>() / worst_5_percent.max(1) as f64
        } else {
            0.0
        };
        
        // 计算最大回撤分布
        let max_drawdown_distribution = self.calculate_drawdown_distribution(paths)?;
        
        // 破产概率（损失超过50%）
        let ruin_probability = sorted_returns.iter()
            .filter(|&&r| r < -0.5)
            .count() as f64 / sorted_returns.len() as f64;
        
        // 达到目标概率（收益超过10%）
        let target_achievement_probability = sorted_returns.iter()
            .filter(|&&r| r > 0.1)
            .count() as f64 / sorted_returns.len() as f64;
        
        Ok(MonteCarloRiskMetrics {
            var_estimates,
            cvar_estimates,
            expected_shortfall,
            max_drawdown_distribution,
            ruin_probability,
            target_achievement_probability,
        })
    }

    /// 计算回撤分布
    fn calculate_drawdown_distribution(&self, paths: &[Vec<f64>]) -> SigmaXResult<DrawdownDistribution> {
        let max_drawdowns: Vec<f64> = paths.iter()
            .map(|path| {
                let mut cumulative_return = 1.0;
                let mut peak = 1.0f64;
                let mut max_drawdown = 0.0f64;
                
                for &daily_return in path {
                    cumulative_return *= 1.0 + daily_return;
                    peak = peak.max(cumulative_return);
                    let drawdown = (peak - cumulative_return) / peak;
                    max_drawdown = max_drawdown.max(drawdown);
                }
                
                max_drawdown
            })
            .collect();
        
        let n = max_drawdowns.len() as f64;
        let mean_max_drawdown = max_drawdowns.iter().sum::<f64>() / n;
        
        let variance = max_drawdowns.iter()
            .map(|&dd| (dd - mean_max_drawdown).powi(2))
            .sum::<f64>() / (n - 1.0);
        let max_drawdown_std = variance.sqrt();
        
        let mut sorted_drawdowns = max_drawdowns.clone();
        sorted_drawdowns.sort_by(|a, b| a.partial_cmp(b).unwrap());
        
        let mut max_drawdown_percentiles = HashMap::new();
        let percentiles = vec![50.0, 75.0, 90.0, 95.0, 99.0];
        
        for percentile in percentiles {
            let index = (percentile / 100.0 * sorted_drawdowns.len() as f64) as usize;
            let index = index.min(sorted_drawdowns.len() - 1);
            max_drawdown_percentiles.insert(
                format!("{:.0}%", percentile),
                sorted_drawdowns[index]
            );
        }
        
        Ok(DrawdownDistribution {
            mean_max_drawdown,
            max_drawdown_std,
            max_drawdown_percentiles,
        })
    }

    /// 计算分位数结果
    fn calculate_percentile_results(&self, paths: &[Vec<f64>], _params: &MonteCarloParams) -> SigmaXResult<HashMap<String, f64>> {
        let total_returns: Vec<f64> = paths.iter()
            .map(|path| {
                path.iter().fold(1.0, |acc, &r| acc * (1.0 + r)) - 1.0
            })
            .collect();
        
        let mut sorted_returns = total_returns;
        sorted_returns.sort_by(|a, b| a.partial_cmp(b).unwrap());
        
        let mut percentile_results = HashMap::new();
        let percentiles = vec![1.0, 5.0, 10.0, 25.0, 50.0, 75.0, 90.0, 95.0, 99.0];
        
        for percentile in percentiles {
            let index = (percentile / 100.0 * sorted_returns.len() as f64) as usize;
            let index = index.min(sorted_returns.len() - 1);
            percentile_results.insert(
                format!("P{:.0}", percentile),
                sorted_returns[index]
            );
        }
        
        Ok(percentile_results)
    }

    /// 计算路径统计
    fn calculate_path_statistics(&self, paths: &[Vec<f64>]) -> SigmaXResult<PathStatistics> {
        if paths.is_empty() {
            return Ok(PathStatistics {
                average_path_length: 0.0,
                trend_persistence: 0.0,
                volatility_clustering: 0.0,
                mean_reversion_strength: 0.0,
            });
        }
        
        let average_path_length = paths[0].len() as f64;
        
        // 简化的趋势持续性计算
        let trend_persistence = paths.iter()
            .map(|path| {
                if path.len() < 3 {
                    return 0.5; // 默认值
                }
                let trend_changes = path.windows(3)
                    .filter(|window| {
                        let trend1 = window[1] - window[0];
                        let trend2 = window[2] - window[1];
                        trend1 * trend2 < 0.0 // 趋势变化
                    })
                    .count();
                1.0 - (trend_changes as f64 / (path.len() - 2) as f64)
            })
            .sum::<f64>() / paths.len() as f64;
        
        // 简化的波动聚集性计算
        let volatility_clustering = 0.5; // 占位符
        
        // 简化的均值回归强度计算
        let mean_reversion_strength = 0.3; // 占位符
        
        Ok(PathStatistics {
            average_path_length,
            trend_persistence,
            volatility_clustering,
            mean_reversion_strength,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;

    #[test]
    fn test_monte_carlo_simulation() {
        let mut simulator = MonteCarloSimulator::new(Some(42));
        
        // 添加BTC参数
        let btc_params = AssetParameters {
            asset_name: "BTC_USDT".to_string(),
            initial_price: dec!(50000),
            expected_return: 0.15, // 15%年化收益
            volatility: 0.80,      // 80%年化波动率
            jump_intensity: 12.0,  // 每年12次跳跃
            jump_size_mean: -0.02, // 平均-2%跳跃
            jump_size_std: 0.05,   // 5%跳跃标准差
            mean_reversion_speed: 0.5,
            long_term_mean: 0.10,
        };
        
        simulator.add_asset_parameters(btc_params);
        
        let params = MonteCarloParams {
            num_simulations: 1000,
            time_steps: 30,
            time_horizon_days: 30,
            confidence_levels: vec![0.95, 0.99],
            random_seed: Some(42),
            model_type: StochasticModel::GeometricBrownianMotion,
            include_jumps: false,
            include_correlation: false,
        };
        
        let result = simulator.run_simulation(params).unwrap();
        
        assert!(result.statistics.mean_return.is_finite());
        assert!(result.statistics.return_std > 0.0);
        assert!(!result.risk_metrics.var_estimates.is_empty());
        assert!(!result.risk_metrics.cvar_estimates.is_empty());
        assert!(result.risk_metrics.ruin_probability >= 0.0);
        assert!(result.risk_metrics.ruin_probability <= 1.0);
        
        println!("蒙特卡洛模拟结果:");
        println!("平均收益: {:.4}", result.statistics.mean_return);
        println!("收益标准差: {:.4}", result.statistics.return_std);
        println!("VaR 95%: {:.4}", result.risk_metrics.var_estimates.get("95.0%").unwrap_or(&0.0));
        println!("CVaR 95%: {:.4}", result.risk_metrics.cvar_estimates.get("95.0%").unwrap_or(&0.0));
        println!("破产概率: {:.4}", result.risk_metrics.ruin_probability);
    }

    #[test]
    fn test_jump_diffusion_model() {
        let mut simulator = MonteCarloSimulator::new(Some(123));
        
        let btc_params = AssetParameters {
            asset_name: "BTC_USDT".to_string(),
            initial_price: dec!(50000),
            expected_return: 0.10,
            volatility: 0.60,
            jump_intensity: 24.0,
            jump_size_mean: -0.05,
            jump_size_std: 0.10,
            mean_reversion_speed: 0.0,
            long_term_mean: 0.0,
        };
        
        simulator.add_asset_parameters(btc_params);
        
        let params = MonteCarloParams {
            num_simulations: 500,
            time_steps: 7,
            time_horizon_days: 7,
            confidence_levels: vec![0.95],
            random_seed: Some(123),
            model_type: StochasticModel::JumpDiffusion,
            include_jumps: true,
            include_correlation: false,
        };
        
        let result = simulator.run_simulation(params).unwrap();
        
        // 跳跃扩散模型应该产生更高的尾部风险
        assert!(result.statistics.kurtosis > 0.0); // 正超额峰度
        assert!(result.risk_metrics.var_estimates.get("95.0%").unwrap_or(&0.0) > &0.0);
        
        println!("跳跃扩散模型结果:");
        println!("峰度: {:.4}", result.statistics.kurtosis);
        println!("偏度: {:.4}", result.statistics.skewness);
    }
}
