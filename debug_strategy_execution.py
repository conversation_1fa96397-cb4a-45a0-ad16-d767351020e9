#!/usr/bin/env python3
"""
诊断策略执行过程 - 找出为什么没有生成交易
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:8080"
API_BASE = f"{BASE_URL}/api/v1"

def create_debug_backtest():
    """创建用于调试的回测"""
    print("🔍 创建调试回测引擎...")
    
    # 1. 创建引擎
    engine_data = {
        "engine_type": "Backtest",
        "trading_pairs": [{"base": "BNB", "quote": "USDT"}],
        "initial_capital": "10000.00"
    }
    
    response = requests.post(f"{API_BASE}/engines", json=engine_data)
    if response.status_code != 200:
        print(f"❌ 创建引擎失败: {response.status_code} - {response.text}")
        return None
    
    engine_id = response.json()['data']['id']
    print(f"✅ 引擎创建成功: {engine_id}")
    
    # 2. 配置回测 - 使用极简配置以便调试
    config_data = {
        "data_file": "BNB_USDT_1d_60.json",
        "start_time": "2021-01-01T00:00:00Z",
        "end_time": "2021-01-10T23:59:59Z",  # 只用前10天数据
        "initial_capital": "10000.00",
        "trading_pairs": ["BNB_USDT"],
        "timeframe": "1d",
        "strategy_type": "asymmetric_volatility_grid",
        "strategy_config": {
            # 极简配置 - 确保触发
            "base_price": 0.0,  # 使用第一个价格
            "order_amount": 100.0,  # 小订单金额
            "trading_pair": "BNB_USDT",
            "exchange_id": "binance",
            
            # 极宽的网格范围 - 确保触发
            "down_range_start": -0.01,  # -1%，非常容易触发
            "down_range_end": -0.90,    # -90%，覆盖极端情况
            "down_grid_count": 2,       # 只有2个网格
            "down_base_quantity": 1.0,  # 大数量
            "down_grid_distribution": "Linear",
            "down_quantity_model": "Linear",
            
            "up_range_start": 0.01,     # +1%，非常容易触发
            "up_range_end": 10.0,       # +1000%，覆盖极端上涨
            "up_grid_count": 2,         # 只有2个网格
            "up_base_quantity": 1.0,    # 大数量
            "up_grid_distribution": "Linear",
            "up_quantity_model": "Linear",
            
            # 禁用所有复杂功能
            "volatility_window_hours": 24,
            "volatility_multiplier": 1.0,
            "enable_dynamic_volatility": False,
            
            "max_position_amount": 9500.0,
            "max_daily_trades": 1000,
            "stop_loss_config": "Disabled",
            
            "strategy_preset": "VeryAggressive",
            
            # 禁用所有过滤器
            "recenter_threshold_percent": None,
            "regime_filter": None
        }
    }
    
    print("📋 调试回测配置:")
    print(f"  数据文件: {config_data['data_file']}")
    print(f"  时间范围: {config_data['start_time']} 到 {config_data['end_time']}")
    print(f"  网格配置: 下跌 -1% 到 -90%，上涨 +1% 到 +1000%")
    print(f"  网格数量: 下跌2个，上涨2个")
    print(f"  订单金额: ${config_data['strategy_config']['order_amount']}")
    
    response = requests.post(f"{API_BASE}/engines/{engine_id}/backtest/config", json=config_data)
    if response.status_code != 200:
        print(f"❌ 配置回测失败: {response.status_code} - {response.text}")
        return None
    
    print("✅ 回测配置成功")
    
    # 3. 启动引擎
    response = requests.post(f"{API_BASE}/engines/{engine_id}/start")
    if response.status_code != 200:
        print(f"❌ 启动引擎失败: {response.status_code} - {response.text}")
        return None
    
    print("✅ 引擎启动成功")
    return engine_id

def monitor_debug_backtest(engine_id):
    """监控调试回测"""
    print(f"📊 监控调试回测进度...")
    
    start_time = time.time()
    timeout_seconds = 5 * 60  # 5分钟超时
    
    while time.time() - start_time < timeout_seconds:
        try:
            response = requests.get(f"{API_BASE}/engines/{engine_id}/backtest/progress")
            if response.status_code == 200:
                data = response.json()
                progress_data = data['data']
                
                progress = progress_data['progress']['percentage']
                status = progress_data['status']
                current_candle = progress_data['progress']['current_candle']
                total_candles = progress_data['progress']['total_candles']
                
                print(f"进度: {progress:.1f}% ({current_candle}/{total_candles}) - 状态: {status}")
                
                if status == 'completed':
                    print("✅ 调试回测完成!")
                    return True
                elif status == 'failed':
                    print("❌ 调试回测失败!")
                    return False
                    
            else:
                print(f"⚠️ 获取进度失败: {response.status_code}")
                
        except Exception as e:
            print(f"⚠️ 监控异常: {e}")
        
        time.sleep(2)
    
    print("⏰ 监控超时")
    return False

def analyze_debug_result(engine_id):
    """分析调试结果"""
    print("🔍 分析调试结果...")
    
    try:
        # 获取回测结果
        response = requests.get(f"{API_BASE}/engines/{engine_id}/backtest/result")
        if response.status_code != 200:
            print(f"❌ 获取结果失败: {response.status_code} - {response.text}")
            return False
        
        result_data = response.json()['data']
        
        print("=" * 70)
        print("🔍 调试回测结果分析")
        print("=" * 70)
        
        print(f"引擎ID: {result_data['engine_id']}")
        print(f"状态: {result_data['status']}")
        
        # 交易统计
        if 'trading_stats' in result_data:
            stats = result_data['trading_stats']
            total_trades = stats.get('total_trades', 0)
            
            print(f"\n📊 交易统计:")
            print(f"总交易数: {total_trades}")
            
            if total_trades > 0:
                print("🎯 策略成功生成交易!")
                print("✅ 问题已解决!")
            else:
                print("❌ 仍然没有交易")
                print("🔍 需要进一步分析...")
        
        # 获取详细交易记录
        response = requests.get(f"{API_BASE}/engines/{engine_id}/backtest/trades")
        if response.status_code == 200:
            trades_data = response.json()['data']
            trades = trades_data['trades']
            print(f"\n📋 交易记录: {len(trades)} 笔交易")
            
            if len(trades) > 0:
                for i, trade in enumerate(trades):
                    side = trade.get('side', 'N/A')
                    quantity = trade.get('quantity', 'N/A')
                    price = trade.get('price', 'N/A')
                    timestamp = trade.get('timestamp', 'N/A')
                    print(f"  {i+1}. {side} {quantity} @ ${price} ({timestamp})")
        
        print("=" * 70)
        return True
        
    except Exception as e:
        print(f"❌ 分析结果异常: {e}")
        return False

def check_data_file():
    """检查数据文件内容"""
    print("📁 检查数据文件内容...")
    
    try:
        with open("bt_klines/BNB_USDT_1d_60.json", 'r') as f:
            data = json.load(f)
        
        print(f"数据文件包含 {len(data)} 组K线")
        
        if len(data) >= 10:
            print("前10组数据的价格变化:")
            for i in range(10):
                candle = data[i]
                timestamp = candle[0]
                open_price = candle[1]
                high_price = candle[2]
                low_price = candle[3]
                close_price = candle[4]
                
                # 转换时间戳
                import datetime
                dt = datetime.datetime.fromtimestamp(timestamp / 1000)
                
                print(f"  {i+1}. {dt.strftime('%Y-%m-%d')}: 开盘${open_price:.2f}, 收盘${close_price:.2f}")
                
                if i > 0:
                    prev_close = data[i-1][4]
                    change_pct = ((close_price - prev_close) / prev_close) * 100
                    print(f"      变化: {change_pct:+.2f}%")
        
        # 分析价格波动
        prices = [candle[4] for candle in data[:10]]  # 收盘价
        min_price = min(prices)
        max_price = max(prices)
        volatility = ((max_price - min_price) / min_price) * 100
        
        print(f"\n前10天价格分析:")
        print(f"最低价: ${min_price:.2f}")
        print(f"最高价: ${max_price:.2f}")
        print(f"波动范围: {volatility:.2f}%")
        
        if volatility > 1:
            print("✅ 数据有足够的波动性，应该能触发网格")
        else:
            print("⚠️ 数据波动性较小，可能难以触发网格")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查数据文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 开始策略执行诊断")
    print("🎯 目标：找出为什么策略不生成交易")
    print("=" * 70)
    
    # 1. 检查数据文件
    if not check_data_file():
        print("❌ 数据文件检查失败")
        return
    
    print("\n" + "=" * 70)
    
    # 2. 创建调试回测
    engine_id = create_debug_backtest()
    if not engine_id:
        print("❌ 创建调试回测失败")
        return
    
    # 3. 监控进度
    success = monitor_debug_backtest(engine_id)
    if not success:
        print("⚠️ 回测可能未完成，但继续分析结果...")
    
    # 4. 分析结果
    analyze_debug_result(engine_id)
    
    print("\n🎉 策略执行诊断完成!")
    print("💡 如果仍然没有交易，问题可能在策略内部逻辑")

if __name__ == "__main__":
    main()
