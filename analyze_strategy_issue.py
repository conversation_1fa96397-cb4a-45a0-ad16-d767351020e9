#!/usr/bin/env python3
"""
分析策略问题 - 找出为什么没有生成交易的根本原因
"""

import requests
import json

def analyze_strategy_issue():
    """分析策略问题"""
    engine_id = "9c503833-d30f-4e7d-bdba-6d46107f7801"
    base_url = "http://127.0.0.1:8080/api/v1"
    
    print("🔍 分析策略问题")
    print("🎯 找出为什么没有生成交易的根本原因")
    print("=" * 70)
    
    # 1. 检查回测配置中的策略配置
    print("\n📋 1. 检查回测配置中的策略配置")
    try:
        config_response = requests.get(f"{base_url}/engines/{engine_id}/backtest/config")
        if config_response.status_code == 200:
            config_data = config_response.json()["data"]
            print("✅ 获取配置成功")
            
            # 检查是否有策略配置
            if "strategy_config" in config_data:
                print("✅ 找到策略配置")
                strategy_config = config_data["strategy_config"]
                print(f"策略配置: {json.dumps(strategy_config, indent=2, ensure_ascii=False)}")
            else:
                print("❌ 没有找到策略配置！")
                print("🔍 这可能是问题的根源 - 回测配置中缺少策略配置")
                
                # 显示完整配置
                print("\n📋 完整配置:")
                print(json.dumps(config_data, indent=2, ensure_ascii=False))
                
        else:
            print(f"❌ 获取配置失败: {config_response.status_code}")
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
    
    # 2. 检查支持的策略列表
    print("\n📈 2. 检查支持的策略列表")
    try:
        strategies_response = requests.get(f"{base_url}/strategies/supported")
        if strategies_response.status_code == 200:
            strategies_data = strategies_response.json()["data"]
            print("✅ 获取策略列表成功")
            print(f"支持的策略: {strategies_data}")
            
            # 检查我们需要的策略
            needed_strategies = ["grid", "adaptive_volatility_grid", "asymmetric_volatility_grid"]
            for strategy in needed_strategies:
                if strategy in strategies_data:
                    print(f"✅ 支持策略: {strategy}")
                else:
                    print(f"❌ 不支持策略: {strategy}")
        else:
            print(f"❌ 获取策略列表失败: {strategies_response.status_code}")
            print(f"响应: {strategies_response.text}")
    except Exception as e:
        print(f"❌ 检查策略列表失败: {e}")
    
    # 3. 分析问题并提供解决方案
    print("\n🔍 3. 问题分析")
    print("=" * 70)
    
    print("🔍 根据诊断结果，主要问题可能是:")
    print("1. ❌ 策略配置丢失 - 回测配置中没有包含策略配置")
    print("2. ❌ 策略类型不匹配 - 使用的策略名称与实际支持的不一致")
    print("3. ❌ 策略参数缺失 - 策略配置中缺少必要的参数")
    
    print("\n💡 解决方案:")
    print("1. 确保回测配置包含完整的策略配置")
    print("2. 使用正确的策略名称 (asymmetric_volatility_grid)")
    print("3. 提供完整的策略参数")
    
    # 4. 生成修正的配置
    print("\n🔧 4. 生成修正的配置")
    corrected_config = {
        "data_file": "BNB_USDT_1d_60.json",
        "start_time": "2021-01-01T00:00:00Z",
        "end_time": "2021-02-28T23:59:59Z",
        "initial_capital": "5000.00",
        "trading_pairs": ["BNB_USDT"],
        "timeframe": "1d",
        "strategy_config": {
            "type": "asymmetric_volatility_grid",  # 使用正确的策略名称
            "config": {
                # 基础配置
                "base_price": 50.0,  # 基准价格，将根据第一个K线调整
                "order_amount": 100.0,
                "trading_pair": "BNB_USDT",
                "exchange_id": "Simulator",
                
                # 下跌区间配置（吸筹区）
                "down_range_start": -0.02,  # -2%
                "down_range_end": -0.20,    # -20%
                "down_grid_count": 10,
                "down_base_quantity": 0.1,
                
                # 上涨区间配置（止盈区）
                "up_range_start": 0.02,     # +2%
                "up_range_end": 0.30,       # +30%
                "up_grid_count": 8,
                "up_base_quantity": 0.1,
                
                # 波动率配置
                "volatility_window_hours": 24,
                "volatility_multiplier": 1.0,
                "enable_dynamic_volatility": True,
                
                # 风险控制
                "max_position_amount": 4000.0,
                "max_daily_trades": 50,
                
                # 预设类型
                "strategy_preset": "Balanced"
            }
        }
    }
    
    print("📋 修正后的配置:")
    print(json.dumps(corrected_config, indent=2, ensure_ascii=False))
    
    return corrected_config

def test_corrected_config():
    """测试修正后的配置"""
    print("\n🧪 5. 测试修正后的配置")
    base_url = "http://127.0.0.1:8080/api/v1"
    
    # 获取修正的配置
    corrected_config = analyze_strategy_issue()
    
    try:
        # 创建新的引擎
        print("\n🔧 创建新的回测引擎...")
        engine_response = requests.post(f"{base_url}/engines", json={
            "engine_type": "Backtest",
            "trading_pairs": [{"base": "BNB", "quote": "USDT"}],
            "initial_capital": "5000.00"
        })
        
        if engine_response.status_code != 200:
            print(f"❌ 创建引擎失败: {engine_response.status_code} - {engine_response.text}")
            return False
            
        engine_data = engine_response.json()
        engine_id = engine_data["data"]["id"]
        print(f"✅ 引擎创建成功: {engine_id}")
        
        # 配置回测参数
        print("\n⚙️ 配置回测参数...")
        config_response = requests.post(f"{base_url}/engines/{engine_id}/backtest/config", 
                                      json=corrected_config)
        
        if config_response.status_code != 200:
            print(f"❌ 配置失败: {config_response.status_code} - {config_response.text}")
            return False
            
        print("✅ 回测配置成功")
        
        # 启动回测
        print("\n🚀 启动回测...")
        start_response = requests.post(f"{base_url}/engines/{engine_id}/start")
        
        if start_response.status_code != 200:
            print(f"❌ 启动失败: {start_response.status_code} - {start_response.text}")
            return False
            
        print("✅ 回测启动成功")
        print(f"🎯 新引擎ID: {engine_id}")
        print("💡 请等待回测完成后检查结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    # 先分析问题
    analyze_strategy_issue()
    
    # 然后测试修正方案
    test_corrected_config()
