//! SigmaX Risk Management Module
//!
//! 企业级风险管理模块，提供完整的风险控制和分析能力
//!
//! ## 架构设计
//!
//! ### 核心组件
//! - **UnifiedRiskEngine**: 统一风险控制引擎，所有风险检查的核心
//! - **规则系统**: 可扩展的风险规则框架
//! - **监控系统**: 实时风险监控和预警
//! - **分析工具**: 高级风险分析和压力测试
//!
//! ### 设计原则
//! - **统一接口**: 所有风险功能通过 UnifiedRiskEngine 统一访问
//! - **模块化**: 每个功能模块职责单一，可独立测试
//! - **可扩展**: 支持自定义规则和分析模型
//! - **高性能**: 异步处理，支持高并发场景

// ============================================================================
// 核心风险引擎 - 主要对外接口
// ============================================================================
pub mod unified_engine;
pub mod rule_implementations;
pub mod rule_helpers;
pub mod repositories;
pub mod unified_interface;
pub mod data_cache;
pub mod async_service;
pub mod real_time_monitor;

// ============================================================================
// 测试模块
// ============================================================================

// ============================================================================
// 核心架构模块
// ============================================================================
pub mod core;
pub mod services;

// ============================================================================
// 高级风险分析模块
// ============================================================================
pub mod advanced_metrics;
pub mod correlation_model;
pub mod liquidity_model;
pub mod credit_model;
pub mod metrics;

// ============================================================================
// 实时监控和预警系统
// ============================================================================
pub mod alert_system;
pub mod notification;
pub mod auto_response;

// ============================================================================
// 自动响应系统模块
// ============================================================================
pub mod response_actions;
pub mod response_rules;
pub mod response_executor;
pub mod response_interfaces;

// ============================================================================
// 分析工具模块
// ============================================================================
pub mod stress_test;
pub mod monte_carlo;
pub mod sensitivity;

// ============================================================================
// 配置管理模块
// ============================================================================
pub mod config_mapper;
pub mod hot_reload;

// ============================================================================
// V2 微内核架构 - 新一代风控系统
// ============================================================================
pub mod v2;

// ============================================================================
// 传统模块已移除 - 请使用新的统一接口
// ============================================================================


// ============================================================================
// 主要公共接口：统一风控引擎
// ============================================================================

/// 统一风控引擎 - 系统的主要风险管理接口
pub use unified_engine::{
    UnifiedRiskEngine, UnifiedRiskRule, UnifiedRiskRepository,
    RuleExecutionContext, RuleResult, RiskCheckResult, FailedRule,
    RuleExecutionRecord
};

// 仅在测试时导出模拟仓库
#[cfg(test)]
pub use unified_engine::tests::MockUnifiedRiskRepository;

/// 统一风控仓储实现
pub use repositories::SqlUnifiedRiskRepository;

// ============================================================================
// 核心架构接口
// ============================================================================

/// 核心风险管理类型和特征
pub use core::{
    RiskEngine, RiskCheckRequest, CoreRiskAssessment,
    RiskRule, RiskMetrics, RiskAlert, RuleManager
};

// 重新导出 core 模块的 RiskCheckResult，避免与 unified_engine 的冲突
pub use core::types::RiskCheckResult as CoreRiskCheckResult;

/// 风险管理服务
pub use services::{
    RiskService, RuleManager as ServiceRuleManager, DefaultRuleManager,
    RiskMonitorService, DefaultRiskMonitorService
};

// ============================================================================
// 配置管理接口
// ============================================================================

/// 统一风险配置类型 (从 sigmax_core 重新导出)
pub use sigmax_core::config::risk::{
    RiskManagementConfig, RiskConfigParameters, BasicRiskParams,
    PositionRiskParams, TradingRiskParams, TimeBasedRiskParams,
    MarketRiskParams, AdvancedRiskParams
};

pub use config_mapper::{
    RiskConfigMapper, StrategyRiskConfig, ConfigChange, ConfigDiffReport, ChangeType
};
pub use hot_reload::{
    ConfigHotReloadManager, ConfigChangeEvent, ConfigStats
};

// ============================================================================
// 风险指标计算接口 - 重构后的分层架构
// ============================================================================

/// 新的分层风险指标计算系统
pub use metrics::{
    // 统一计算器
    MetricsCalculator, ComprehensiveRiskMetrics,
    // 基础指标
    BasicRiskMetrics, BasicRiskCalculator,
    // 比率指标
    RatioMetrics, RatioCalculator,
    // 回撤指标
    DrawdownMetrics, DrawdownCalculator, DrawdownDistribution,
    // 市场指标
    MarketMetrics, MarketCalculator,
};

/// 高级风险分析（保留兼容性）
pub use advanced_metrics::{
    AdvancedRiskMetrics, AdvancedRiskCalculator
};

pub use correlation_model::{
    CorrelationRiskMetrics
};

pub use liquidity_model::{
    LiquidityRiskMetrics
};

pub use credit_model::{
    CreditRiskMetrics
};

pub use alert_system::{
    RealTimeAlertSystem, AlertRule, AlertSeverity
};

pub use notification::{
    NotificationConfig, NotificationTemplate
};

// 自动响应系统 - 主要接口
pub use auto_response::{
    AutoRiskResponseSystem
};

// 自动响应系统 - 详细组件
pub use response_actions::{
    AutoResponseAction, LogLevel, ActionRiskLevel
};

pub use response_rules::{
    AutoResponseRule, TriggerCondition, ThresholdCondition,
    TimeCondition, ComparisonOperator
};

pub use response_executor::{
    ResponseExecutor, ResponseExecutionRecord, ExecutionStatus,
    ActionResult, ResponseStatistics, ActionTypeStats
};

pub use response_interfaces::{
    StrategyController, OrderManager, NotificationSender,
    StrategyStatus, NotificationChannel
};

pub use stress_test::{
    StressTestEngine, StressTestResult, HistoricalScenario, ScenarioType,
    MarketShock, PortfolioSnapshot, StressTestConfig, RecoveryEstimationMethod
};

pub use monte_carlo::{
    MonteCarloSimulator, MonteCarloResult, MonteCarloParams, StochasticModel,
    SimulationStatistics, MonteCarloRiskMetrics,
    PathStatistics, AssetParameters, CorrelationMatrix
};

pub use sensitivity::{
    SensitivityAnalyzer, SensitivityAnalysisResult, SensitivityParams,
    ParameterSensitivity, RiskFactorSensitivity, GreeksAnalysis,
    ScenarioSensitivity, SensitivityLevel, ImpactDirection,
    BaseRiskMetrics, RiskCalculator
};

// ============================================================================
// 统一接口 - 推荐使用
// ============================================================================

/// V2 微内核风控系统（推荐使用）
pub use v2::{
    RiskCore, RiskCoreBuilder, RiskRequest, RiskResult,
    RiskRepository, RiskEventBus, RiskEvent, CompatRiskManager,
    RiskManager, UnifiedRiskManager, ExecutionStats
};

/// V2风控规则系统
pub use v2::RiskRule as V2RiskRule;

/// 新的统一风控接口（推荐使用）
pub use unified_interface::{
    UnifiedRiskInterface, UnifiedRiskAdapter, RiskContext, RuleFilter
};

/// 数据缓存系统（推荐使用）
pub use data_cache::{
    RiskDataCache, CacheManager, CachedDataProvider, CachedDataProviderImpl
};

/// 异步风险检查服务（推荐使用）
pub use async_service::AsyncRiskService;

/// 实时风险监控系统（推荐使用）
pub use real_time_monitor::{
    RealTimeRiskMonitor, RiskMonitorManager, RiskThreshold, 
    RiskMonitorEvent, RiskMonitorConfig
};

// ============================================================================
// 使用指南
// ============================================================================

// 推荐的风险管理接口使用方式：
//
// 1. 同步风险检查：使用 UnifiedRiskInterface 接口
//    ```rust
//    let risk_engine = UnifiedRiskAdapter::new(engine);
//    let context = RiskContext::new()
//        .with_order(order)
//        .with_strategy_type("grid_trading".to_string());
//    let result = risk_engine.check_risk(context).await?;
//    ```
//
// 2. 异步风险检查（推荐）：使用 AsyncRiskService
//    ```rust
//    let async_service = AsyncRiskService::new(risk_engine);
//    let result = async_service.check_order_risk(order, Some("grid_trading".to_string()), None).await?;
//    ```
//
// 3. 带缓存的风险检查：
//    ```rust
//    let cache = Arc::new(RiskDataCache::new());
//    let cache_manager = CacheManager::new(cache);
//    let async_service = AsyncRiskService::new_with_options(risk_engine, 100, Some(cache_manager));
//    ```
//
// 4. 实时风险监控（推荐）：使用 RealTimeRiskMonitor
//    ```rust
//    let monitor_manager = RiskMonitorManager::new(risk_engine, Some(cache_manager), None);
//    monitor_manager.initialize().await?;
//    monitor_manager.start().await?;
//    
//    // 订阅监控事件
//    let mut event_receiver = monitor_manager.monitor().subscribe_events();
//    tokio::spawn(async move {
//        while let Ok(event) = event_receiver.recv().await {
//            match event {
//                RiskMonitorEvent::ThresholdTriggered { threshold_name, current_value, .. } => {
//                    println!("风险阈值触发: {} = {}", threshold_name, current_value);
//                }
//                RiskMonitorEvent::RiskAlert { alert, .. } => {
//                    println!("风险预警: {}", alert.message);
//                }
//                _ => {}
//            }
//        }
//    });
//    ```
//
// 5. 风险指标获取：
//    ```rust
//    let metrics = risk_engine.get_risk_metrics(&context).await?;
//    ```
//
// 5. 风险规则管理：使用 UnifiedRiskInterface 接口
//    ```rust
//    let filter = RuleFilter {
//        strategy_type: Some("grid_trading".to_string()),
//        enabled: Some(true),
//        ..Default::default()
//    };
//    let rules = risk_engine.get_rules(&filter).await?;
//    ```
//
// 6. 风险指标：使用 MetricsCalculator 替代原来的 ComprehensiveRiskCalculator
//
// 迁移指南：
// - 原 UnifiedRiskEngine → UnifiedRiskAdapter
// - 原 RuleExecutionContext → RiskContext
// - 新增异步风险检查：AsyncRiskService
// - 新增数据缓存：RiskDataCache, CacheManager
// - 原 comprehensive_metrics::ComprehensiveRiskCalculator → metrics::MetricsCalculator
// - 原 comprehensive_metrics::ComprehensiveRiskMetrics → metrics::ComprehensiveRiskMetrics
// - 新增分层指标计算：BasicRiskMetrics, RatioMetrics, DrawdownMetrics, MarketMetrics
