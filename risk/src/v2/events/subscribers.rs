//! 内置事件订阅者实现
//!
//! 提供常用的事件处理器，如日志记录、指标收集、告警等

use super::{EventSubscriber, RiskEvent, EventSeverity, EventFilter};
use async_trait::async_trait;
use sigmax_core::SigmaXResult;
use std::sync::Arc;
use tokio::sync::Mutex;

/// 日志事件订阅者
pub struct LoggingSubscriber {
    name: String,
    min_severity: EventSeverity,
}

impl LoggingSubscriber {
    pub fn new(name: String, min_severity: EventSeverity) -> Self {
        Self { name, min_severity }
    }
}

#[async_trait]
impl EventSubscriber for LoggingSubscriber {
    async fn handle_event(&self, event: &RiskEvent) -> SigmaXResult<()> {
        let severity = event.severity();
        let event_type = event.event_type();
        let timestamp = event.timestamp();
        
        match severity {
            EventSeverity::Info => {
                tracing::info!(
                    event_type = event_type,
                    timestamp = %timestamp,
                    "风控事件: {:?}", event
                );
            }
            EventSeverity::Warning => {
                tracing::warn!(
                    event_type = event_type,
                    timestamp = %timestamp,
                    "风控警告: {:?}", event
                );
            }
            EventSeverity::Error => {
                tracing::error!(
                    event_type = event_type,
                    timestamp = %timestamp,
                    "风控错误: {:?}", event
                );
            }
            EventSeverity::Critical => {
                tracing::error!(
                    event_type = event_type,
                    timestamp = %timestamp,
                    "风控严重错误: {:?}", event
                );
            }
        }
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn min_severity(&self) -> EventSeverity {
        self.min_severity.clone()
    }
}

/// 指标收集订阅者
pub struct MetricsCollector {
    name: String,
    metrics: Arc<Mutex<std::collections::HashMap<String, f64>>>,
}

impl MetricsCollector {
    pub fn new(name: String) -> Self {
        Self {
            name,
            metrics: Arc::new(Mutex::new(std::collections::HashMap::new())),
        }
    }
    
    /// 获取收集的指标
    pub async fn get_metrics(&self) -> std::collections::HashMap<String, f64> {
        let metrics = self.metrics.lock().await;
        metrics.clone()
    }
    
    /// 清空指标
    pub async fn clear_metrics(&self) {
        let mut metrics = self.metrics.lock().await;
        metrics.clear();
    }
}

#[async_trait]
impl EventSubscriber for MetricsCollector {
    async fn handle_event(&self, event: &RiskEvent) -> SigmaXResult<()> {
        let mut metrics = self.metrics.lock().await;
        
        match event {
            RiskEvent::PerformanceMetric { metric_name, value, .. } => {
                metrics.insert(metric_name.clone(), *value);
            }
            RiskEvent::ViolationDetected { violations, .. } => {
                let count = metrics.entry("violation_count".to_string()).or_insert(0.0);
                *count += violations.len() as f64;
            }
            RiskEvent::RiskWarning { risk_level, .. } => {
                let key = format!("warning_{:?}", risk_level).to_lowercase();
                let count = metrics.entry(key).or_insert(0.0);
                *count += 1.0;
            }
            RiskEvent::SystemHealth { component, status, .. } => {
                let key = format!("health_{}_{}", component, status).to_lowercase();
                let count = metrics.entry(key).or_insert(0.0);
                *count += 1.0;
            }
            _ => {
                // 其他事件类型的通用计数
                let key = format!("event_{}", event.event_type());
                let count = metrics.entry(key).or_insert(0.0);
                *count += 1.0;
            }
        }
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn interested_event_types(&self) -> Vec<&str> {
        vec![
            "performance_metric",
            "violation_detected",
            "risk_warning",
            "system_health"
        ]
    }
}

/// 告警订阅者
pub struct AlertSubscriber {
    name: String,
    alert_handler: Arc<dyn AlertHandler>,
    min_severity: EventSeverity,
}

impl AlertSubscriber {
    pub fn new(
        name: String, 
        alert_handler: Arc<dyn AlertHandler>,
        min_severity: EventSeverity,
    ) -> Self {
        Self {
            name,
            alert_handler,
            min_severity,
        }
    }
}

#[async_trait]
impl EventSubscriber for AlertSubscriber {
    async fn handle_event(&self, event: &RiskEvent) -> SigmaXResult<()> {
        // 只处理严重程度足够的事件
        if event.severity() >= self.min_severity {
            self.alert_handler.send_alert(event).await?;
        }
        Ok(())
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn min_severity(&self) -> EventSeverity {
        self.min_severity.clone()
    }
}

/// 告警处理器特征
#[async_trait]
pub trait AlertHandler: Send + Sync {
    async fn send_alert(&self, event: &RiskEvent) -> SigmaXResult<()>;
}

/// 控制台告警处理器
pub struct ConsoleAlertHandler;

#[async_trait]
impl AlertHandler for ConsoleAlertHandler {
    async fn send_alert(&self, event: &RiskEvent) -> SigmaXResult<()> {
        println!("🚨 风控告警: {:?}", event);
        Ok(())
    }
}

/// 文件告警处理器
pub struct FileAlertHandler {
    file_path: String,
}

impl FileAlertHandler {
    pub fn new(file_path: String) -> Self {
        Self { file_path }
    }
}

#[async_trait]
impl AlertHandler for FileAlertHandler {
    async fn send_alert(&self, event: &RiskEvent) -> SigmaXResult<()> {
        use tokio::io::AsyncWriteExt;
        
        let alert_message = format!(
            "[{}] {} - {:?}\n",
            event.timestamp().format("%Y-%m-%d %H:%M:%S"),
            event.event_type(),
            event
        );
        
        let mut file = tokio::fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open(&self.file_path)
            .await
            .map_err(|e| sigmax_core::SigmaXError::Internal(format!("文件打开失败: {}", e)))?;

        file.write_all(alert_message.as_bytes()).await
            .map_err(|e| sigmax_core::SigmaXError::Internal(format!("文件写入失败: {}", e)))?;

        file.flush().await
            .map_err(|e| sigmax_core::SigmaXError::Internal(format!("文件刷新失败: {}", e)))?;
        
        Ok(())
    }
}

/// 过滤订阅者包装器
pub struct FilteredSubscriber {
    name: String,
    inner: Arc<dyn EventSubscriber>,
    filter: EventFilter,
}

impl FilteredSubscriber {
    pub fn new(
        name: String,
        inner: Arc<dyn EventSubscriber>,
        filter: EventFilter,
    ) -> Self {
        Self { name, inner, filter }
    }
}

#[async_trait]
impl EventSubscriber for FilteredSubscriber {
    async fn handle_event(&self, event: &RiskEvent) -> SigmaXResult<()> {
        self.inner.handle_event(event).await
    }
    
    fn name(&self) -> &str {
        &self.name
    }
    
    fn filter(&self) -> Option<EventFilter> {
        Some(self.filter.clone())
    }
    
    fn interested_event_types(&self) -> Vec<&str> {
        self.inner.interested_event_types()
    }
    
    fn min_severity(&self) -> EventSeverity {
        self.inner.min_severity()
    }
}
