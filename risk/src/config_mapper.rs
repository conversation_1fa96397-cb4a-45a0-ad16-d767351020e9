//! 风控配置映射工具
//!
//! 提供策略层RiskManagerConfig与核心层RiskManagementConfig之间的转换功能

use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use tracing::{info, debug};
use rust_decimal::prelude::{ToPrimitive, FromPrimitive};

use sigmax_core::{RiskManagementConfig, SigmaXResult, SigmaXError};

/// 策略层风控配置（简化版）
/// 
/// 这是策略层使用的简化风控配置，主要用于策略切换和性能评估
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyRiskConfig {
    /// 最大风险评分阈值
    pub max_risk_score: f64,
    /// 紧急停止风险阈值
    pub emergency_risk_threshold: f64,
    /// 最大仓位大小（相对于总资金）
    pub max_position_ratio: f64,
    /// 最大波动率阈值
    pub max_volatility_threshold: f64,
    /// 最小夏普比率
    pub min_sharpe_ratio: f64,
    /// 最大未实现损失比例
    pub max_unrealized_loss_ratio: f64,
    /// 切换冷却期（分钟）
    pub switch_cooldown_minutes: i64,
    /// 最大每小时切换次数
    pub max_switches_per_hour: u32,
}

impl Default for StrategyRiskConfig {
    fn default() -> Self {
        Self {
            max_risk_score: 0.7,
            emergency_risk_threshold: 0.9,
            max_position_ratio: 0.8,
            max_volatility_threshold: 0.3,
            min_sharpe_ratio: 0.5,
            max_unrealized_loss_ratio: 0.15,
            switch_cooldown_minutes: 30,
            max_switches_per_hour: 4,
        }
    }
}

/// 风控配置映射器
/// 
/// 负责在不同层次的风控配置之间进行转换和映射
pub struct RiskConfigMapper;

impl RiskConfigMapper {
    /// 从核心层配置提取策略层配置
    pub fn extract_strategy_config(core_config: &RiskManagementConfig) -> StrategyRiskConfig {
        debug!("从核心配置提取策略配置");

        StrategyRiskConfig {
            max_risk_score: 0.7, // 默认值，核心配置中没有直接对应
            emergency_risk_threshold: core_config.risk_parameters.advanced.emergency_measures.emergency_stop_threshold
                .to_f64().unwrap_or(0.9),
            max_position_ratio: core_config.risk_parameters.basic.position_size_limit_percent
                .to_f64().unwrap_or(0.8) / 100.0, // 转换为比例
            max_volatility_threshold: core_config.risk_parameters.market.volatility_threshold
                .to_f64().unwrap_or(0.3),
            min_sharpe_ratio: 0.5, // 默认值，核心配置中没有直接对应
            max_unrealized_loss_ratio: core_config.risk_parameters.basic.max_drawdown_percent
                .to_f64().unwrap_or(15.0) / 100.0, // 转换为比例
            switch_cooldown_minutes: 30, // 默认值，核心配置中没有直接对应
            max_switches_per_hour: core_config.risk_parameters.trading.max_hourly_trades,
        }
    }

    /// 将策略层配置映射到核心层配置的部分字段
    pub fn map_to_core_config(
        strategy_config: &StrategyRiskConfig,
        base_config: Option<RiskManagementConfig>
    ) -> SigmaXResult<RiskManagementConfig> {
        debug!("将策略配置映射到核心配置");
        
        // 使用基础配置或创建默认配置
        let mut core_config = base_config.unwrap_or_else(|| RiskManagementConfig::default());
        
        // 更新基础风险参数
        core_config.risk_parameters.basic.position_size_limit_percent =
            rust_decimal::Decimal::from_f64(strategy_config.max_position_ratio * 100.0)
                .unwrap_or(rust_decimal::Decimal::from(80));

        core_config.risk_parameters.basic.max_drawdown_percent =
            rust_decimal::Decimal::from_f64(strategy_config.max_unrealized_loss_ratio * 100.0)
                .unwrap_or(rust_decimal::Decimal::from(15));

        // 更新市场风险参数
        core_config.risk_parameters.market.volatility_threshold =
            rust_decimal::Decimal::from_f64(strategy_config.max_volatility_threshold)
                .unwrap_or(rust_decimal::Decimal::new(3, 1)); // 0.3

        // 更新交易风险参数
        core_config.risk_parameters.trading.max_hourly_trades = strategy_config.max_switches_per_hour;

        // 更新高级风险参数 - 紧急措施
        core_config.risk_parameters.advanced.emergency_measures.emergency_stop_threshold =
            rust_decimal::Decimal::from_f64(strategy_config.emergency_risk_threshold)
                .unwrap_or(rust_decimal::Decimal::new(9, 1)); // 0.9
        
        // 更新时间戳
        core_config.updated_at = Some(Utc::now());
        
        info!("策略配置成功映射到核心配置");
        Ok(core_config)
    }

    /// 验证策略配置的合理性
    pub fn validate_strategy_config(config: &StrategyRiskConfig) -> SigmaXResult<()> {
        debug!("验证策略风控配置");
        
        // 检查风险评分阈值
        if config.max_risk_score <= 0.0 || config.max_risk_score > 1.0 {
            return Err(SigmaXError::Config(
                "最大风险评分阈值必须在0-1之间".to_string()
            ));
        }
        
        if config.emergency_risk_threshold <= config.max_risk_score || config.emergency_risk_threshold > 1.0 {
            return Err(SigmaXError::Config(
                "紧急停止阈值必须大于最大风险评分且不超过1.0".to_string()
            ));
        }
        
        // 检查仓位比例
        if config.max_position_ratio <= 0.0 || config.max_position_ratio > 1.0 {
            return Err(SigmaXError::Config(
                "最大仓位比例必须在0-1之间".to_string()
            ));
        }
        
        // 检查波动率阈值
        if config.max_volatility_threshold <= 0.0 {
            return Err(SigmaXError::Config(
                "最大波动率阈值必须大于0".to_string()
            ));
        }
        
        // 检查夏普比率
        if config.min_sharpe_ratio < 0.0 {
            return Err(SigmaXError::Config(
                "最小夏普比率不能为负数".to_string()
            ));
        }
        
        // 检查损失比例
        if config.max_unrealized_loss_ratio <= 0.0 || config.max_unrealized_loss_ratio > 1.0 {
            return Err(SigmaXError::Config(
                "最大未实现损失比例必须在0-1之间".to_string()
            ));
        }
        
        // 检查时间参数
        if config.switch_cooldown_minutes <= 0 {
            return Err(SigmaXError::Config(
                "切换冷却期必须大于0分钟".to_string()
            ));
        }
        
        if config.max_switches_per_hour == 0 {
            return Err(SigmaXError::Config(
                "每小时最大切换次数必须大于0".to_string()
            ));
        }
        
        info!("策略风控配置验证通过");
        Ok(())
    }

    /// 创建配置差异报告
    pub fn create_diff_report(
        old_config: &StrategyRiskConfig,
        new_config: &StrategyRiskConfig
    ) -> ConfigDiffReport {
        debug!("创建配置差异报告");
        
        let mut changes = Vec::new();
        
        if (old_config.max_risk_score - new_config.max_risk_score).abs() > f64::EPSILON {
            changes.push(ConfigChange {
                field: "max_risk_score".to_string(),
                old_value: old_config.max_risk_score.to_string(),
                new_value: new_config.max_risk_score.to_string(),
                change_type: ChangeType::Modified,
            });
        }
        
        if (old_config.emergency_risk_threshold - new_config.emergency_risk_threshold).abs() > f64::EPSILON {
            changes.push(ConfigChange {
                field: "emergency_risk_threshold".to_string(),
                old_value: old_config.emergency_risk_threshold.to_string(),
                new_value: new_config.emergency_risk_threshold.to_string(),
                change_type: ChangeType::Modified,
            });
        }
        
        if (old_config.max_position_ratio - new_config.max_position_ratio).abs() > f64::EPSILON {
            changes.push(ConfigChange {
                field: "max_position_ratio".to_string(),
                old_value: old_config.max_position_ratio.to_string(),
                new_value: new_config.max_position_ratio.to_string(),
                change_type: ChangeType::Modified,
            });
        }
        
        if (old_config.max_volatility_threshold - new_config.max_volatility_threshold).abs() > f64::EPSILON {
            changes.push(ConfigChange {
                field: "max_volatility_threshold".to_string(),
                old_value: old_config.max_volatility_threshold.to_string(),
                new_value: new_config.max_volatility_threshold.to_string(),
                change_type: ChangeType::Modified,
            });
        }
        
        if old_config.max_switches_per_hour != new_config.max_switches_per_hour {
            changes.push(ConfigChange {
                field: "max_switches_per_hour".to_string(),
                old_value: old_config.max_switches_per_hour.to_string(),
                new_value: new_config.max_switches_per_hour.to_string(),
                change_type: ChangeType::Modified,
            });
        }
        
        ConfigDiffReport {
            timestamp: Utc::now(),
            total_changes: changes.len(),
            changes,
        }
    }

    /// 合并多个策略配置（取最严格的限制）
    pub fn merge_strategy_configs(configs: &[StrategyRiskConfig]) -> SigmaXResult<StrategyRiskConfig> {
        if configs.is_empty() {
            return Err(SigmaXError::Config("配置列表不能为空".to_string()));
        }
        
        debug!("合并{}个策略配置", configs.len());
        
        let mut merged = configs[0].clone();
        
        for config in configs.iter().skip(1) {
            // 取最严格的风险限制
            merged.max_risk_score = merged.max_risk_score.min(config.max_risk_score);
            merged.emergency_risk_threshold = merged.emergency_risk_threshold.min(config.emergency_risk_threshold);
            merged.max_position_ratio = merged.max_position_ratio.min(config.max_position_ratio);
            merged.max_volatility_threshold = merged.max_volatility_threshold.min(config.max_volatility_threshold);
            merged.min_sharpe_ratio = merged.min_sharpe_ratio.max(config.min_sharpe_ratio);
            merged.max_unrealized_loss_ratio = merged.max_unrealized_loss_ratio.min(config.max_unrealized_loss_ratio);
            merged.switch_cooldown_minutes = merged.switch_cooldown_minutes.max(config.switch_cooldown_minutes);
            merged.max_switches_per_hour = merged.max_switches_per_hour.min(config.max_switches_per_hour);
        }
        
        info!("成功合并{}个策略配置", configs.len());
        Ok(merged)
    }
}

/// 配置变更类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ChangeType {
    Added,
    Modified,
    Removed,
}

/// 配置变更记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigChange {
    pub field: String,
    pub old_value: String,
    pub new_value: String,
    pub change_type: ChangeType,
}

/// 配置差异报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigDiffReport {
    pub timestamp: DateTime<Utc>,
    pub total_changes: usize,
    pub changes: Vec<ConfigChange>,
}

impl ConfigDiffReport {
    /// 检查是否有重要变更
    pub fn has_critical_changes(&self) -> bool {
        self.changes.iter().any(|change| {
            matches!(change.field.as_str(), 
                "emergency_risk_threshold" | "max_position_ratio" | "max_risk_score"
            )
        })
    }
    
    /// 生成变更摘要
    pub fn summary(&self) -> String {
        if self.changes.is_empty() {
            "无配置变更".to_string()
        } else {
            format!("共{}项配置变更，包含{}项关键变更", 
                self.total_changes,
                if self.has_critical_changes() { "重要" } else { "一般" }
            )
        }
    }
}
