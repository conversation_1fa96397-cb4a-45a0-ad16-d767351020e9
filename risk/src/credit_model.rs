//! 信用风险模型
//! 
//! 实现交易对手风险和信用评估

use sigmax_core::{Amount, SigmaXResult, SigmaXError, ExchangeId};
use rust_decimal::Decimal;
use std::collections::HashMap;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

/// 信用风险指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreditRiskMetrics {
    /// 整体信用风险评分 (0-1, 1为最高风险)
    pub overall_credit_risk: f64,
    /// 交易对手风险评分
    pub counterparty_risk: f64,
    /// 集中度风险
    pub concentration_risk: f64,
    /// 违约概率估计
    pub default_probability: f64,
    /// 信用暴露金额
    pub credit_exposure: Amount,
    /// 各交易所信用评分
    pub exchange_credit_scores: HashMap<String, f64>,
    /// 计算时间戳
    pub calculated_at: DateTime<Utc>,
}

/// 交易所信用信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExchangeCreditInfo {
    /// 交易所ID
    pub exchange_id: ExchangeId,
    /// 信用评级 (AAA, AA, A, BBB, BB, B, CCC, CC, C, D)
    pub credit_rating: CreditRating,
    /// 历史违约记录
    pub default_history: Vec<DefaultEvent>,
    /// 监管状态
    pub regulatory_status: RegulatoryStatus,
    /// 资产规模
    pub assets_under_management: Amount,
    /// 用户资金保护措施
    pub user_fund_protection: FundProtectionLevel,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

/// 信用评级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum CreditRating {
    AAA = 10,
    AA = 9,
    A = 8,
    BBB = 7,
    BB = 6,
    B = 5,
    CCC = 4,
    CC = 3,
    C = 2,
    D = 1,
    Unrated = 0,
}

/// 违约事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DefaultEvent {
    /// 事件类型
    pub event_type: DefaultEventType,
    /// 发生时间
    pub occurred_at: DateTime<Utc>,
    /// 影响金额
    pub impact_amount: Amount,
    /// 恢复率 (0-1)
    pub recovery_rate: f64,
    /// 事件描述
    pub description: String,
}

/// 违约事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DefaultEventType {
    /// 技术故障
    TechnicalFailure,
    /// 黑客攻击
    SecurityBreach,
    /// 流动性危机
    LiquidityCrisis,
    /// 监管问题
    RegulatoryIssue,
    /// 破产
    Bankruptcy,
    /// 其他
    Other,
}

/// 监管状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RegulatoryStatus {
    /// 完全合规
    FullyCompliant,
    /// 部分合规
    PartiallyCompliant,
    /// 监管审查中
    UnderReview,
    /// 监管处罚
    Sanctioned,
    /// 无监管
    Unregulated,
}

/// 资金保护级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FundProtectionLevel {
    /// 完全保护 (保险/隔离)
    FullProtection,
    /// 部分保护
    PartialProtection,
    /// 基础保护
    BasicProtection,
    /// 无保护
    NoProtection,
}

/// 信用风险计算器
pub struct CreditRiskCalculator {
    /// 交易所信用信息
    exchange_credit_info: HashMap<String, ExchangeCreditInfo>,
    /// 各交易所的资金分配
    fund_allocation: HashMap<String, Amount>,
    /// 信用风险参数
    risk_parameters: CreditRiskParameters,
}

/// 信用风险参数
#[derive(Debug, Clone)]
pub struct CreditRiskParameters {
    /// 最大单一交易所暴露比例
    pub max_single_exchange_exposure: f64,
    /// 信用评级权重
    pub credit_rating_weights: HashMap<CreditRating, f64>,
    /// 违约概率基准
    pub base_default_probabilities: HashMap<CreditRating, f64>,
    /// 集中度风险阈值
    pub concentration_risk_threshold: f64,
}

impl Default for CreditRiskParameters {
    fn default() -> Self {
        let mut credit_rating_weights = HashMap::new();
        credit_rating_weights.insert(CreditRating::AAA, 0.05);
        credit_rating_weights.insert(CreditRating::AA, 0.10);
        credit_rating_weights.insert(CreditRating::A, 0.15);
        credit_rating_weights.insert(CreditRating::BBB, 0.25);
        credit_rating_weights.insert(CreditRating::BB, 0.40);
        credit_rating_weights.insert(CreditRating::B, 0.60);
        credit_rating_weights.insert(CreditRating::CCC, 0.80);
        credit_rating_weights.insert(CreditRating::CC, 0.90);
        credit_rating_weights.insert(CreditRating::C, 0.95);
        credit_rating_weights.insert(CreditRating::D, 1.00);
        credit_rating_weights.insert(CreditRating::Unrated, 0.70);

        let mut base_default_probabilities = HashMap::new();
        base_default_probabilities.insert(CreditRating::AAA, 0.0001); // 0.01%
        base_default_probabilities.insert(CreditRating::AA, 0.0005);  // 0.05%
        base_default_probabilities.insert(CreditRating::A, 0.001);    // 0.1%
        base_default_probabilities.insert(CreditRating::BBB, 0.005);  // 0.5%
        base_default_probabilities.insert(CreditRating::BB, 0.02);    // 2%
        base_default_probabilities.insert(CreditRating::B, 0.05);     // 5%
        base_default_probabilities.insert(CreditRating::CCC, 0.15);   // 15%
        base_default_probabilities.insert(CreditRating::CC, 0.30);    // 30%
        base_default_probabilities.insert(CreditRating::C, 0.50);     // 50%
        base_default_probabilities.insert(CreditRating::D, 1.00);     // 100%
        base_default_probabilities.insert(CreditRating::Unrated, 0.10); // 10%

        Self {
            max_single_exchange_exposure: 0.5, // 最大50%暴露于单一交易所
            credit_rating_weights,
            base_default_probabilities,
            concentration_risk_threshold: 0.3, // 30%集中度阈值
        }
    }
}

impl CreditRiskCalculator {
    /// 创建新的信用风险计算器
    pub fn new() -> Self {
        Self {
            exchange_credit_info: HashMap::new(),
            fund_allocation: HashMap::new(),
            risk_parameters: CreditRiskParameters::default(),
        }
    }

    /// 创建带自定义参数的信用风险计算器
    pub fn with_parameters(parameters: CreditRiskParameters) -> Self {
        Self {
            exchange_credit_info: HashMap::new(),
            fund_allocation: HashMap::new(),
            risk_parameters: parameters,
        }
    }

    /// 添加交易所信用信息
    pub fn add_exchange_credit_info(&mut self, credit_info: ExchangeCreditInfo) {
        let exchange_key = format!("{:?}", credit_info.exchange_id);
        self.exchange_credit_info.insert(exchange_key, credit_info);
    }

    /// 更新资金分配
    pub fn update_fund_allocation(&mut self, exchange_id: &ExchangeId, amount: Amount) {
        let exchange_key = format!("{:?}", exchange_id);
        self.fund_allocation.insert(exchange_key, amount);
    }

    /// 计算信用风险指标
    pub fn calculate_credit_risk(&self) -> SigmaXResult<CreditRiskMetrics> {
        if self.fund_allocation.is_empty() {
            return Err(SigmaXError::InvalidOperation(
                "没有资金分配数据可供分析".to_string()
            ));
        }

        let total_funds: Amount = self.fund_allocation.values().sum();
        let mut exchange_credit_scores = HashMap::new();
        let mut weighted_risk_sum = 0.0;
        let mut total_weight = 0.0;

        // 计算各交易所的信用评分
        for (exchange_key, &allocated_amount) in &self.fund_allocation {
            let credit_score = self.calculate_exchange_credit_score(exchange_key)?;
            exchange_credit_scores.insert(exchange_key.clone(), credit_score);
            
            let weight = if total_funds > Amount::ZERO {
                allocated_amount.to_string().parse::<f64>().unwrap_or(0.0) /
                total_funds.to_string().parse::<f64>().unwrap_or(1.0)
            } else {
                0.0
            };
            
            weighted_risk_sum += credit_score * weight;
            total_weight += weight;
        }

        let overall_credit_risk = if total_weight > 0.0 {
            weighted_risk_sum / total_weight
        } else {
            0.5 // 默认中等风险
        };

        let counterparty_risk = self.calculate_counterparty_risk()?;
        let concentration_risk = self.calculate_concentration_risk(total_funds)?;
        let default_probability = self.calculate_default_probability()?;
        let credit_exposure = total_funds;

        Ok(CreditRiskMetrics {
            overall_credit_risk,
            counterparty_risk,
            concentration_risk,
            default_probability,
            credit_exposure,
            exchange_credit_scores,
            calculated_at: Utc::now(),
        })
    }

    /// 计算单个交易所的信用评分
    fn calculate_exchange_credit_score(&self, exchange_key: &str) -> SigmaXResult<f64> {
        if let Some(credit_info) = self.exchange_credit_info.get(exchange_key) {
            let mut score = 0.0;
            let mut factors = 0;

            // 1. 信用评级评分 (权重: 40%)
            let rating_score = self.risk_parameters.credit_rating_weights
                .get(&credit_info.credit_rating)
                .copied()
                .unwrap_or(0.7);
            score += rating_score * 0.4;
            factors += 1;

            // 2. 历史违约记录评分 (权重: 30%)
            let default_score = self.calculate_default_history_score(&credit_info.default_history)?;
            score += default_score * 0.3;
            factors += 1;

            // 3. 监管状态评分 (权重: 20%)
            let regulatory_score = self.calculate_regulatory_score(&credit_info.regulatory_status);
            score += regulatory_score * 0.2;
            factors += 1;

            // 4. 资金保护评分 (权重: 10%)
            let protection_score = self.calculate_protection_score(&credit_info.user_fund_protection);
            score += protection_score * 0.1;
            factors += 1;

            if factors > 0 {
                Ok(score)
            } else {
                Ok(0.5) // 默认中等风险
            }
        } else {
            Ok(0.8) // 未知交易所，高风险
        }
    }

    /// 计算违约历史评分
    fn calculate_default_history_score(&self, default_history: &[DefaultEvent]) -> SigmaXResult<f64> {
        if default_history.is_empty() {
            return Ok(0.0); // 无违约历史，低风险
        }

        let now = Utc::now();
        let mut weighted_score = 0.0;
        let mut total_weight = 0.0;

        for event in default_history {
            // 时间衰减：越久远的事件权重越小
            let days_ago = (now - event.occurred_at).num_days() as f64;
            let time_weight = (-days_ago / 365.0).exp(); // 指数衰减，半衰期1年

            // 事件严重性评分
            let severity_score = match event.event_type {
                DefaultEventType::Bankruptcy => 1.0,
                DefaultEventType::SecurityBreach => 0.8,
                DefaultEventType::LiquidityCrisis => 0.7,
                DefaultEventType::RegulatoryIssue => 0.6,
                DefaultEventType::TechnicalFailure => 0.4,
                DefaultEventType::Other => 0.5,
            };

            // 考虑恢复率
            let adjusted_score = severity_score * (1.0 - event.recovery_rate);

            weighted_score += adjusted_score * time_weight;
            total_weight += time_weight;
        }

        let final_score = if total_weight > 0.0 {
            weighted_score / total_weight
        } else {
            0.0
        };

        Ok(final_score.min(1.0))
    }

    /// 计算监管状态评分
    fn calculate_regulatory_score(&self, status: &RegulatoryStatus) -> f64 {
        match status {
            RegulatoryStatus::FullyCompliant => 0.0,
            RegulatoryStatus::PartiallyCompliant => 0.2,
            RegulatoryStatus::UnderReview => 0.4,
            RegulatoryStatus::Sanctioned => 0.8,
            RegulatoryStatus::Unregulated => 0.6,
        }
    }

    /// 计算资金保护评分
    fn calculate_protection_score(&self, protection: &FundProtectionLevel) -> f64 {
        match protection {
            FundProtectionLevel::FullProtection => 0.0,
            FundProtectionLevel::PartialProtection => 0.3,
            FundProtectionLevel::BasicProtection => 0.6,
            FundProtectionLevel::NoProtection => 1.0,
        }
    }

    /// 计算交易对手风险
    fn calculate_counterparty_risk(&self) -> SigmaXResult<f64> {
        // 基于交易所数量和分散程度计算
        let exchange_count = self.fund_allocation.len() as f64;
        
        if exchange_count <= 1.0 {
            Ok(1.0) // 单一交易所，最高风险
        } else {
            // 交易所越多，交易对手风险越低
            let diversification_factor = 1.0 / exchange_count.sqrt();
            Ok(diversification_factor.min(1.0))
        }
    }

    /// 计算集中度风险
    fn calculate_concentration_risk(&self, total_funds: Amount) -> SigmaXResult<f64> {
        if total_funds <= Amount::ZERO {
            return Ok(0.0);
        }

        let mut max_allocation_ratio = 0.0f64;
        
        for &allocated_amount in self.fund_allocation.values() {
            let ratio = allocated_amount.to_string().parse::<f64>().unwrap_or(0.0) /
                       total_funds.to_string().parse::<f64>().unwrap_or(1.0);
            max_allocation_ratio = max_allocation_ratio.max(ratio);
        }

        // 集中度风险评分
        if max_allocation_ratio > self.risk_parameters.concentration_risk_threshold {
            let excess_concentration = max_allocation_ratio - self.risk_parameters.concentration_risk_threshold;
            let max_excess = 1.0 - self.risk_parameters.concentration_risk_threshold;
            Ok((excess_concentration / max_excess).min(1.0))
        } else {
            Ok(0.0)
        }
    }

    /// 计算违约概率
    fn calculate_default_probability(&self) -> SigmaXResult<f64> {
        let total_funds: Amount = self.fund_allocation.values().sum();
        let mut weighted_probability = 0.0;
        let mut total_weight = 0.0;

        for (exchange_key, &allocated_amount) in &self.fund_allocation {
            if let Some(credit_info) = self.exchange_credit_info.get(exchange_key) {
                let base_probability = self.risk_parameters.base_default_probabilities
                    .get(&credit_info.credit_rating)
                    .copied()
                    .unwrap_or(0.1);
                
                // 根据历史违约记录调整概率
                let history_adjustment = self.calculate_default_history_score(&credit_info.default_history)?;
                let adjusted_probability = base_probability * (1.0 + history_adjustment);
                
                let weight = if total_funds > Amount::ZERO {
                    allocated_amount.to_string().parse::<f64>().unwrap_or(0.0) /
                    total_funds.to_string().parse::<f64>().unwrap_or(1.0)
                } else {
                    0.0
                };
                
                weighted_probability += adjusted_probability * weight;
                total_weight += weight;
            }
        }

        if total_weight > 0.0 {
            Ok(weighted_probability / total_weight)
        } else {
            Ok(0.1) // 默认10%违约概率
        }
    }

    /// 获取交易所信用评级
    pub fn get_exchange_rating(&self, exchange_id: &ExchangeId) -> Option<CreditRating> {
        let exchange_key = format!("{:?}", exchange_id);
        self.exchange_credit_info.get(&exchange_key)
            .map(|info| info.credit_rating.clone())
    }

    /// 检查是否超过单一交易所暴露限制
    pub fn check_exposure_limit(&self, exchange_id: &ExchangeId, amount: Amount) -> bool {
        let total_funds: Amount = self.fund_allocation.values().sum::<Amount>() + amount;
        
        if total_funds <= Amount::ZERO {
            return true;
        }
        
        let exposure_ratio = amount.to_string().parse::<f64>().unwrap_or(0.0) /
                           total_funds.to_string().parse::<f64>().unwrap_or(1.0);
        
        exposure_ratio <= self.risk_parameters.max_single_exchange_exposure
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_credit_risk_calculation() {
        let mut calculator = CreditRiskCalculator::new();
        
        // 添加交易所信用信息
        let binance_info = ExchangeCreditInfo {
            exchange_id: ExchangeId::Binance,
            credit_rating: CreditRating::AA,
            default_history: vec![],
            regulatory_status: RegulatoryStatus::FullyCompliant,
            assets_under_management: Decimal::from(1000000000), // 10亿
            user_fund_protection: FundProtectionLevel::PartialProtection,
            last_updated: Utc::now(),
        };
        
        calculator.add_exchange_credit_info(binance_info);
        calculator.update_fund_allocation(&ExchangeId::Binance, Decimal::from(100000));
        
        let metrics = calculator.calculate_credit_risk().unwrap();
        
        assert!(metrics.overall_credit_risk >= 0.0);
        assert!(metrics.overall_credit_risk <= 1.0);
        assert!(metrics.default_probability >= 0.0);
        assert!(metrics.default_probability <= 1.0);
    }
}
