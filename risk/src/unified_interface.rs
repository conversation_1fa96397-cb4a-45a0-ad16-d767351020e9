//! 统一风控接口
//! 
//! 提供统一的风控系统接口，解决架构不一致问题

use async_trait::async_trait;
use std::sync::Arc;
use sigmax_core::{Order, Balance, SigmaXResult, TradingPair, ExchangeId, Amount};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use serde::{Serialize, Deserialize};
use rust_decimal::Decimal;
use rust_decimal::prelude::FromPrimitive;

use crate::unified_engine::{
    RiskCheckResult, UnifiedRiskRule, RuleExecutionContext, RuleResult, 
    UnifiedRiskRepository, ExecutionStats
};
use crate::core::types::{RiskMetrics, PortfolioInfo, RiskLevel};

/// 风控上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskContext {
    /// 订单信息
    pub order: Option<Order>,
    /// 持仓信息
    pub balances: Option<Vec<Balance>>,
    /// 投资组合信息
    pub portfolio: Option<PortfolioInfo>,
    /// 策略类型
    pub strategy_type: Option<String>,
    /// 交易对
    pub trading_pair: Option<String>,
    /// 额外上下文数据
    pub extra_data: HashMap<String, serde_json::Value>,
}

impl RiskContext {
    /// 创建新的风控上下文
    pub fn new() -> Self {
        Self {
            order: None,
            balances: None,
            portfolio: None,
            strategy_type: None,
            trading_pair: None,
            extra_data: HashMap::new(),
        }
    }

    /// 设置订单
    pub fn with_order(mut self, order: Order) -> Self {
        self.order = Some(order);
        self
    }

    /// 设置持仓信息
    pub fn with_balances(mut self, balances: Vec<Balance>) -> Self {
        self.balances = Some(balances);
        self
    }

    /// 设置投资组合信息
    pub fn with_portfolio(mut self, portfolio: PortfolioInfo) -> Self {
        self.portfolio = Some(portfolio);
        self
    }

    /// 设置策略类型
    pub fn with_strategy_type(mut self, strategy_type: String) -> Self {
        self.strategy_type = Some(strategy_type);
        self
    }

    /// 设置交易对
    pub fn with_trading_pair(mut self, trading_pair: String) -> Self {
        self.trading_pair = Some(trading_pair);
        self
    }

    /// 添加额外数据
    pub fn with_extra_data(mut self, key: String, value: serde_json::Value) -> Self {
        self.extra_data.insert(key, value);
        self
    }

    /// 转换为规则执行上下文
    pub fn to_rule_execution_context(&self) -> RuleExecutionContext {
        RuleExecutionContext {
            order: self.order.clone(),
            balances: self.balances.clone(),
            strategy_type: self.strategy_type.clone(),
            trading_pair: self.trading_pair.clone(),
            extra_data: self.extra_data.clone(),
        }
    }
}

/// 规则过滤器
#[derive(Debug, Clone, Default)]
pub struct RuleFilter {
    /// 规则分类
    pub category: Option<String>,
    /// 规则类型
    pub rule_type: Option<String>,
    /// 是否启用
    pub enabled: Option<bool>,
    /// 策略类型
    pub strategy_type: Option<String>,
    /// 交易对
    pub trading_pair: Option<String>,
    /// 优先级范围
    pub min_priority: Option<i32>,
    pub max_priority: Option<i32>,
}

/// 统一风控引擎接口
#[async_trait]
pub trait UnifiedRiskInterface: Send + Sync {
    /// 执行风险检查
    async fn check_risk(&self, context: RiskContext) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查订单风险
    async fn check_order_risk(&self, order: &Order, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查持仓风险
    async fn check_position_risk(&self, balances: &[Balance], strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查投资组合风险
    async fn check_portfolio_risk(&self, portfolio: &PortfolioInfo, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;
    
    /// 获取风险指标
    async fn get_risk_metrics(&self, context: &RiskContext) -> SigmaXResult<RiskMetrics>;
    
    /// 获取规则
    async fn get_rules(&self, filter: &RuleFilter) -> SigmaXResult<Vec<UnifiedRiskRule>>;
    
    /// 更新规则
    async fn update_rule(&self, rule: &UnifiedRiskRule) -> SigmaXResult<()>;
    
    /// 创建规则
    async fn create_rule(&self, rule: UnifiedRiskRule) -> SigmaXResult<Uuid>;
    
    /// 删除规则
    async fn delete_rule(&self, rule_id: Uuid) -> SigmaXResult<()>;
    
    /// 启用/禁用规则
    async fn toggle_rule(&self, rule_id: Uuid, enabled: bool) -> SigmaXResult<()>;
    
    /// 获取执行统计
    async fn get_execution_stats(&self) -> SigmaXResult<ExecutionStats>;
    
    /// 重新加载规则
    async fn reload_rules(&self) -> SigmaXResult<()>;
}

/// 统一风控引擎适配器
/// 
/// 将现有的 UnifiedRiskEngine 适配到新的 UnifiedRiskInterface 接口
pub struct UnifiedRiskAdapter {
    /// 内部风控引擎
    engine: Arc<crate::unified_engine::UnifiedRiskEngine>,
}

impl UnifiedRiskAdapter {
    /// 创建新的适配器
    pub fn new(engine: Arc<crate::unified_engine::UnifiedRiskEngine>) -> Self {
        Self { engine }
    }
}

#[async_trait]
impl UnifiedRiskInterface for UnifiedRiskAdapter {
    async fn check_risk(&self, context: RiskContext) -> SigmaXResult<RiskCheckResult> {
        let rule_context = context.to_rule_execution_context();
        
        if let Some(order) = &context.order {
            self.engine.check_order_risk(order, context.strategy_type.as_deref()).await
        } else if let Some(balances) = &context.balances {
            self.engine.check_position_risk(balances, context.strategy_type.as_deref()).await
        } else {
            // 如果没有订单和持仓信息，返回通过的结果
            Ok(RiskCheckResult::passed())
        }
    }
    
    async fn check_order_risk(&self, order: &Order, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        self.engine.check_order_risk(order, strategy_type).await
    }
    
    async fn check_position_risk(&self, balances: &[Balance], strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        self.engine.check_position_risk(balances, strategy_type).await
    }
    
    async fn check_portfolio_risk(&self, portfolio: &PortfolioInfo, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        // 将投资组合信息转换为持仓信息
        let balances = portfolio.positions.iter()
            .map(|(asset, value)| {
                Balance {
                    asset: asset.clone(),
                    free: Decimal::from_f64(*value).unwrap_or_default(),
                    locked: Decimal::from_f64(0.0).unwrap_or_default(),
                    exchange_id: ExchangeId::Simulator, // 默认交易所ID
                    updated_at: chrono::Utc::now(),
                }
            })
            .collect::<Vec<_>>();
        
        self.engine.check_position_risk(&balances, strategy_type).await
    }
    
    async fn get_risk_metrics(&self, context: &RiskContext) -> SigmaXResult<RiskMetrics> {
        // 创建默认风险指标
        let mut metrics = RiskMetrics::default();
        
        // 如果有策略类型，尝试获取回撤数据
        if let Some(strategy_type) = &context.strategy_type {
            let rule_context = context.to_rule_execution_context();
            
            // 获取回撤
            if let Ok(drawdown) = self.engine.calculate_current_drawdown(&rule_context).await {
                metrics.max_drawdown = drawdown;
            }
            
            // 获取波动率
            if let Some(trading_pair) = &context.trading_pair {
                if let Ok(volatility) = self.engine.calculate_market_volatility(&rule_context).await {
                    metrics.volatility = volatility;
                }
            }
        }
        
        // 设置时间戳
        metrics.timestamp = Utc::now();
        
        Ok(metrics)
    }
    
    async fn get_rules(&self, filter: &RuleFilter) -> SigmaXResult<Vec<UnifiedRiskRule>> {
        // 转换为内部过滤器
        let internal_filter = crate::unified_engine::RuleFilter {
            category: filter.category.clone(),
            rule_type: filter.rule_type.clone(),
            enabled: filter.enabled,
            strategy_type: filter.strategy_type.clone(),
        };
        
        // 获取规则
        let repository = self.engine.repository();
        let rules = repository.get_rules_with_filter(&internal_filter, 1, 1000).await?;
        
        // 应用额外过滤
        let filtered_rules = if let Some(trading_pair) = &filter.trading_pair {
            rules.into_iter()
                .filter(|rule| rule.trading_pairs.is_empty() || rule.trading_pairs.contains(trading_pair))
                .collect()
        } else {
            rules
        };
        
        // 应用优先级过滤
        let filtered_rules = if let Some(min_priority) = filter.min_priority {
            filtered_rules.into_iter()
                .filter(|rule| rule.priority >= min_priority)
                .collect()
        } else {
            filtered_rules
        };
        
        let filtered_rules = if let Some(max_priority) = filter.max_priority {
            filtered_rules.into_iter()
                .filter(|rule| rule.priority <= max_priority)
                .collect()
        } else {
            filtered_rules
        };
        
        Ok(filtered_rules)
    }
    
    async fn update_rule(&self, rule: &UnifiedRiskRule) -> SigmaXResult<()> {
        let repository = self.engine.repository();
        repository.update_rule(rule).await
    }
    
    async fn create_rule(&self, rule: UnifiedRiskRule) -> SigmaXResult<Uuid> {
        // 创建规则
        let repository = self.engine.repository();
        repository.update_rule(&rule).await?;
        
        // 重新加载规则
        self.engine.reload_rules().await?;
        
        Ok(rule.id)
    }
    
    async fn delete_rule(&self, rule_id: Uuid) -> SigmaXResult<()> {
        // 获取规则
        let repository = self.engine.repository();
        let rule = repository.get_rule(rule_id).await?;
        
        if let Some(mut rule) = rule {
            // 禁用规则
            rule.enabled = false;
            repository.update_rule(&rule).await?;
            
            // 重新加载规则
            self.engine.reload_rules().await?;
        }
        
        Ok(())
    }
    
    async fn toggle_rule(&self, rule_id: Uuid, enabled: bool) -> SigmaXResult<()> {
        // 获取规则
        let repository = self.engine.repository();
        let rule = repository.get_rule(rule_id).await?;
        
        if let Some(mut rule) = rule {
            // 更新启用状态
            rule.enabled = enabled;
            repository.update_rule(&rule).await?;
            
            // 重新加载规则
            self.engine.reload_rules().await?;
        }
        
        Ok(())
    }
    
    async fn get_execution_stats(&self) -> SigmaXResult<ExecutionStats> {
        Ok(self.engine.get_execution_stats().await)
    }
    
    async fn reload_rules(&self) -> SigmaXResult<()> {
        self.engine.reload_rules().await
    }
}