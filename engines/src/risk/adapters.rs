//! 适配器基础类和trait定义
//! 
//! 设计原则：
//! - 适配器模式：统一的适配器接口
//! - 模板方法模式：通用逻辑在基类，特定逻辑在子类
//! - 策略模式：不同的缓存和优化策略

use async_trait::async_trait;
use sigmax_core::{EngineType, Order, Balance, SigmaXResult, SigmaXError, RiskCheckResult, EngineRiskAdapter};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tracing::{debug, error, warn};

use super::{RiskEngine, CacheService, MetricsCollector};

// 重新导出核心trait
// pub use core::traits::EngineRiskAdapter; // 暂时注释掉，等待core::traits实现

/// 适配器指标
#[derive(Debug, Clone)]
pub struct AdapterMetrics {
    pub engine_type: EngineType,
    pub cache_hit_rate: f64,
    pub avg_latency_ms: f64,
    pub throughput_rps: f64,
    pub error_rate: f64,
}

/// 适配器基础类 - 提供通用功能
pub struct AdapterBase {
    /// 风控引擎
    risk_engine: Arc<dyn RiskEngine>,
    /// 缓存服务
    cache_service: Arc<dyn CacheService>,
    /// 指标收集器
    metrics_collector: Arc<dyn MetricsCollector>,
    /// 引擎类型
    engine_type: EngineType,
    /// 适配器配置
    config: AdapterConfig,
}

/// 适配器配置
#[derive(Debug, Clone)]
pub struct AdapterConfig {
    /// 启用缓存
    pub enable_cache: bool,
    /// 缓存TTL
    pub cache_ttl: Duration,
    /// 启用指标收集
    pub enable_metrics: bool,
    /// 超时时间
    pub timeout: Duration,
    /// 重试次数
    pub retry_attempts: u32,
}

impl Default for AdapterConfig {
    fn default() -> Self {
        Self {
            enable_cache: true,
            cache_ttl: Duration::from_secs(60),
            enable_metrics: true,
            timeout: Duration::from_millis(5000),
            retry_attempts: 3,
        }
    }
}

impl AdapterBase {
    /// 创建新的适配器基础实例
    pub fn new(
        risk_engine: Arc<dyn RiskEngine>,
        cache_service: Arc<dyn CacheService>,
        metrics_collector: Arc<dyn MetricsCollector>,
        engine_type: EngineType,
        config: AdapterConfig,
    ) -> Self {
        Self {
            risk_engine,
            cache_service,
            metrics_collector,
            engine_type,
            config,
        }
    }
    
    /// 获取风控引擎
    pub fn risk_engine(&self) -> &Arc<dyn RiskEngine> {
        &self.risk_engine
    }
    
    /// 获取缓存服务
    pub fn cache_service(&self) -> &Arc<dyn CacheService> {
        &self.cache_service
    }
    
    /// 获取指标收集器
    pub fn metrics_collector(&self) -> &Arc<dyn MetricsCollector> {
        &self.metrics_collector
    }
    
    /// 获取引擎类型
    pub fn engine_type(&self) -> EngineType {
        self.engine_type
    }
    
    /// 获取配置
    pub fn config(&self) -> &AdapterConfig {
        &self.config
    }
    
    /// 生成缓存键
    pub fn generate_cache_key(&self, prefix: &str, order: &Order) -> String {
        format!(
            "{}:{}:{}:{}:{}",
            prefix,
            self.engine_type.to_string().to_lowercase(),
            order.trading_pair.base,
            order.trading_pair.quote,
            format!("{:?}", order.order_type).to_lowercase()
        )
    }
    
    /// 带缓存的风控检查
    pub async fn cached_risk_check<F, Fut>(&self, cache_key: &str, check_fn: F) -> SigmaXResult<bool>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = SigmaXResult<bool>>,
    {
        let start_time = Instant::now();
        
        // 尝试从缓存获取
        if self.config.enable_cache {
            match self.cache_service.get_string(cache_key).await {
                Ok(Some(cached_str)) => {
                    let cached_result = cached_str == "true";
                    if self.config.enable_metrics {
                        self.metrics_collector.record_cache_hit().await;
                        self.metrics_collector.record_latency("cache_hit", start_time.elapsed()).await;
                    }
                    debug!("Cache hit for key: {}", cache_key);
                    return Ok(cached_result);
                }
                Ok(None) => {
                    debug!("Cache miss for key: {}", cache_key);
                }
                Err(e) => {
                    warn!("Cache error for key {}: {}", cache_key, e);
                }
            }
        }
        
        // 执行实际检查
        let result = check_fn().await;
        
        // 缓存结果
        if self.config.enable_cache && result.is_ok() {
            let cache_value = if *result.as_ref().unwrap() { "true" } else { "false" };
            if let Err(e) = self.cache_service.set_string(cache_key, cache_value.to_string(), self.config.cache_ttl).await {
                warn!("Failed to cache result for key {}: {}", cache_key, e);
            }
        }
        
        // 记录指标
        if self.config.enable_metrics {
            let elapsed = start_time.elapsed();
            self.metrics_collector.record_latency("risk_check", elapsed).await;
            
            match &result {
                Ok(passed) => {
                    self.metrics_collector.record_risk_check(*passed).await;
                }
                Err(_) => {
                    self.metrics_collector.record_risk_check(false).await;
                }
            }
        }
        
        result
    }
    
    /// 带重试的操作执行
    pub async fn execute_with_retry<F, Fut, T>(&self, operation: F) -> SigmaXResult<T>
    where
        F: Fn() -> Fut,
        Fut: std::future::Future<Output = SigmaXResult<T>>,
    {
        let mut last_error = None;
        
        for attempt in 0..=self.config.retry_attempts {
            match tokio::time::timeout(self.config.timeout, operation()).await {
                Ok(Ok(result)) => return Ok(result),
                Ok(Err(e)) => {
                    warn!("Operation failed on attempt {}: {}", attempt + 1, e);
                    last_error = Some(e);
                    
                    if attempt < self.config.retry_attempts {
                        let delay = Duration::from_millis(100 * (attempt + 1) as u64);
                        tokio::time::sleep(delay).await;
                    }
                }
                Err(_) => {
                    let timeout_error = SigmaXError::Internal(
                        format!("Operation timed out after {:?}", self.config.timeout)
                    );
                    warn!("Operation timed out on attempt {}", attempt + 1);
                    last_error = Some(timeout_error);
                    
                    if attempt < self.config.retry_attempts {
                        let delay = Duration::from_millis(100 * (attempt + 1) as u64);
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }
        
        Err(last_error.unwrap_or_else(|| {
            SigmaXError::Internal("All retry attempts failed".to_string())
        }))
    }
    
    /// 批量处理订单风控检查
    pub async fn batch_check_orders(&self, orders: &[Order]) -> SigmaXResult<Vec<bool>> {
        debug!("Batch checking {} orders", orders.len());
        
        let mut results = Vec::with_capacity(orders.len());
        
        // 这里可以实现批量优化逻辑
        // 例如：批量缓存查询、并行处理等
        
        for order in orders {
            let result = self.check_single_order(order).await?;
            results.push(result);
        }
        
        Ok(results)
    }
    
    /// 检查单个订单 (内部方法)
    async fn check_single_order(&self, order: &Order) -> SigmaXResult<bool> {
        let cache_key = self.generate_cache_key("order_risk", order);
        
        self.cached_risk_check(&cache_key, || async {
            // 调用底层风控引擎
            let risk_result = self.risk_engine.check_order_risk(order, None).await?;
            Ok(risk_result.passed)
        }).await
    }
    
    /// 获取适配器指标
    pub async fn get_adapter_metrics(&self) -> SigmaXResult<AdapterMetrics> {
        // 这里应该从指标收集器获取实际数据
        // 目前返回模拟数据
        Ok(AdapterMetrics {
            engine_type: self.engine_type,
            cache_hit_rate: 0.85, // 85% 缓存命中率
            avg_latency_ms: 5.0,   // 5ms 平均延迟
            throughput_rps: 1000.0, // 1000 RPS
            error_rate: 0.01,      // 1% 错误率
        })
    }
}

/// 适配器特化trait - 用于不同引擎类型的特定优化
#[async_trait]
pub trait AdapterSpecialization: Send + Sync {
    /// 引擎特定的订单风控检查
    async fn specialized_order_check(&self, order: &Order) -> SigmaXResult<bool>;
    
    /// 引擎特定的持仓风控检查
    async fn specialized_position_check(&self, balances: &[Balance]) -> SigmaXResult<bool>;
    
    /// 引擎特定的优化策略
    fn optimization_strategy(&self) -> OptimizationStrategy;
}

/// 优化策略
#[derive(Debug, Clone, PartialEq)]
pub enum OptimizationStrategy {
    /// 高吞吐量优化 (回测)
    HighThroughput {
        batch_size: usize,
        parallel_processing: bool,
    },
    /// 低延迟优化 (实盘)
    LowLatency {
        hot_cache_size: usize,
        precompute_results: bool,
    },
    /// 详细结果优化 (Web API)
    DetailedResults {
        include_rule_details: bool,
        max_response_size: usize,
    },
    /// 策略切换优化 (策略模块)
    StrategySwitching {
        risk_assessment_mode: bool,
        performance_analysis: bool,
    },
}

/// 适配器工厂trait
#[async_trait]
pub trait AdapterFactory: Send + Sync {
    /// 创建适配器
    async fn create_adapter(&self, engine_type: EngineType) -> SigmaXResult<Arc<dyn EngineRiskAdapter>>;
    
    /// 支持的引擎类型
    fn supported_engine_types(&self) -> Vec<EngineType>;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_adapter_config_default() {
        let config = AdapterConfig::default();
        assert!(config.enable_cache);
        assert_eq!(config.cache_ttl, Duration::from_secs(60));
        assert!(config.enable_metrics);
        assert_eq!(config.retry_attempts, 3);
    }
    
    #[test]
    fn test_optimization_strategy() {
        let strategy = OptimizationStrategy::HighThroughput {
            batch_size: 100,
            parallel_processing: true,
        };
        
        match strategy {
            OptimizationStrategy::HighThroughput { batch_size, .. } => {
                assert_eq!(batch_size, 100);
            }
            _ => panic!("Unexpected strategy type"),
        }
    }
    
    #[tokio::test]
    async fn test_adapter_metrics() {
        let metrics = AdapterMetrics {
            engine_type: EngineType::Backtest,
            cache_hit_rate: 0.9,
            avg_latency_ms: 3.0,
            throughput_rps: 2000.0,
            error_rate: 0.005,
        };
        
        assert_eq!(metrics.engine_type, EngineType::Backtest);
        assert_eq!(metrics.cache_hit_rate, 0.9);
    }
}
