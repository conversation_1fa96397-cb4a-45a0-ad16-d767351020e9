//! 统一风控规则仓储实现
//! 
//! 基于SQLx实现的统一风控规则数据访问层

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, SigmaXError};
use sigmax_database::DatabaseManager;
use std::sync::Arc;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde_json::Value;
use tracing::{debug, info};
use sqlx::Row;
use rust_decimal::prelude::ToPrimitive;

// 重新导入统一规则引擎的类型
use crate::unified_engine::{
    UnifiedRiskRepository, UnifiedRiskRule, RuleExecutionRecord, RuleResult, ExecutionHistoryRecord
};

/// SQLx实现的统一风控规则仓储
pub struct SqlUnifiedRiskRepository {
    db: Arc<DatabaseManager>,
}

impl SqlUnifiedRiskRepository {
    /// 创建新的仓储实例
    pub fn new(db: Arc<DatabaseManager>) -> Self {
        Self { db }
    }
    
    /// 将数据库行转换为规则对象
    fn row_to_rule(&self, row: sqlx::postgres::PgRow) -> SigmaXResult<UnifiedRiskRule> {
        use sqlx::Row;
        
        let trading_pairs_json: Value = row.try_get("trading_pairs")
            .map_err(|e| SigmaXError::Database(format!("获取trading_pairs失败: {}", e)))?;
            
        let trading_pairs = trading_pairs_json.as_array()
            .unwrap_or(&vec![])
            .iter()
            .filter_map(|v| v.as_str().map(|s| s.to_string()))
            .collect();
        
        Ok(UnifiedRiskRule {
            id: row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取id失败: {}", e)))?,
            name: row.try_get("name")
                .map_err(|e| SigmaXError::Database(format!("获取name失败: {}", e)))?,
            description: row.try_get("description")
                .map_err(|e| SigmaXError::Database(format!("获取description失败: {}", e)))?,
            category: row.try_get("category")
                .map_err(|e| SigmaXError::Database(format!("获取category失败: {}", e)))?,
            rule_type: row.try_get("rule_type")
                .map_err(|e| SigmaXError::Database(format!("获取rule_type失败: {}", e)))?,
            parameters: row.try_get("parameters")
                .map_err(|e| SigmaXError::Database(format!("获取parameters失败: {}", e)))?,
            conditions: row.try_get("conditions")
                .map_err(|e| SigmaXError::Database(format!("获取conditions失败: {}", e)))?,
            enabled: row.try_get("enabled")
                .map_err(|e| SigmaXError::Database(format!("获取enabled失败: {}", e)))?,
            priority: row.try_get("priority")
                .map_err(|e| SigmaXError::Database(format!("获取priority失败: {}", e)))?,
            strategy_type: row.try_get("strategy_type")
                .map_err(|e| SigmaXError::Database(format!("获取strategy_type失败: {}", e)))?,
            trading_pairs,
            execution_count: row.try_get::<i64, _>("execution_count").unwrap_or(0) as u64,
            success_count: row.try_get::<i64, _>("success_count").unwrap_or(0) as u64,
            failure_count: row.try_get::<i64, _>("failure_count").unwrap_or(0) as u64,
            last_executed_at: row.try_get("last_executed_at").ok(),
            created_at: row.try_get("created_at")
                .map_err(|e| SigmaXError::Database(format!("获取created_at失败: {}", e)))?,
            updated_at: row.try_get("updated_at")
                .map_err(|e| SigmaXError::Database(format!("获取updated_at失败: {}", e)))?,
        })
    }
}

#[async_trait]
impl UnifiedRiskRepository for SqlUnifiedRiskRepository {
    async fn get_strategy_ids_by_type(&self, strategy_type: &str) -> SigmaXResult<Vec<Uuid>> {
        SqlUnifiedRiskRepository::get_strategy_ids_by_type(self, strategy_type).await
    }

    async fn get_trade_count_for_strategies_today(&self, strategy_ids: &[Uuid]) -> SigmaXResult<u64> {
        SqlUnifiedRiskRepository::get_trade_count_for_strategies_today(self, strategy_ids).await
    }

    async fn get_trade_count_for_strategies_today_with_filter(&self, strategy_ids: &[Uuid], exclude_cancelled: bool) -> SigmaXResult<u64> {
        SqlUnifiedRiskRepository::get_trade_count_for_strategies_today_with_filter(self, strategy_ids, exclude_cancelled).await
    }

    async fn get_historical_net_asset_values(&self, strategy_id: Uuid) -> SigmaXResult<Vec<rust_decimal::Decimal>> {
        SqlUnifiedRiskRepository::get_historical_net_asset_values(self, strategy_id).await
    }
    
    /// 获取所有启用的规则
    async fn get_enabled_rules(&self) -> SigmaXResult<Vec<UnifiedRiskRule>> {
        let pool = self.db.pool();
        
        debug!("查询所有启用的风控规则");
        
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, category, rule_type, parameters, conditions,
                   enabled, priority, strategy_type, trading_pairs,
                   execution_count, success_count, failure_count, last_executed_at,
                   created_at, updated_at
            FROM unified_risk_rules
            WHERE enabled = true AND is_active = true
            ORDER BY priority DESC, created_at ASC
            "#
        )
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询启用规则失败: {}", e)))?;
        
        let mut rules = Vec::new();
        for row in rows {
            rules.push(self.row_to_rule(row)?);
        }
        
        info!("成功查询到 {} 条启用的风控规则", rules.len());
        Ok(rules)
    }
    
    /// 根据策略类型获取规则
    async fn get_rules_by_strategy(&self, strategy_type: &str) -> SigmaXResult<Vec<UnifiedRiskRule>> {
        let pool = self.db.pool();
        
        debug!("查询策略类型 {} 的风控规则", strategy_type);
        
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, category, rule_type, parameters, conditions,
                   enabled, priority, strategy_type, trading_pairs,
                   execution_count, success_count, failure_count, last_executed_at,
                   created_at, updated_at
            FROM unified_risk_rules
            WHERE enabled = true AND is_active = true
            AND (strategy_type = $1 OR strategy_type IS NULL)
            ORDER BY priority DESC, created_at ASC
            "#
        )
        .bind(strategy_type)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询策略规则失败: {}", e)))?;
        
        let mut rules = Vec::new();
        for row in rows {
            rules.push(self.row_to_rule(row)?);
        }
        
        info!("成功查询到策略 {} 的 {} 条风控规则", strategy_type, rules.len());
        Ok(rules)
    }
    
    /// 根据规则类型获取规则
    async fn get_rules_by_type(&self, rule_type: &str) -> SigmaXResult<Vec<UnifiedRiskRule>> {
        let pool = self.db.pool();
        
        debug!("查询规则类型 {} 的风控规则", rule_type);
        
        let rows = sqlx::query(
            r#"
            SELECT id, name, description, category, rule_type, parameters, conditions,
                   enabled, priority, strategy_type, trading_pairs,
                   execution_count, success_count, failure_count, last_executed_at,
                   created_at, updated_at
            FROM unified_risk_rules
            WHERE enabled = true AND is_active = true AND rule_type = $1
            ORDER BY priority DESC, created_at ASC
            "#
        )
        .bind(rule_type)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询规则类型失败: {}", e)))?;
        
        let mut rules = Vec::new();
        for row in rows {
            rules.push(self.row_to_rule(row)?);
        }
        
        info!("成功查询到规则类型 {} 的 {} 条风控规则", rule_type, rules.len());
        Ok(rules)
    }
    
    /// 保存规则执行记录
    async fn save_execution_record(&self, record: &RuleExecutionRecord) -> SigmaXResult<()> {
        let pool = self.db.pool();
        
        let result_str = match &record.result {
            RuleResult::Pass => "PASS",
            RuleResult::Fail(_) => "FAIL",
            RuleResult::Error(_) => "ERROR",
            RuleResult::Skip(_) => "SKIP",
        };
        
        let message = record.result.message().unwrap_or("").to_string();
        
        sqlx::query(
            r#"
            INSERT INTO risk_rule_executions (
                rule_id, execution_context, result, message, execution_time_ms, executed_at
            ) VALUES ($1, $2, $3, $4, $5, $6)
            "#
        )
        .bind(record.rule_id)
        .bind(serde_json::to_value(&record.execution_context)
              .map_err(|e| SigmaXError::Serialization(format!("序列化执行上下文失败: {}", e)))?)
        .bind(result_str)
        .bind(message)
        .bind(record.execution_time_ms as i32)
        .bind(record.executed_at)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("保存执行记录失败: {}", e)))?;
        
        debug!("成功保存规则执行记录: {}", record.rule_id);
        Ok(())
    }
    
    /// 更新规则统计
    async fn update_rule_stats(&self, rule_id: Uuid, result: &RuleResult) -> SigmaXResult<()> {
        let pool = self.db.pool();
        
        let (execution_count_inc, success_count_inc, failure_count_inc) = match result {
            RuleResult::Pass => (1, 1, 0),
            RuleResult::Fail(_) | RuleResult::Error(_) => (1, 0, 1),
            RuleResult::Skip(_) => (0, 0, 0), // 跳过的规则不计入统计
        };
        
        sqlx::query(
            r#"
            UPDATE unified_risk_rules 
            SET execution_count = execution_count + $1,
                success_count = success_count + $2,
                failure_count = failure_count + $3,
                last_executed_at = NOW()
            WHERE id = $4
            "#
        )
        .bind(execution_count_inc)
        .bind(success_count_inc)
        .bind(failure_count_inc)
        .bind(rule_id)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("更新规则统计失败: {}", e)))?;
        
        debug!("成功更新规则统计: {}", rule_id);
        Ok(())
    }

    /// 获取规则执行历史
    async fn get_execution_history(
        &self,
        rule_id: Uuid,
        page: usize,
        per_page: usize,
        result_filter: Option<&str>,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
    ) -> SigmaXResult<(Vec<ExecutionHistoryRecord>, usize)> {
        let pool = self.db.pool();

        debug!("查询规则执行历史: rule_id={}, page={}, per_page={}", rule_id, page, per_page);

        // 构建查询条件
        let mut where_conditions = vec!["rule_id = $1".to_string()];
        let mut param_index = 2;

        if result_filter.is_some() {
            where_conditions.push(format!("result = ${}", param_index));
            param_index += 1;
        }

        if start_time.is_some() {
            where_conditions.push(format!("executed_at >= ${}", param_index));
            param_index += 1;
        }

        if end_time.is_some() {
            where_conditions.push(format!("executed_at <= ${}", param_index));
            param_index += 1;
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询总数
        let count_query = format!(
            "SELECT COUNT(*) FROM risk_rule_executions {}",
            where_clause
        );

        let mut count_sql = sqlx::query_scalar::<_, i64>(&count_query).bind(rule_id);

        if let Some(result) = result_filter {
            count_sql = count_sql.bind(result);
        }
        if let Some(start) = start_time {
            count_sql = count_sql.bind(start);
        }
        if let Some(end) = end_time {
            count_sql = count_sql.bind(end);
        }

        let total_count = count_sql
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询执行历史总数失败: {}", e)))? as usize;

        // 查询历史记录
        let offset = (page - 1) * per_page;
        let history_query = format!(
            r#"
            SELECT id, rule_id, execution_context, result, message, execution_time_ms, executed_at
            FROM risk_rule_executions
            {}
            ORDER BY executed_at DESC
            LIMIT ${} OFFSET ${}
            "#,
            where_clause,
            param_index,
            param_index + 1
        );

        let mut history_sql = sqlx::query(&history_query)
            .bind(rule_id);

        if let Some(result) = result_filter {
            history_sql = history_sql.bind(result);
        }
        if let Some(start) = start_time {
            history_sql = history_sql.bind(start);
        }
        if let Some(end) = end_time {
            history_sql = history_sql.bind(end);
        }

        history_sql = history_sql.bind(per_page as i64).bind(offset as i64);

        let rows = history_sql
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询执行历史失败: {}", e)))?;

        let mut records = Vec::new();
        for row in rows {
            let record = ExecutionHistoryRecord {
                id: row.get("id"),
                rule_id: row.get("rule_id"),
                execution_context: row.get("execution_context"),
                result: row.get("result"),
                message: row.get("message"),
                execution_time_ms: row.get("execution_time_ms"),
                executed_at: row.get("executed_at"),
            };
            records.push(record);
        }

        info!("成功查询到 {} 条执行历史记录，总数: {}", records.len(), total_count);
        Ok((records, total_count))
    }

    async fn get_rule(&self, rule_id: Uuid) -> SigmaXResult<Option<UnifiedRiskRule>> {
        SqlUnifiedRiskRepository::get_rule(self, rule_id).await
    }

    async fn update_rule(&self, rule: &UnifiedRiskRule) -> SigmaXResult<()> {
        SqlUnifiedRiskRepository::update_rule(self, rule).await
    }

    async fn get_rules_with_filter(
        &self,
        filter: &crate::unified_engine::RuleFilter,
        page: u64,
        per_page: u64,
    ) -> SigmaXResult<Vec<UnifiedRiskRule>> {
        SqlUnifiedRiskRepository::get_rules_with_filter(self, filter, page, per_page).await
    }

    async fn count_rules_with_filter(&self, filter: &crate::unified_engine::RuleFilter) -> SigmaXResult<u64> {
        SqlUnifiedRiskRepository::count_rules_with_filter(self, filter).await
    }

    // ===== rule_helpers 需要的新方法实现 =====

    async fn get_sliding_hour_trade_count(&self, strategy_type: Option<&str>) -> SigmaXResult<u64> {
        SqlUnifiedRiskRepository::get_sliding_hour_trade_count(self, strategy_type).await
    }

    async fn get_current_hour_trade_count(&self, strategy_type: Option<&str>) -> SigmaXResult<u64> {
        SqlUnifiedRiskRepository::get_current_hour_trade_count(self, strategy_type).await
    }

    async fn get_last_order_time_for_pair(
        &self,
        trading_pair: &str,
        strategy_type: Option<&str>
    ) -> SigmaXResult<Option<DateTime<Utc>>> {
        SqlUnifiedRiskRepository::get_last_order_time_for_pair(self, trading_pair, strategy_type).await
    }

    async fn get_last_order_time_global(&self, strategy_type: Option<&str>) -> SigmaXResult<Option<DateTime<Utc>>> {
        SqlUnifiedRiskRepository::get_last_order_time_global(self, strategy_type).await
    }

    async fn get_historical_prices(&self, trading_pair: &str, periods: usize) -> SigmaXResult<Vec<f64>> {
        SqlUnifiedRiskRepository::get_historical_prices(self, trading_pair, periods).await
    }

    async fn get_portfolio_drawdown_data(&self, strategy_ids: &[Uuid]) -> SigmaXResult<(f64, f64, f64)> {
        SqlUnifiedRiskRepository::get_portfolio_drawdown_data(self, strategy_ids).await
    }

    async fn get_position_values(&self, strategy_ids: &[Uuid]) -> SigmaXResult<f64> {
        SqlUnifiedRiskRepository::get_position_values(self, strategy_ids).await
    }

    async fn get_current_balances(&self, exchange_id: &str) -> SigmaXResult<Vec<(String, f64, f64)>> {
        SqlUnifiedRiskRepository::get_current_balances(self, exchange_id).await
    }

    // ===== 新增的回撤计算相关方法实现 =====

    async fn get_portfolio_snapshots(&self, strategy_ids: &[Uuid], limit: usize) -> SigmaXResult<Vec<(DateTime<Utc>, f64)>> {
        SqlUnifiedRiskRepository::get_portfolio_snapshots(self, strategy_ids, limit).await
    }

    async fn get_portfolio_id_by_strategy(&self, strategy_id: Uuid) -> SigmaXResult<Option<Uuid>> {
        SqlUnifiedRiskRepository::get_portfolio_id_by_strategy(self, strategy_id).await
    }

    async fn create_portfolio_snapshot(&self, portfolio_id: Uuid, strategy_id: Option<Uuid>, total_value: f64, cash_balance: f64, position_value: f64) -> SigmaXResult<()> {
        SqlUnifiedRiskRepository::create_portfolio_snapshot(self, portfolio_id, strategy_id, total_value, cash_balance, position_value).await
    }

    async fn get_portfolio_peak_value(&self, portfolio_id: Uuid) -> SigmaXResult<f64> {
        SqlUnifiedRiskRepository::get_portfolio_peak_value(self, portfolio_id).await
    }

    async fn update_portfolio_drawdown(&self, portfolio_id: Uuid, current_value: f64, peak_value: f64, drawdown: f64) -> SigmaXResult<()> {
        SqlUnifiedRiskRepository::update_portfolio_drawdown(self, portfolio_id, current_value, peak_value, drawdown).await
    }

    // ===== 市场流动性相关方法实现 =====

    async fn get_market_liquidity_data(&self, trading_pair: &str, exchange_id: Option<i32>) -> SigmaXResult<Option<(f64, f64, f64)>> {
        SqlUnifiedRiskRepository::get_market_liquidity_data(self, trading_pair, exchange_id).await
    }

    async fn get_latest_liquidity_score(&self, trading_pair: &str) -> SigmaXResult<f64> {
        SqlUnifiedRiskRepository::get_latest_liquidity_score(self, trading_pair).await
    }

    // ===== 交易时间相关方法实现 =====

    async fn is_trading_pair_open(&self, trading_pair: &str, exchange_name: Option<&str>) -> SigmaXResult<bool> {
        SqlUnifiedRiskRepository::is_trading_pair_open(self, trading_pair, exchange_name).await
    }

    async fn get_exchange_trading_hours(&self, exchange_name: &str, trading_pair: Option<&str>) -> SigmaXResult<Option<(bool, String)>> {
        SqlUnifiedRiskRepository::get_exchange_trading_hours(self, exchange_name, trading_pair).await
    }

    // ===== 风险预算相关方法实现 =====

    async fn get_risk_budget_config(&self, strategy_type: Option<&str>) -> SigmaXResult<Option<(f64, f64, f64)>> {
        SqlUnifiedRiskRepository::get_risk_budget_config(self, strategy_type).await
    }

    async fn get_strategy_risk_allocation(&self, strategy_type: &str) -> SigmaXResult<Option<f64>> {
        SqlUnifiedRiskRepository::get_strategy_risk_allocation(self, strategy_type).await
    }

    async fn calculate_current_risk_usage(&self, strategy_ids: &[Uuid]) -> SigmaXResult<f64> {
        SqlUnifiedRiskRepository::calculate_current_risk_usage(self, strategy_ids).await
    }

    async fn create_risk_budget_usage_record(&self, strategy_type: Option<&str>, strategy_id: Option<Uuid>, total_budget: f64, used_budget: f64, usage_percentage: f64) -> SigmaXResult<()> {
        SqlUnifiedRiskRepository::create_risk_budget_usage_record(self, strategy_type, strategy_id, total_budget, used_budget, usage_percentage).await
    }
}

impl SqlUnifiedRiskRepository {
    /// 根据策略类型获取策略ID列表
    pub async fn get_strategy_ids_by_type(&self, strategy_type: &str) -> SigmaXResult<Vec<Uuid>> {
        let pool = self.db.pool();
        let ids = sqlx::query_scalar("SELECT id FROM strategies WHERE strategy_type = $1")
            .bind(strategy_type)
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询策略ID失败: {}", e)))?;
        Ok(ids)
    }

    /// 获取指定策略今日的订单数量
    pub async fn get_trade_count_for_strategies_today(&self, strategy_ids: &[Uuid]) -> SigmaXResult<u64> {
        if strategy_ids.is_empty() {
            return Ok(0);
        }
        let pool = self.db.pool();
        let count: i64 = sqlx::query_scalar(
            r#"
            SELECT COUNT(*) FROM orders
            WHERE created_at >= date_trunc('day', NOW() AT TIME ZONE 'UTC')
            AND strategy_id = ANY($1)
            "#
        )
        .bind(strategy_ids)
        .fetch_one(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询今日订单数量失败: {}", e)))?;
        Ok(count as u64)
    }

    /// 获取指定策略今日的订单数量（支持排除已取消订单）
    pub async fn get_trade_count_for_strategies_today_with_filter(&self, strategy_ids: &[Uuid], exclude_cancelled: bool) -> SigmaXResult<u64> {
        if strategy_ids.is_empty() {
            return Ok(0);
        }

        let pool = self.db.pool();
        let query = if exclude_cancelled {
            r#"
            SELECT COUNT(*) FROM orders
            WHERE created_at >= date_trunc('day', NOW() AT TIME ZONE 'UTC')
            AND strategy_id = ANY($1)
            AND status != 'Cancelled'
            "#
        } else {
            r#"
            SELECT COUNT(*) FROM orders
            WHERE created_at >= date_trunc('day', NOW() AT TIME ZONE 'UTC')
            AND strategy_id = ANY($1)
            "#
        };

        let count: i64 = sqlx::query_scalar(query)
            .bind(strategy_ids)
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询今日订单数量失败: {}", e)))?;

        Ok(count as u64)
    }

    /// 获取策略的历史资产净值
    pub async fn get_historical_net_asset_values(&self, strategy_id: Uuid) -> SigmaXResult<Vec<rust_decimal::Decimal>> {
        let pool = self.db.pool();

        // 1. 找到该策略最近一次的回测ID
        let latest_backtest: Option<(Uuid,)> = sqlx::query_as(
            "SELECT id FROM backtest_results WHERE strategy_id = $1 ORDER BY start_time DESC LIMIT 1"
        )
        .bind(strategy_id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询最新回测ID失败: {}", e)))?;

        let Some((backtest_id,)) = latest_backtest else {
            // 如果没有找到回测记录，返回空列表
            return Ok(Vec::new());
        };

        // 2. 根据回测ID获取所有投资组合快照的总价值
        let values: Vec<(rust_decimal::Decimal,)> = sqlx::query_as(
            "SELECT total_value FROM backtest_portfolio_snapshots WHERE backtest_id = $1 ORDER BY timestamp ASC"
        )
        .bind(backtest_id)
        .fetch_all(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询历史净值失败: {}", e)))?;

        Ok(values.into_iter().map(|(v,)| v).collect())
    }

    /// 创建新规则
    pub async fn create_rule(&self, rule: &UnifiedRiskRule) -> SigmaXResult<()> {
        let pool = self.db.pool();
        
        let trading_pairs_json = serde_json::to_value(&rule.trading_pairs)
            .map_err(|e| SigmaXError::Serialization(format!("序列化交易对失败: {}", e)))?;
        
        sqlx::query(
            r#"
            INSERT INTO unified_risk_rules (
                id, name, description, category, rule_type, parameters, conditions,
                enabled, priority, strategy_type, trading_pairs, created_by
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            "#
        )
        .bind(rule.id)
        .bind(&rule.name)
        .bind(&rule.description)
        .bind(&rule.category)
        .bind(&rule.rule_type)
        .bind(&rule.parameters)
        .bind(&rule.conditions)
        .bind(rule.enabled)
        .bind(rule.priority)
        .bind(&rule.strategy_type)
        .bind(trading_pairs_json)
        .bind("system") // 默认创建者
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("创建规则失败: {}", e)))?;
        
        info!("成功创建风控规则: {} ({})", rule.name, rule.id);
        Ok(())
    }
    
    /// 更新规则
    pub async fn update_rule(&self, rule: &UnifiedRiskRule) -> SigmaXResult<()> {
        let pool = self.db.pool();
        
        let trading_pairs_json = serde_json::to_value(&rule.trading_pairs)
            .map_err(|e| SigmaXError::Serialization(format!("序列化交易对失败: {}", e)))?;
        
        sqlx::query(
            r#"
            UPDATE unified_risk_rules 
            SET name = $1, description = $2, category = $3, rule_type = $4,
                parameters = $5, conditions = $6, enabled = $7, priority = $8,
                strategy_type = $9, trading_pairs = $10, updated_at = NOW(),
                version = version + 1
            WHERE id = $11
            "#
        )
        .bind(&rule.name)
        .bind(&rule.description)
        .bind(&rule.category)
        .bind(&rule.rule_type)
        .bind(&rule.parameters)
        .bind(&rule.conditions)
        .bind(rule.enabled)
        .bind(rule.priority)
        .bind(&rule.strategy_type)
        .bind(trading_pairs_json)
        .bind(rule.id)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("更新规则失败: {}", e)))?;
        
        info!("成功更新风控规则: {} ({})", rule.name, rule.id);
        Ok(())
    }
    
    /// 删除规则（软删除）
    pub async fn delete_rule(&self, rule_id: Uuid) -> SigmaXResult<()> {
        let pool = self.db.pool();
        
        sqlx::query(
            r#"
            UPDATE unified_risk_rules 
            SET is_active = false, updated_at = NOW()
            WHERE id = $1
            "#
        )
        .bind(rule_id)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("删除规则失败: {}", e)))?;
        
        info!("成功删除风控规则: {}", rule_id);
        Ok(())
    }
    
    /// 启用/禁用规则
    pub async fn toggle_rule(&self, rule_id: Uuid, enabled: bool) -> SigmaXResult<()> {
        let pool = self.db.pool();
        
        sqlx::query(
            r#"
            UPDATE unified_risk_rules 
            SET enabled = $1, updated_at = NOW()
            WHERE id = $2
            "#
        )
        .bind(enabled)
        .bind(rule_id)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("切换规则状态失败: {}", e)))?;
        
        info!("成功{}风控规则: {}", if enabled { "启用" } else { "禁用" }, rule_id);
        Ok(())
    }
    
    /// 获取规则执行统计
    pub async fn get_rule_execution_stats(&self, rule_id: Uuid) -> SigmaXResult<(u64, u64, u64)> {
        let pool = self.db.pool();
        
        let row = sqlx::query(
            r#"
            SELECT execution_count, success_count, failure_count
            FROM unified_risk_rules
            WHERE id = $1
            "#
        )
        .bind(rule_id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询规则统计失败: {}", e)))?;
        
        match row {
            Some(row) => {
                use sqlx::Row;
                Ok((
                    row.try_get::<i64, _>("execution_count").unwrap_or(0) as u64,
                    row.try_get::<i64, _>("success_count").unwrap_or(0) as u64,
                    row.try_get::<i64, _>("failure_count").unwrap_or(0) as u64,
                ))
            }
            None => Err(SigmaXError::NotFound(format!("规则不存在: {}", rule_id)))
        }
    }

    /// 获取单个规则
    pub async fn get_rule(&self, rule_id: Uuid) -> SigmaXResult<Option<UnifiedRiskRule>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT id, name, description, category, rule_type, parameters, conditions,
                   enabled, priority, strategy_type, trading_pairs,
                   execution_count, success_count, failure_count, last_executed_at,
                   created_at, updated_at
            FROM unified_risk_rules
            WHERE id = $1 AND is_active = true
            "#
        )
        .bind(rule_id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询规则失败: {}", e)))?;

        if let Some(row) = row {
            let trading_pairs: serde_json::Value = row.try_get("trading_pairs").unwrap_or(serde_json::json!([]));
            let trading_pairs_vec: Vec<String> = serde_json::from_value(trading_pairs)
                .map_err(|e| SigmaXError::Serialization(format!("反序列化交易对失败: {}", e)))?;

            let rule = UnifiedRiskRule {
                id: row.get("id"),
                name: row.get("name"),
                description: row.get("description"),
                category: row.get("category"),
                rule_type: row.get("rule_type"),
                parameters: row.get("parameters"),
                conditions: row.try_get("conditions").ok(),
                enabled: row.get("enabled"),
                priority: row.get("priority"),
                strategy_type: row.try_get("strategy_type").ok(),
                trading_pairs: trading_pairs_vec,
                execution_count: row.try_get::<i64, _>("execution_count").unwrap_or(0) as u64,
                success_count: row.try_get::<i64, _>("success_count").unwrap_or(0) as u64,
                failure_count: row.try_get::<i64, _>("failure_count").unwrap_or(0) as u64,
                last_executed_at: row.try_get("last_executed_at").ok(),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            };

            Ok(Some(rule))
        } else {
            Ok(None)
        }
    }

    /// 根据过滤条件获取规则列表
    pub async fn get_rules_with_filter(
        &self,
        filter: &crate::unified_engine::RuleFilter,
        page: u64,
        per_page: u64,
    ) -> SigmaXResult<Vec<UnifiedRiskRule>> {
        let pool = self.db.pool();
        let offset = (page - 1) * per_page;

        // 构建查询条件
        let mut where_conditions = vec!["is_active = true".to_string()];
        let mut param_index = 0;

        if let Some(category) = &filter.category {
            param_index += 1;
            where_conditions.push(format!("category = ${}", param_index));
        }

        if let Some(rule_type) = &filter.rule_type {
            param_index += 1;
            where_conditions.push(format!("rule_type = ${}", param_index));
        }

        if let Some(enabled) = filter.enabled {
            param_index += 1;
            where_conditions.push(format!("enabled = ${}", param_index));
        }

        if let Some(strategy_type) = &filter.strategy_type {
            param_index += 1;
            where_conditions.push(format!("(strategy_type = ${} OR strategy_type IS NULL)", param_index));
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        let query = format!(
            r#"
            SELECT id, name, description, category, rule_type, parameters, conditions,
                   enabled, priority, strategy_type, trading_pairs,
                   execution_count, success_count, failure_count, last_executed_at,
                   created_at, updated_at
            FROM unified_risk_rules
            {}
            ORDER BY priority DESC, created_at ASC
            LIMIT ${} OFFSET ${}
            "#,
            where_clause,
            param_index + 1,
            param_index + 2
        );

        let mut query_builder = sqlx::query(&query);

        // 绑定参数
        if let Some(category) = &filter.category {
            query_builder = query_builder.bind(category);
        }
        if let Some(rule_type) = &filter.rule_type {
            query_builder = query_builder.bind(rule_type);
        }
        if let Some(enabled) = filter.enabled {
            query_builder = query_builder.bind(enabled);
        }
        if let Some(strategy_type) = &filter.strategy_type {
            query_builder = query_builder.bind(strategy_type);
        }

        query_builder = query_builder.bind(per_page as i64).bind(offset as i64);

        let rows = query_builder
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询规则列表失败: {}", e)))?;

        let mut rules = Vec::new();
        for row in rows {
            let trading_pairs: serde_json::Value = row.try_get("trading_pairs").unwrap_or(serde_json::json!([]));
            let trading_pairs_vec: Vec<String> = serde_json::from_value(trading_pairs)
                .map_err(|e| SigmaXError::Serialization(format!("反序列化交易对失败: {}", e)))?;

            let rule = UnifiedRiskRule {
                id: row.get("id"),
                name: row.get("name"),
                description: row.get("description"),
                category: row.get("category"),
                rule_type: row.get("rule_type"),
                parameters: row.get("parameters"),
                conditions: row.try_get("conditions").ok(),
                enabled: row.get("enabled"),
                priority: row.get("priority"),
                strategy_type: row.try_get("strategy_type").ok(),
                trading_pairs: trading_pairs_vec,
                execution_count: row.try_get::<i64, _>("execution_count").unwrap_or(0) as u64,
                success_count: row.try_get::<i64, _>("success_count").unwrap_or(0) as u64,
                failure_count: row.try_get::<i64, _>("failure_count").unwrap_or(0) as u64,
                last_executed_at: row.try_get("last_executed_at").ok(),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
            };
            rules.push(rule);
        }

        Ok(rules)
    }

    /// 统计符合过滤条件的规则数量
    pub async fn count_rules_with_filter(&self, filter: &crate::unified_engine::RuleFilter) -> SigmaXResult<u64> {
        let pool = self.db.pool();

        // 构建查询条件
        let mut where_conditions = vec!["is_active = true".to_string()];
        let mut param_index = 0;

        if let Some(category) = &filter.category {
            param_index += 1;
            where_conditions.push(format!("category = ${}", param_index));
        }

        if let Some(rule_type) = &filter.rule_type {
            param_index += 1;
            where_conditions.push(format!("rule_type = ${}", param_index));
        }

        if let Some(enabled) = filter.enabled {
            param_index += 1;
            where_conditions.push(format!("enabled = ${}", param_index));
        }

        if let Some(strategy_type) = &filter.strategy_type {
            param_index += 1;
            where_conditions.push(format!("(strategy_type = ${} OR strategy_type IS NULL)", param_index));
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        let query = format!("SELECT COUNT(*) FROM unified_risk_rules {}", where_clause);

        let mut query_builder = sqlx::query_scalar::<_, i64>(&query);

        // 绑定参数
        if let Some(category) = &filter.category {
            query_builder = query_builder.bind(category);
        }
        if let Some(rule_type) = &filter.rule_type {
            query_builder = query_builder.bind(rule_type);
        }
        if let Some(enabled) = filter.enabled {
            query_builder = query_builder.bind(enabled);
        }
        if let Some(strategy_type) = &filter.strategy_type {
            query_builder = query_builder.bind(strategy_type);
        }

        let count = query_builder
            .fetch_one(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("统计规则数量失败: {}", e)))?;

        Ok(count as u64)
    }

    // ===== rule_helpers 需要的新方法实现 =====

    /// 获取滑动窗口小时交易次数
    async fn get_sliding_hour_trade_count(&self, strategy_type: Option<&str>) -> SigmaXResult<u64> {
        let pool = self.db.pool();

        let query = if strategy_type.is_some() {
            r#"
            SELECT COUNT(*) as count
            FROM orders o
            JOIN strategies s ON o.strategy_id = s.id
            WHERE o.created_at >= NOW() - INTERVAL '1 HOUR'
            AND s.strategy_type = $1
            "#
        } else {
            r#"
            SELECT COUNT(*) as count
            FROM orders
            WHERE created_at >= NOW() - INTERVAL '1 HOUR'
            "#
        };

        let row = if let Some(st) = strategy_type {
            sqlx::query(query)
                .bind(st)
                .fetch_one(pool)
                .await
        } else {
            sqlx::query(query)
                .fetch_one(pool)
                .await
        }
        .map_err(|e| SigmaXError::Database(format!("查询滑动窗口小时交易次数失败: {}", e)))?;

        let count: i64 = row.try_get("count")
            .map_err(|e| SigmaXError::Database(format!("获取交易次数失败: {}", e)))?;

        Ok(count as u64)
    }

    /// 获取当前小时交易次数
    async fn get_current_hour_trade_count(&self, strategy_type: Option<&str>) -> SigmaXResult<u64> {
        let pool = self.db.pool();

        let query = if strategy_type.is_some() {
            r#"
            SELECT COUNT(*) as count
            FROM orders o
            JOIN strategies s ON o.strategy_id = s.id
            WHERE EXTRACT(HOUR FROM o.created_at) = EXTRACT(HOUR FROM NOW())
            AND DATE(o.created_at) = CURRENT_DATE
            AND s.strategy_type = $1
            "#
        } else {
            r#"
            SELECT COUNT(*) as count
            FROM orders
            WHERE EXTRACT(HOUR FROM created_at) = EXTRACT(HOUR FROM NOW())
            AND DATE(created_at) = CURRENT_DATE
            "#
        };

        let row = if let Some(st) = strategy_type {
            sqlx::query(query)
                .bind(st)
                .fetch_one(pool)
                .await
        } else {
            sqlx::query(query)
                .fetch_one(pool)
                .await
        }
        .map_err(|e| SigmaXError::Database(format!("查询当前小时交易次数失败: {}", e)))?;

        let count: i64 = row.try_get("count")
            .map_err(|e| SigmaXError::Database(format!("获取交易次数失败: {}", e)))?;

        Ok(count as u64)
    }

    /// 获取指定交易对的最后订单时间
    async fn get_last_order_time_for_pair(
        &self,
        trading_pair: &str,
        strategy_type: Option<&str>
    ) -> SigmaXResult<Option<DateTime<Utc>>> {
        let pool = self.db.pool();

        let query = if strategy_type.is_some() {
            r#"
            SELECT MAX(o.created_at) as last_time
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            JOIN strategies s ON o.strategy_id = s.id
            WHERE tp.symbol = $1 AND s.strategy_type = $2
            "#
        } else {
            r#"
            SELECT MAX(o.created_at) as last_time
            FROM orders o
            JOIN trading_pairs tp ON o.trading_pair_id = tp.id
            WHERE tp.symbol = $1
            "#
        };

        let row = if let Some(st) = strategy_type {
            sqlx::query(query)
                .bind(trading_pair)
                .bind(st)
                .fetch_optional(pool)
                .await
        } else {
            sqlx::query(query)
                .bind(trading_pair)
                .fetch_optional(pool)
                .await
        }
        .map_err(|e| SigmaXError::Database(format!("查询交易对最后订单时间失败: {}", e)))?;

        if let Some(row) = row {
            let last_time: Option<DateTime<Utc>> = row.try_get("last_time")
                .map_err(|e| SigmaXError::Database(format!("获取最后订单时间失败: {}", e)))?;
            Ok(last_time)
        } else {
            Ok(None)
        }
    }

    /// 获取全局最后订单时间
    async fn get_last_order_time_global(&self, strategy_type: Option<&str>) -> SigmaXResult<Option<DateTime<Utc>>> {
        let pool = self.db.pool();

        let query = if strategy_type.is_some() {
            r#"
            SELECT MAX(o.created_at) as last_time
            FROM orders o
            JOIN strategies s ON o.strategy_id = s.id
            WHERE s.strategy_type = $1
            "#
        } else {
            r#"
            SELECT MAX(created_at) as last_time
            FROM orders
            "#
        };

        let row = if let Some(st) = strategy_type {
            sqlx::query(query)
                .bind(st)
                .fetch_optional(pool)
                .await
        } else {
            sqlx::query(query)
                .fetch_optional(pool)
                .await
        }
        .map_err(|e| SigmaXError::Database(format!("查询全局最后订单时间失败: {}", e)))?;

        if let Some(row) = row {
            let last_time: Option<DateTime<Utc>> = row.try_get("last_time")
                .map_err(|e| SigmaXError::Database(format!("获取最后订单时间失败: {}", e)))?;
            Ok(last_time)
        } else {
            Ok(None)
        }
    }

    /// 获取历史价格数据
    async fn get_historical_prices(&self, trading_pair: &str, periods: usize) -> SigmaXResult<Vec<f64>> {
        let pool = self.db.pool();

        let query = r#"
            SELECT close_price
            FROM candles c
            JOIN trading_pairs tp ON c.trading_pair_id = tp.id
            WHERE tp.symbol = $1
            ORDER BY c.close_time DESC
            LIMIT $2
        "#;

        let rows = sqlx::query(query)
            .bind(trading_pair)
            .bind(periods as i64)
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询历史价格数据失败: {}", e)))?;

        let mut prices = Vec::new();
        for row in rows {
            let price: rust_decimal::Decimal = row.try_get("close_price")
                .map_err(|e| SigmaXError::Database(format!("获取价格数据失败: {}", e)))?;

            let price_f64 = price.to_f64().unwrap_or(0.0);
            prices.push(price_f64);
        }

        Ok(prices)
    }

    /// 获取投资组合回撤数据
    async fn get_portfolio_drawdown_data(&self, strategy_ids: &[Uuid]) -> SigmaXResult<(f64, f64, f64)> {
        let pool = self.db.pool();

        if strategy_ids.is_empty() {
            return Ok((0.0, 0.0, 0.0));
        }

        // 构建策略ID的占位符
        let placeholders: Vec<String> = (1..=strategy_ids.len()).map(|i| format!("${}", i)).collect();
        let placeholders_str = placeholders.join(",");

        let query = format!(
            r#"
            SELECT
                SUM(p.initial_capital) as total_initial,
                SUM(p.current_capital) as total_current
            FROM portfolios p
            WHERE p.id IN (
                SELECT DISTINCT portfolio_id
                FROM positions pos
                WHERE pos.id IN ({})
            )
            "#,
            placeholders_str
        );

        let mut query_builder = sqlx::query(&query);
        for strategy_id in strategy_ids {
            query_builder = query_builder.bind(strategy_id);
        }

        let row = query_builder
            .fetch_optional(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询投资组合数据失败: {}", e)))?;

        if let Some(row) = row {
            let initial: Option<rust_decimal::Decimal> = row.try_get("total_initial").ok();
            let current: Option<rust_decimal::Decimal> = row.try_get("total_current").ok();

            let initial_f64: f64 = initial.and_then(|d| d.to_f64()).unwrap_or(0.0);
            let current_f64: f64 = current.and_then(|d| d.to_f64()).unwrap_or(0.0);

            // 简化实现：假设峰值等于初始值（实际应该查询历史最高值）
            let peak_f64 = initial_f64.max(current_f64);

            Ok((initial_f64, current_f64, peak_f64))
        } else {
            Ok((0.0, 0.0, 0.0))
        }
    }

    /// 获取持仓价值数据
    async fn get_position_values(&self, strategy_ids: &[Uuid]) -> SigmaXResult<f64> {
        let pool = self.db.pool();

        if strategy_ids.is_empty() {
            return Ok(0.0);
        }

        // 构建策略ID的占位符
        let placeholders: Vec<String> = (1..=strategy_ids.len()).map(|i| format!("${}", i)).collect();
        let placeholders_str = placeholders.join(",");

        let query = format!(
            r#"
            SELECT SUM(pos.quantity * pos.average_price) as total_value
            FROM positions pos
            WHERE pos.id IN ({})
            "#,
            placeholders_str
        );

        let mut query_builder = sqlx::query(&query);
        for strategy_id in strategy_ids {
            query_builder = query_builder.bind(strategy_id);
        }

        let row = query_builder
            .fetch_optional(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询持仓价值失败: {}", e)))?;

        if let Some(row) = row {
            let total_value: Option<rust_decimal::Decimal> = row.try_get("total_value").ok();
            Ok(total_value.and_then(|d| d.to_f64()).unwrap_or(0.0))
        } else {
            Ok(0.0)
        }
    }

    /// 获取当前余额数据
    async fn get_current_balances(&self, exchange_id: &str) -> SigmaXResult<Vec<(String, f64, f64)>> {
        let pool = self.db.pool();

        let query = r#"
            SELECT asset, free, locked
            FROM balances
            WHERE exchange_id = $1
            ORDER BY asset
        "#;

        let rows = sqlx::query(query)
            .bind(exchange_id)
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询余额数据失败: {}", e)))?;

        let mut balances = Vec::new();
        for row in rows {
            let asset: String = row.try_get("asset")
                .map_err(|e| SigmaXError::Database(format!("获取资产名称失败: {}", e)))?;
            let free: rust_decimal::Decimal = row.try_get("free")
                .map_err(|e| SigmaXError::Database(format!("获取可用余额失败: {}", e)))?;
            let locked: rust_decimal::Decimal = row.try_get("locked")
                .map_err(|e| SigmaXError::Database(format!("获取锁定余额失败: {}", e)))?;

            let free_f64 = free.to_f64().unwrap_or(0.0);
            let locked_f64 = locked.to_f64().unwrap_or(0.0);

            balances.push((asset, free_f64, locked_f64));
        }

        Ok(balances)
    }

    // ===== 新增的回撤计算相关方法的具体实现 =====

    /// 获取投资组合快照数据用于回撤计算
    pub async fn get_portfolio_snapshots(&self, strategy_ids: &[Uuid], limit: usize) -> SigmaXResult<Vec<(DateTime<Utc>, f64)>> {
        let pool = self.db.pool();

        if strategy_ids.is_empty() {
            return Ok(Vec::new());
        }

        // 构建策略ID的占位符
        let placeholders: Vec<String> = (1..=strategy_ids.len()).map(|i| format!("${}", i)).collect();
        let placeholders_str = placeholders.join(",");

        let query = format!(
            r#"
            SELECT ps.timestamp, ps.total_value
            FROM portfolio_snapshots ps
            WHERE ps.strategy_id IN ({})
            ORDER BY ps.timestamp DESC
            LIMIT ${}
            "#,
            placeholders_str,
            strategy_ids.len() + 1
        );

        let mut query_builder = sqlx::query(&query);
        for strategy_id in strategy_ids {
            query_builder = query_builder.bind(strategy_id);
        }
        query_builder = query_builder.bind(limit as i64);

        let rows = query_builder
            .fetch_all(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询投资组合快照失败: {}", e)))?;

        let mut snapshots = Vec::new();
        for row in rows {
            let timestamp: DateTime<Utc> = row.try_get("timestamp")
                .map_err(|e| SigmaXError::Database(format!("获取时间戳失败: {}", e)))?;
            let total_value: rust_decimal::Decimal = row.try_get("total_value")
                .map_err(|e| SigmaXError::Database(format!("获取总价值失败: {}", e)))?;

            let value_f64 = total_value.to_f64().unwrap_or(0.0);
            snapshots.push((timestamp, value_f64));
        }

        Ok(snapshots)
    }

    /// 获取策略的投资组合ID
    pub async fn get_portfolio_id_by_strategy(&self, strategy_id: Uuid) -> SigmaXResult<Option<Uuid>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT DISTINCT p.id
            FROM portfolios p
            JOIN positions pos ON p.id = pos.portfolio_id
            WHERE pos.id = $1
            LIMIT 1
            "#
        )
        .bind(strategy_id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询策略投资组合ID失败: {}", e)))?;

        if let Some(row) = row {
            let portfolio_id: Uuid = row.try_get("id")
                .map_err(|e| SigmaXError::Database(format!("获取投资组合ID失败: {}", e)))?;
            Ok(Some(portfolio_id))
        } else {
            Ok(None)
        }
    }

    /// 创建投资组合快照
    pub async fn create_portfolio_snapshot(&self, portfolio_id: Uuid, strategy_id: Option<Uuid>, total_value: f64, cash_balance: f64, position_value: f64) -> SigmaXResult<()> {
        let pool = self.db.pool();

        // 获取当前峰值
        let peak_value = self.get_portfolio_peak_value(portfolio_id).await?;
        let new_peak = peak_value.max(total_value);

        // 计算回撤
        let drawdown = if new_peak > 0.0 {
            (new_peak - total_value) / new_peak
        } else {
            0.0
        };

        sqlx::query(
            r#"
            INSERT INTO portfolio_snapshots (
                portfolio_id, strategy_id, total_value, cash_balance, position_value,
                unrealized_pnl, realized_pnl, drawdown_from_peak, peak_value
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            "#
        )
        .bind(portfolio_id)
        .bind(strategy_id)
        .bind(rust_decimal::Decimal::from_f64_retain(total_value).unwrap_or_default())
        .bind(rust_decimal::Decimal::from_f64_retain(cash_balance).unwrap_or_default())
        .bind(rust_decimal::Decimal::from_f64_retain(position_value).unwrap_or_default())
        .bind(rust_decimal::Decimal::from_f64_retain(position_value - cash_balance).unwrap_or_default()) // 简化的未实现盈亏
        .bind(rust_decimal::Decimal::ZERO) // 已实现盈亏，需要从其他地方获取
        .bind(rust_decimal::Decimal::from_f64_retain(drawdown).unwrap_or_default())
        .bind(rust_decimal::Decimal::from_f64_retain(new_peak).unwrap_or_default())
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("创建投资组合快照失败: {}", e)))?;

        Ok(())
    }

    /// 获取投资组合的历史峰值
    pub async fn get_portfolio_peak_value(&self, portfolio_id: Uuid) -> SigmaXResult<f64> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT MAX(peak_value) as max_peak
            FROM portfolio_snapshots
            WHERE portfolio_id = $1
            "#
        )
        .bind(portfolio_id)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询投资组合峰值失败: {}", e)))?;

        if let Some(row) = row {
            let peak: Option<rust_decimal::Decimal> = row.try_get("max_peak").ok();
            Ok(peak.and_then(|p| p.to_f64()).unwrap_or(0.0))
        } else {
            // 如果没有快照记录，从投资组合表获取初始资金作为峰值
            let row = sqlx::query(
                "SELECT initial_capital FROM portfolios WHERE id = $1"
            )
            .bind(portfolio_id)
            .fetch_optional(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询投资组合初始资金失败: {}", e)))?;

            if let Some(row) = row {
                let initial: rust_decimal::Decimal = row.try_get("initial_capital")
                    .map_err(|e| SigmaXError::Database(format!("获取初始资金失败: {}", e)))?;
                Ok(initial.to_f64().unwrap_or(0.0))
            } else {
                Ok(0.0)
            }
        }
    }

    /// 更新投资组合的峰值和回撤
    pub async fn update_portfolio_drawdown(&self, portfolio_id: Uuid, current_value: f64, peak_value: f64, drawdown: f64) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            UPDATE portfolios
            SET current_capital = $1, max_drawdown = $2, updated_at = NOW()
            WHERE id = $3
            "#
        )
        .bind(rust_decimal::Decimal::from_f64_retain(current_value).unwrap_or_default())
        .bind(rust_decimal::Decimal::from_f64_retain(drawdown).unwrap_or_default())
        .bind(portfolio_id)
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("更新投资组合回撤失败: {}", e)))?;

        Ok(())
    }

    // ===== 市场流动性相关方法的具体实现 =====

    /// 获取市场流动性数据
    pub async fn get_market_liquidity_data(&self, trading_pair: &str, exchange_id: Option<i32>) -> SigmaXResult<Option<(f64, f64, f64)>> {
        let pool = self.db.pool();

        let query = if let Some(exchange_id) = exchange_id {
            r#"
            SELECT ml.liquidity_score, ml.spread_percentage, ml.volume_24h
            FROM market_liquidity ml
            JOIN trading_pairs tp ON ml.trading_pair_id = tp.id
            WHERE tp.symbol = $1 AND ml.exchange_id = $2
            ORDER BY ml.timestamp DESC
            LIMIT 1
            "#
        } else {
            r#"
            SELECT AVG(ml.liquidity_score) as liquidity_score,
                   AVG(ml.spread_percentage) as spread_percentage,
                   SUM(ml.volume_24h) as volume_24h
            FROM market_liquidity ml
            JOIN trading_pairs tp ON ml.trading_pair_id = tp.id
            WHERE tp.symbol = $1
            AND ml.timestamp >= NOW() - INTERVAL '1 hour'
            "#
        };

        let row = if let Some(exchange_id) = exchange_id {
            sqlx::query(query)
                .bind(trading_pair)
                .bind(exchange_id)
                .fetch_optional(pool)
                .await
        } else {
            sqlx::query(query)
                .bind(trading_pair)
                .fetch_optional(pool)
                .await
        }
        .map_err(|e| SigmaXError::Database(format!("查询市场流动性数据失败: {}", e)))?;

        if let Some(row) = row {
            let liquidity_score: Option<rust_decimal::Decimal> = row.try_get("liquidity_score").ok();
            let spread_percentage: Option<rust_decimal::Decimal> = row.try_get("spread_percentage").ok();
            let volume_24h: Option<rust_decimal::Decimal> = row.try_get("volume_24h").ok();

            let score = liquidity_score.and_then(|s| s.to_f64()).unwrap_or(0.0);
            let spread = spread_percentage.and_then(|s| s.to_f64()).unwrap_or(0.0);
            let volume = volume_24h.and_then(|v| v.to_f64()).unwrap_or(0.0);

            Ok(Some((score, spread, volume)))
        } else {
            Ok(None)
        }
    }

    /// 获取交易对的最新流动性评分
    pub async fn get_latest_liquidity_score(&self, trading_pair: &str) -> SigmaXResult<f64> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT AVG(ml.liquidity_score) as avg_score
            FROM market_liquidity ml
            JOIN trading_pairs tp ON ml.trading_pair_id = tp.id
            WHERE tp.symbol = $1
            AND ml.timestamp >= NOW() - INTERVAL '1 hour'
            "#
        )
        .bind(trading_pair)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询流动性评分失败: {}", e)))?;

        if let Some(row) = row {
            let avg_score: Option<rust_decimal::Decimal> = row.try_get("avg_score").ok();
            Ok(avg_score.and_then(|s| s.to_f64()).unwrap_or(0.0))
        } else {
            // 如果没有数据，返回基于交易对的默认流动性评分
            Ok(self.get_default_liquidity_score(trading_pair))
        }
    }

    /// 获取默认流动性评分
    fn get_default_liquidity_score(&self, trading_pair: &str) -> f64 {
        match trading_pair {
            pair if pair.contains("BTC") => 85.0, // BTC流动性很好
            pair if pair.contains("ETH") => 80.0, // ETH流动性好
            pair if pair.contains("BNB") => 75.0, // BNB流动性较好
            pair if pair.contains("USDT") || pair.contains("USDC") => 90.0, // 稳定币流动性最好
            pair if pair.contains("ADA") || pair.contains("DOT") => 65.0, // 主流币流动性中等
            _ => 50.0, // 其他币种流动性一般
        }
    }

    // ===== 交易时间相关方法的具体实现 =====

    /// 检查交易对在指定交易所是否开放交易
    pub async fn is_trading_pair_open(&self, trading_pair: &str, exchange_name: Option<&str>) -> SigmaXResult<bool> {
        let pool = self.db.pool();

        let query = if let Some(exchange_name) = exchange_name {
            r#"
            SELECT eth.is_24_7, eth.timezone, eth.monday_open, eth.monday_close,
                   eth.tuesday_open, eth.tuesday_close, eth.wednesday_open, eth.wednesday_close,
                   eth.thursday_open, eth.thursday_close, eth.friday_open, eth.friday_close,
                   eth.saturday_open, eth.saturday_close, eth.sunday_open, eth.sunday_close,
                   eth.maintenance_windows, eth.holidays
            FROM exchange_trading_hours eth
            JOIN exchanges e ON eth.exchange_id = e.id
            WHERE e.name = $1
            AND (eth.trading_pair = $2 OR eth.trading_pair IS NULL)
            AND eth.enabled = true
            ORDER BY eth.trading_pair NULLS LAST
            LIMIT 1
            "#
        } else {
            r#"
            SELECT eth.is_24_7, eth.timezone, eth.monday_open, eth.monday_close,
                   eth.tuesday_open, eth.tuesday_close, eth.wednesday_open, eth.wednesday_close,
                   eth.thursday_open, eth.thursday_close, eth.friday_open, eth.friday_close,
                   eth.saturday_open, eth.saturday_close, eth.sunday_open, eth.sunday_close,
                   eth.maintenance_windows, eth.holidays
            FROM exchange_trading_hours eth
            WHERE (eth.trading_pair = $1 OR eth.trading_pair IS NULL)
            AND eth.enabled = true
            ORDER BY eth.trading_pair NULLS LAST
            LIMIT 1
            "#
        };

        let row = if let Some(exchange_name) = exchange_name {
            sqlx::query(query)
                .bind(exchange_name)
                .bind(trading_pair)
                .fetch_optional(pool)
                .await
        } else {
            sqlx::query(query)
                .bind(trading_pair)
                .fetch_optional(pool)
                .await
        }
        .map_err(|e| SigmaXError::Database(format!("查询交易时间配置失败: {}", e)))?;

        if let Some(row) = row {
            let is_24_7: bool = row.try_get("is_24_7")
                .map_err(|e| SigmaXError::Database(format!("获取24/7标志失败: {}", e)))?;

            if is_24_7 {
                // 24/7交易，检查是否在维护窗口
                let maintenance_windows: serde_json::Value = row.try_get("maintenance_windows")
                    .unwrap_or(serde_json::Value::Array(vec![]));

                // TODO: 实现维护窗口检查逻辑
                return Ok(true);
            } else {
                // 非24/7交易，检查当前时间是否在交易时间内
                let timezone: String = row.try_get("timezone")
                    .map_err(|e| SigmaXError::Database(format!("获取时区失败: {}", e)))?;

                // TODO: 实现基于时区和交易时间的检查逻辑
                return Ok(self.is_within_trading_hours(&row, &timezone));
            }
        } else {
            // 没有找到配置，默认认为是24/7交易（加密货币市场）
            Ok(true)
        }
    }

    /// 获取交易所的交易时间配置
    pub async fn get_exchange_trading_hours(&self, exchange_name: &str, trading_pair: Option<&str>) -> SigmaXResult<Option<(bool, String)>> {
        let pool = self.db.pool();

        let query = r#"
            SELECT eth.is_24_7, eth.timezone
            FROM exchange_trading_hours eth
            JOIN exchanges e ON eth.exchange_id = e.id
            WHERE e.name = $1
            AND (eth.trading_pair = $2 OR eth.trading_pair IS NULL)
            AND eth.enabled = true
            ORDER BY eth.trading_pair NULLS LAST
            LIMIT 1
        "#;

        let row = sqlx::query(query)
            .bind(exchange_name)
            .bind(trading_pair)
            .fetch_optional(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询交易所交易时间失败: {}", e)))?;

        if let Some(row) = row {
            let is_24_7: bool = row.try_get("is_24_7")
                .map_err(|e| SigmaXError::Database(format!("获取24/7标志失败: {}", e)))?;
            let timezone: String = row.try_get("timezone")
                .map_err(|e| SigmaXError::Database(format!("获取时区失败: {}", e)))?;

            Ok(Some((is_24_7, timezone)))
        } else {
            Ok(None)
        }
    }

    /// 检查当前时间是否在交易时间内
    fn is_within_trading_hours(&self, row: &sqlx::postgres::PgRow, _timezone: &str) -> bool {
        use chrono::{Utc, Weekday, Timelike, Datelike};

        let now = Utc::now();
        let weekday = now.weekday();
        let current_time = now.time();

        // 根据星期几获取对应的开盘和收盘时间
        let (open_time, close_time) = match weekday {
            Weekday::Mon => (row.try_get::<Option<chrono::NaiveTime>, _>("monday_open").ok().flatten(),
                           row.try_get::<Option<chrono::NaiveTime>, _>("monday_close").ok().flatten()),
            Weekday::Tue => (row.try_get::<Option<chrono::NaiveTime>, _>("tuesday_open").ok().flatten(),
                           row.try_get::<Option<chrono::NaiveTime>, _>("tuesday_close").ok().flatten()),
            Weekday::Wed => (row.try_get::<Option<chrono::NaiveTime>, _>("wednesday_open").ok().flatten(),
                           row.try_get::<Option<chrono::NaiveTime>, _>("wednesday_close").ok().flatten()),
            Weekday::Thu => (row.try_get::<Option<chrono::NaiveTime>, _>("thursday_open").ok().flatten(),
                           row.try_get::<Option<chrono::NaiveTime>, _>("thursday_close").ok().flatten()),
            Weekday::Fri => (row.try_get::<Option<chrono::NaiveTime>, _>("friday_open").ok().flatten(),
                           row.try_get::<Option<chrono::NaiveTime>, _>("friday_close").ok().flatten()),
            Weekday::Sat => (row.try_get::<Option<chrono::NaiveTime>, _>("saturday_open").ok().flatten(),
                           row.try_get::<Option<chrono::NaiveTime>, _>("saturday_close").ok().flatten()),
            Weekday::Sun => (row.try_get::<Option<chrono::NaiveTime>, _>("sunday_open").ok().flatten(),
                           row.try_get::<Option<chrono::NaiveTime>, _>("sunday_close").ok().flatten()),
        };

        match (open_time, close_time) {
            (Some(open), Some(close)) => {
                current_time >= open && current_time <= close
            }
            _ => false, // 如果没有设置交易时间，认为不开放
        }
    }

    // ===== 风险预算相关方法的具体实现 =====

    /// 获取风险预算配置
    pub async fn get_risk_budget_config(&self, strategy_type: Option<&str>) -> SigmaXResult<Option<(f64, f64, f64)>> {
        let pool = self.db.pool();

        // 从风险配置表获取风险预算配置
        let query = if let Some(strategy_type) = strategy_type {
            r#"
            SELECT risk_parameters
            FROM risk_config
            WHERE strategy_type = $1 AND enabled = true
            ORDER BY created_at DESC
            LIMIT 1
            "#
        } else {
            r#"
            SELECT risk_parameters
            FROM risk_config
            WHERE strategy_type IS NULL AND enabled = true
            ORDER BY created_at DESC
            LIMIT 1
            "#
        };

        let row = if let Some(strategy_type) = strategy_type {
            sqlx::query(query)
                .bind(strategy_type)
                .fetch_optional(pool)
                .await
        } else {
            sqlx::query(query)
                .fetch_optional(pool)
                .await
        }
        .map_err(|e| SigmaXError::Database(format!("查询风险预算配置失败: {}", e)))?;

        if let Some(row) = row {
            let risk_parameters: serde_json::Value = row.try_get("risk_parameters")
                .map_err(|e| SigmaXError::Database(format!("获取风险参数失败: {}", e)))?;

            // 从JSON中提取风险预算信息
            if let Some(advanced) = risk_parameters.get("advanced") {
                if let Some(risk_budget) = advanced.get("risk_budget") {
                    let total_budget = risk_budget.get("total_risk_budget")
                        .and_then(|v| v.as_str())
                        .and_then(|s| s.parse::<f64>().ok())
                        .unwrap_or(0.0);

                    // 查询当前使用情况
                    let used_budget = if let Some(strategy_type) = strategy_type {
                        self.get_strategy_current_usage(strategy_type).await?
                    } else {
                        self.get_total_current_usage().await?
                    };

                    let usage_percentage = if total_budget > 0.0 {
                        (used_budget / total_budget) * 100.0
                    } else {
                        0.0
                    };

                    return Ok(Some((total_budget, used_budget, usage_percentage)));
                }
            }
        }

        Ok(None)
    }

    /// 获取策略的风险预算分配
    pub async fn get_strategy_risk_allocation(&self, strategy_type: &str) -> SigmaXResult<Option<f64>> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT allocated_amount
            FROM risk_budget_allocations
            WHERE allocation_type = 'strategy'
            AND allocation_key = $1
            AND is_active = true
            ORDER BY created_at DESC
            LIMIT 1
            "#
        )
        .bind(strategy_type)
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询策略风险预算分配失败: {}", e)))?;

        if let Some(row) = row {
            let allocated_amount: rust_decimal::Decimal = row.try_get("allocated_amount")
                .map_err(|e| SigmaXError::Database(format!("获取分配金额失败: {}", e)))?;
            Ok(Some(allocated_amount.to_f64().unwrap_or(0.0)))
        } else {
            Ok(None)
        }
    }

    /// 计算当前风险预算使用情况
    pub async fn calculate_current_risk_usage(&self, strategy_ids: &[Uuid]) -> SigmaXResult<f64> {
        if strategy_ids.is_empty() {
            return Ok(0.0);
        }

        // 计算当前持仓的风险敞口
        let position_risk = self.get_position_values(strategy_ids).await?;

        // 计算挂单的风险敞口
        let pending_orders_risk = self.get_pending_orders_risk(strategy_ids).await?;

        // 总风险使用 = 持仓风险 + 挂单风险
        Ok(position_risk + pending_orders_risk)
    }

    /// 创建风险预算使用记录
    pub async fn create_risk_budget_usage_record(&self, strategy_type: Option<&str>, strategy_id: Option<Uuid>, total_budget: f64, used_budget: f64, usage_percentage: f64) -> SigmaXResult<()> {
        let pool = self.db.pool();

        sqlx::query(
            r#"
            INSERT INTO risk_budget_usage (
                strategy_type, strategy_id, total_risk_budget, used_risk_budget,
                available_risk_budget, usage_percentage
            ) VALUES ($1, $2, $3, $4, $5, $6)
            "#
        )
        .bind(strategy_type)
        .bind(strategy_id)
        .bind(rust_decimal::Decimal::from_f64_retain(total_budget).unwrap_or_default())
        .bind(rust_decimal::Decimal::from_f64_retain(used_budget).unwrap_or_default())
        .bind(rust_decimal::Decimal::from_f64_retain(total_budget - used_budget).unwrap_or_default())
        .bind(rust_decimal::Decimal::from_f64_retain(usage_percentage).unwrap_or_default())
        .execute(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("创建风险预算使用记录失败: {}", e)))?;

        Ok(())
    }

    /// 获取策略当前使用情况
    async fn get_strategy_current_usage(&self, strategy_type: &str) -> SigmaXResult<f64> {
        let strategy_ids = self.get_strategy_ids_by_type(strategy_type).await?;
        self.calculate_current_risk_usage(&strategy_ids).await
    }

    /// 获取总的当前使用情况
    async fn get_total_current_usage(&self) -> SigmaXResult<f64> {
        let pool = self.db.pool();

        let row = sqlx::query(
            r#"
            SELECT SUM(pos.quantity * pos.average_price) as total_position_value
            FROM positions pos
            WHERE pos.quantity > 0
            "#
        )
        .fetch_optional(pool)
        .await
        .map_err(|e| SigmaXError::Database(format!("查询总持仓价值失败: {}", e)))?;

        if let Some(row) = row {
            let total_value: Option<rust_decimal::Decimal> = row.try_get("total_position_value").ok();
            Ok(total_value.and_then(|v| v.to_f64()).unwrap_or(0.0))
        } else {
            Ok(0.0)
        }
    }

    /// 获取挂单风险
    async fn get_pending_orders_risk(&self, strategy_ids: &[Uuid]) -> SigmaXResult<f64> {
        let pool = self.db.pool();

        if strategy_ids.is_empty() {
            return Ok(0.0);
        }

        // 构建策略ID的占位符
        let placeholders: Vec<String> = (1..=strategy_ids.len()).map(|i| format!("${}", i)).collect();
        let placeholders_str = placeholders.join(",");

        let query = format!(
            r#"
            SELECT SUM(o.quantity * COALESCE(o.price, 0)) as total_pending_value
            FROM orders o
            WHERE o.strategy_id IN ({})
            AND o.status IN ('Pending', 'PartiallyFilled')
            "#,
            placeholders_str
        );

        let mut query_builder = sqlx::query(&query);
        for strategy_id in strategy_ids {
            query_builder = query_builder.bind(strategy_id);
        }

        let row = query_builder
            .fetch_optional(pool)
            .await
            .map_err(|e| SigmaXError::Database(format!("查询挂单风险失败: {}", e)))?;

        if let Some(row) = row {
            let pending_value: Option<rust_decimal::Decimal> = row.try_get("total_pending_value").ok();
            Ok(pending_value.and_then(|v| v.to_f64()).unwrap_or(0.0))
        } else {
            Ok(0.0)
        }
    }
}