//! RiskServiceFacade 统一服务门面
//! 
//! 设计原则：
//! - 统一入口：所有风控检查的唯一入口点
//! - 模式路由：根据调用模式路由到专用适配器
//! - 缓存管理：统一的缓存策略和生命周期管理
//! - 指标收集：完整的性能和业务指标收集
//! - 向后兼容：保持现有API接口不变

use async_trait::async_trait;
use sigmax_core::{Order, Balance, SigmaXResult, SigmaXError, RiskCheckResult, EngineType, EngineRiskAdapter};
use crate::risk::{
    RiskEngine, CacheService, MetricsCollector, ConfigService,
    BasicRiskEngine, MemoryCacheService, MemoryMetricsCollector, MemoryConfigService
};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};

/// 风控检查模式
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum RiskCheckMode {
    /// 回测模式 - 高吞吐量批量处理
    Backtest,
    /// 实盘模式 - 低延迟实时处理
    Live,
    /// WebAPI模式 - 详细结果展示
    WebApi,
    /// 策略模式 - 策略专用检查
    Strategy,
}

impl From<EngineType> for RiskCheckMode {
    fn from(engine_type: EngineType) -> Self {
        match engine_type {
            EngineType::Backtest => RiskCheckMode::Backtest,
            EngineType::Live => RiskCheckMode::Live,
            EngineType::Paper => RiskCheckMode::Live, // Paper trading uses live mode
            EngineType::Simulation => RiskCheckMode::Backtest,
        }
    }
}

/// 风控检查上下文
#[derive(Debug, Clone)]
pub struct RiskCheckContext {
    /// 检查模式
    pub mode: RiskCheckMode,
    /// 策略类型 (可选)
    pub strategy_type: Option<String>,
    /// 引擎ID (可选)
    pub engine_id: Option<String>,
    /// 用户ID (可选)
    pub user_id: Option<String>,
    /// 会话ID (可选)
    pub session_id: Option<String>,
    /// 额外参数
    pub extra_params: HashMap<String, String>,
}

impl Default for RiskCheckContext {
    fn default() -> Self {
        Self {
            mode: RiskCheckMode::Live,
            strategy_type: None,
            engine_id: None,
            user_id: None,
            session_id: None,
            extra_params: HashMap::new(),
        }
    }
}

/// RiskServiceFacade 统一服务门面
pub struct RiskServiceFacade {
    /// 核心风控引擎
    core_engine: Arc<dyn RiskEngine>,
    /// 模式适配器映射
    adapters: Arc<RwLock<HashMap<RiskCheckMode, Arc<dyn EngineRiskAdapter>>>>,
    /// 缓存服务
    cache_service: Arc<dyn CacheService>,
    /// 指标收集器
    metrics_collector: Arc<dyn MetricsCollector>,
    /// 配置服务
    config_service: Arc<dyn ConfigService>,
    /// 门面配置
    config: FacadeConfig,
}

/// 门面配置
#[derive(Debug, Clone)]
pub struct FacadeConfig {
    /// 启用缓存
    pub enable_cache: bool,
    /// 缓存TTL
    pub cache_ttl_secs: u64,
    /// 启用指标收集
    pub enable_metrics: bool,
    /// 启用详细日志
    pub enable_detailed_logging: bool,
    /// 默认超时时间
    pub default_timeout_ms: u64,
    /// 启用熔断器
    pub enable_circuit_breaker: bool,
    /// 熔断器阈值
    pub circuit_breaker_threshold: f64,
}

impl Default for FacadeConfig {
    fn default() -> Self {
        Self {
            enable_cache: true,
            cache_ttl_secs: 300, // 5分钟
            enable_metrics: true,
            enable_detailed_logging: false,
            default_timeout_ms: 5000, // 5秒
            enable_circuit_breaker: true,
            circuit_breaker_threshold: 0.1, // 10%错误率
        }
    }
}

impl RiskServiceFacade {
    /// 创建新的风控服务门面
    pub async fn new(config: FacadeConfig) -> SigmaXResult<Self> {
        info!("Creating RiskServiceFacade with config: {:?}", config);
        
        // 创建核心服务
        let core_engine = Arc::new(BasicRiskEngine::new(Default::default()).await?);
        let cache_service = Arc::new(MemoryCacheService::new(Default::default()));
        let metrics_collector = Arc::new(MemoryMetricsCollector::new());
        let config_service = Arc::new(MemoryConfigService::new());
        
        let facade = Self {
            core_engine,
            adapters: Arc::new(RwLock::new(HashMap::new())),
            cache_service,
            metrics_collector,
            config_service,
            config,
        };
        
        // 初始化默认适配器
        facade.initialize_default_adapters().await?;
        
        info!("RiskServiceFacade created successfully");
        Ok(facade)
    }
    
    /// 使用自定义服务创建门面
    pub async fn new_with_services(
        config: FacadeConfig,
        core_engine: Arc<dyn RiskEngine>,
        cache_service: Arc<dyn CacheService>,
        metrics_collector: Arc<dyn MetricsCollector>,
        config_service: Arc<dyn ConfigService>,
    ) -> SigmaXResult<Self> {
        let facade = Self {
            core_engine,
            adapters: Arc::new(RwLock::new(HashMap::new())),
            cache_service,
            metrics_collector,
            config_service,
            config,
        };
        
        facade.initialize_default_adapters().await?;
        Ok(facade)
    }
    
    /// 注册适配器
    pub async fn register_adapter(
        &self,
        mode: RiskCheckMode,
        adapter: Arc<dyn EngineRiskAdapter>,
    ) -> SigmaXResult<()> {
        let mut adapters = self.adapters.write().await;
        adapters.insert(mode, adapter);
        info!("Registered adapter for mode: {:?}", mode);
        Ok(())
    }
    
    /// 统一风控检查入口 - 订单检查
    pub async fn check_order_risk(
        &self,
        order: &Order,
        context: &RiskCheckContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let start_time = Instant::now();
        
        if self.config.enable_detailed_logging {
            debug!("Starting order risk check: order_id={:?}, mode={:?}", order.id, context.mode);
        }
        
        // 检查缓存
        if self.config.enable_cache {
            if let Some(cached_result) = self.check_cache_for_order(order, context).await? {
                if self.config.enable_metrics {
                    self.metrics_collector.record_cache_hit().await;
                }
                return Ok(cached_result);
            }
        }
        
        // 路由到适配器
        let result = self.route_order_check(order, context).await?;
        
        // 缓存结果
        if self.config.enable_cache && result.passed {
            self.cache_order_result(order, context, &result).await?;
        }
        
        // 记录指标
        if self.config.enable_metrics {
            self.record_check_metrics("order", start_time.elapsed(), &result).await;
        }
        
        if self.config.enable_detailed_logging {
            debug!("Order risk check completed: passed={}, time={:?}", result.passed, start_time.elapsed());
        }
        
        Ok(result)
    }
    
    /// 统一风控检查入口 - 持仓检查
    pub async fn check_position_risk(
        &self,
        balances: &[Balance],
        context: &RiskCheckContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let start_time = Instant::now();
        
        if self.config.enable_detailed_logging {
            debug!("Starting position risk check: balances_count={}, mode={:?}", balances.len(), context.mode);
        }
        
        // 检查缓存
        if self.config.enable_cache {
            if let Some(cached_result) = self.check_cache_for_position(balances, context).await? {
                if self.config.enable_metrics {
                    self.metrics_collector.record_cache_hit().await;
                }
                return Ok(cached_result);
            }
        }
        
        // 路由到适配器
        let result = self.route_position_check(balances, context).await?;
        
        // 缓存结果
        if self.config.enable_cache && result.passed {
            self.cache_position_result(balances, context, &result).await?;
        }
        
        // 记录指标
        if self.config.enable_metrics {
            self.record_check_metrics("position", start_time.elapsed(), &result).await;
        }
        
        if self.config.enable_detailed_logging {
            debug!("Position risk check completed: passed={}, time={:?}", result.passed, start_time.elapsed());
        }
        
        Ok(result)
    }
    
    /// 批量订单检查 (主要用于回测模式)
    pub async fn batch_check_orders(
        &self,
        orders: &[Order],
        context: &RiskCheckContext,
    ) -> SigmaXResult<Vec<RiskCheckResult>> {
        let start_time = Instant::now();
        
        info!("Starting batch order risk check: count={}, mode={:?}", orders.len(), context.mode);
        
        // 获取适配器
        let adapter = self.get_adapter(context.mode).await?;
        
        // 批量检查
        let results = if let Some(backtest_adapter) = self.try_get_backtest_adapter(&adapter).await {
            // 使用回测适配器的批量检查
            let bool_results = backtest_adapter.batch_check_orders(orders).await?;
            bool_results.into_iter()
                .map(|passed| if passed { 
                    RiskCheckResult::pass() 
                } else { 
                    RiskCheckResult::fail("Batch check failed".to_string()) 
                })
                .collect()
        } else {
            // 逐个检查
            let mut results = Vec::new();
            for order in orders {
                let result = self.route_order_check(order, context).await?;
                results.push(result);
            }
            results
        };
        
        // 记录指标
        if self.config.enable_metrics {
            for result in &results {
                self.record_check_metrics("batch_order", start_time.elapsed() / orders.len() as u32, result).await;
            }
        }
        
        info!("Batch order risk check completed: total={}, passed={}, time={:?}", 
              results.len(), 
              results.iter().filter(|r| r.passed).count(),
              start_time.elapsed());
        
        Ok(results)
    }
    
    /// 路由订单检查到适配器
    async fn route_order_check(
        &self,
        order: &Order,
        context: &RiskCheckContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let adapter = self.get_adapter(context.mode).await?;
        
        // 调用适配器检查
        let passed = adapter.check_order_risk(order).await?;
        
        if passed {
            Ok(RiskCheckResult::pass())
        } else {
            Ok(RiskCheckResult::fail(format!("Risk check failed for mode: {:?}", context.mode)))
        }
    }
    
    /// 路由持仓检查到适配器
    async fn route_position_check(
        &self,
        balances: &[Balance],
        context: &RiskCheckContext,
    ) -> SigmaXResult<RiskCheckResult> {
        let adapter = self.get_adapter(context.mode).await?;
        
        // 调用适配器检查
        let passed = adapter.check_position_risk(balances).await?;
        
        if passed {
            Ok(RiskCheckResult::pass())
        } else {
            Ok(RiskCheckResult::fail(format!("Position risk check failed for mode: {:?}", context.mode)))
        }
    }
    
    /// 获取适配器
    async fn get_adapter(&self, mode: RiskCheckMode) -> SigmaXResult<Arc<dyn EngineRiskAdapter>> {
        let adapters = self.adapters.read().await;
        adapters.get(&mode)
            .cloned()
            .ok_or_else(|| SigmaXError::Configuration(
                format!("No adapter registered for mode: {:?}", mode)
            ))
    }
    
    /// 尝试获取回测适配器 (用于批量操作)
    async fn try_get_backtest_adapter(
        &self,
        adapter: &Arc<dyn EngineRiskAdapter>,
    ) -> Option<&crate::backtest::BacktestRiskAdapter> {
        // 这里需要使用downcast，但由于trait object的限制，我们使用类型检查
        // 在实际实现中，可以添加一个方法来标识适配器类型
        if adapter.engine_type() == EngineType::Backtest {
            // 在实际实现中，这里需要安全的downcast
            // adapter.as_any().downcast_ref::<crate::backtest::BacktestRiskAdapter>()
            None // 暂时返回None，需要完善downcast机制
        } else {
            None
        }
    }
    
    /// 初始化默认适配器
    async fn initialize_default_adapters(&self) -> SigmaXResult<()> {
        info!("Initializing default adapters");
        
        // 这里会在后续实现中添加默认适配器的创建
        // 目前先创建占位符适配器
        
        Ok(())
    }
    
    /// 检查订单缓存
    async fn check_cache_for_order(
        &self,
        order: &Order,
        context: &RiskCheckContext,
    ) -> SigmaXResult<Option<RiskCheckResult>> {
        let cache_key = format!("order_risk:{}:{:?}:{}", 
                               order.id, 
                               context.mode,
                               context.strategy_type.as_deref().unwrap_or("default"));
        
        if let Some(json_value) = self.cache_service.get_json(&cache_key).await? {
            serde_json::from_value(json_value).ok()
        } else {
            None
        }
    }
    
    /// 检查持仓缓存
    async fn check_cache_for_position(
        &self,
        balances: &[Balance],
        context: &RiskCheckContext,
    ) -> SigmaXResult<Option<RiskCheckResult>> {
        // 生成持仓的哈希作为缓存键的一部分
        let balances_hash = self.calculate_balances_hash(balances);
        let cache_key = format!("position_risk:{}:{:?}:{}", 
                               balances_hash,
                               context.mode,
                               context.strategy_type.as_deref().unwrap_or("default"));
        
        if let Some(json_value) = self.cache_service.get_json(&cache_key).await? {
            serde_json::from_value(json_value).ok()
        } else {
            None
        }
    }
    
    /// 缓存订单结果
    async fn cache_order_result(
        &self,
        order: &Order,
        context: &RiskCheckContext,
        result: &RiskCheckResult,
    ) -> SigmaXResult<()> {
        let cache_key = format!("order_risk:{}:{:?}:{}", 
                               order.id, 
                               context.mode,
                               context.strategy_type.as_deref().unwrap_or("default"));
        
        let ttl = Duration::from_secs(self.config.cache_ttl_secs);
        let json_value = serde_json::to_value(result)
            .map_err(|e| SigmaXError::Internal(format!("Cache serialization failed: {}", e)))?;
        self.cache_service.set_json(&cache_key, json_value, ttl).await
    }
    
    /// 缓存持仓结果
    async fn cache_position_result(
        &self,
        balances: &[Balance],
        context: &RiskCheckContext,
        result: &RiskCheckResult,
    ) -> SigmaXResult<()> {
        let balances_hash = self.calculate_balances_hash(balances);
        let cache_key = format!("position_risk:{}:{:?}:{}", 
                               balances_hash,
                               context.mode,
                               context.strategy_type.as_deref().unwrap_or("default"));
        
        let ttl = Duration::from_secs(self.config.cache_ttl_secs);
        let json_value = serde_json::to_value(result)
            .map_err(|e| SigmaXError::Internal(format!("Cache serialization failed: {}", e)))?;
        self.cache_service.set_json(&cache_key, json_value, ttl).await
    }
    
    /// 计算持仓哈希
    fn calculate_balances_hash(&self, balances: &[Balance]) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        for balance in balances {
            balance.asset.hash(&mut hasher);
            balance.free.to_string().hash(&mut hasher);
            balance.locked.to_string().hash(&mut hasher);
        }
        hasher.finish()
    }
    
    /// 记录检查指标
    async fn record_check_metrics(
        &self,
        check_type: &str,
        duration: Duration,
        result: &RiskCheckResult,
    ) {
        self.metrics_collector.record_risk_check(result.passed).await;
        self.metrics_collector.record_latency(check_type, duration).await;
    }
    
    /// 获取门面统计信息
    pub async fn get_facade_stats(&self) -> SigmaXResult<FacadeStats> {
        let metrics = self.metrics_collector.get_metrics().await?;
        
        Ok(FacadeStats {
            total_checks: metrics.get("risk_checks_total").copied().unwrap_or(0.0) as u64,
            passed_checks: metrics.get("risk_checks_passed").copied().unwrap_or(0.0) as u64,
            failed_checks: metrics.get("risk_checks_failed").copied().unwrap_or(0.0) as u64,
            cache_hits: metrics.get("cache_hits").copied().unwrap_or(0.0) as u64,
            cache_misses: metrics.get("cache_misses").copied().unwrap_or(0.0) as u64,
            avg_latency_ms: metrics.get("order_latency_avg_ms").copied().unwrap_or(0.0),
            registered_adapters: {
                let adapters = self.adapters.read().await;
                adapters.len()
            },
        })
    }
}

/// 门面统计信息
#[derive(Debug, Clone)]
pub struct FacadeStats {
    pub total_checks: u64,
    pub passed_checks: u64,
    pub failed_checks: u64,
    pub cache_hits: u64,
    pub cache_misses: u64,
    pub avg_latency_ms: f64,
    pub registered_adapters: usize,
}

impl FacadeStats {
    pub fn pass_rate(&self) -> f64 {
        if self.total_checks > 0 {
            self.passed_checks as f64 / self.total_checks as f64
        } else {
            0.0
        }
    }
    
    pub fn cache_hit_rate(&self) -> f64 {
        let total_cache_ops = self.cache_hits + self.cache_misses;
        if total_cache_ops > 0 {
            self.cache_hits as f64 / total_cache_ops as f64
        } else {
            0.0
        }
    }
}
