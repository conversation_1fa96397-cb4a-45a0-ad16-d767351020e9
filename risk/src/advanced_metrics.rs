//! 高级风险指标计算模块
//! 
//! 实现专业级风险度量指标，包括CVaR、尾部风险等

use sigmax_core::{Amount, SigmaXResult, SigmaXError};
use rust_decimal::Decimal;
use std::collections::VecDeque;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

/// 高级风险指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdvancedRiskMetrics {
    /// CVaR 95% (条件风险价值)
    pub cvar_95: Amount,
    /// CVaR 99% (条件风险价值)
    pub cvar_99: Amount,
    /// CVaR 99.9% (极端条件风险价值)
    pub cvar_999: Amount,
    /// 尾部风险指标
    pub tail_risk: f64,
    /// 期望短缺 (Expected Shortfall)
    pub expected_shortfall: Amount,
    /// 最大损失期望
    pub maximum_loss_expectation: Amount,
    /// 风险贡献度
    pub risk_contribution: f64,
    /// 计算时间戳
    pub calculated_at: DateTime<Utc>,
}

/// 高级风险计算器
pub struct AdvancedRiskCalculator {
    /// 历史收益率数据
    returns_history: VecDeque<Decimal>,
    /// 最大历史长度
    max_history_length: usize,
    /// 当前投资组合价值
    current_portfolio_value: Amount,
}

impl AdvancedRiskCalculator {
    /// 创建新的高级风险计算器
    pub fn new(max_history_length: usize) -> Self {
        Self {
            returns_history: VecDeque::new(),
            max_history_length,
            current_portfolio_value: Amount::ZERO,
        }
    }

    /// 更新投资组合价值
    pub fn update_portfolio_value(&mut self, value: Amount) {
        self.current_portfolio_value = value;
    }

    /// 添加收益率数据
    pub fn add_return(&mut self, return_rate: Decimal) {
        self.returns_history.push_back(return_rate);
        
        // 保持历史数据在指定长度内
        if self.returns_history.len() > self.max_history_length {
            self.returns_history.pop_front();
        }
    }

    /// 计算高级风险指标
    pub fn calculate_advanced_metrics(&self) -> SigmaXResult<AdvancedRiskMetrics> {
        if self.returns_history.len() < 30 {
            return Err(SigmaXError::InvalidOperation(
                "需要至少30个历史数据点来计算高级风险指标".to_string()
            ));
        }

        let cvar_95 = self.calculate_cvar(0.05)?;
        let cvar_99 = self.calculate_cvar(0.01)?;
        let cvar_999 = self.calculate_cvar(0.001)?;
        let tail_risk = self.calculate_tail_risk()?;
        let expected_shortfall = self.calculate_expected_shortfall(0.05)?;
        let maximum_loss_expectation = self.calculate_maximum_loss_expectation()?;
        let risk_contribution = self.calculate_risk_contribution()?;

        Ok(AdvancedRiskMetrics {
            cvar_95,
            cvar_99,
            cvar_999,
            tail_risk,
            expected_shortfall,
            maximum_loss_expectation,
            risk_contribution,
            calculated_at: Utc::now(),
        })
    }

    /// 计算CVaR (条件风险价值)
    /// 
    /// CVaR是在给定置信度下，超过VaR的条件期望损失
    /// 
    /// # 参数
    /// * `alpha` - 显著性水平 (例如: 0.05 表示95%置信度)
    pub fn calculate_cvar(&self, alpha: f64) -> SigmaXResult<Amount> {
        if self.returns_history.is_empty() {
            return Ok(Amount::ZERO);
        }

        // 将收益率转换为损失率（取负值）
        let mut losses: Vec<Decimal> = self.returns_history
            .iter()
            .map(|r| -r) // 损失 = -收益
            .collect();
        
        // 按损失大小排序（从小到大）
        losses.sort();
        
        // 计算VaR位置
        let var_index = ((1.0 - alpha) * losses.len() as f64) as usize;
        
        if var_index >= losses.len() {
            return Ok(Amount::ZERO);
        }
        
        // CVaR = 超过VaR的损失的平均值
        let tail_losses: Vec<Decimal> = losses[var_index..].to_vec();
        
        if tail_losses.is_empty() {
            return Ok(Amount::ZERO);
        }
        
        let cvar_rate = tail_losses.iter().sum::<Decimal>() / Decimal::from(tail_losses.len());
        
        // 转换为绝对金额
        let cvar_amount = self.current_portfolio_value * cvar_rate;
        
        Ok(cvar_amount)
    }

    /// 计算期望短缺 (Expected Shortfall)
    /// 
    /// ES是CVaR的另一种表述，表示在最坏情况下的期望损失
    pub fn calculate_expected_shortfall(&self, alpha: f64) -> SigmaXResult<Amount> {
        // Expected Shortfall 等同于 CVaR
        self.calculate_cvar(alpha)
    }

    /// 计算尾部风险指标
    /// 
    /// 衡量极端损失事件的概率和严重程度
    pub fn calculate_tail_risk(&self) -> SigmaXResult<f64> {
        if self.returns_history.len() < 10 {
            return Ok(0.0);
        }

        let mut losses: Vec<f64> = self.returns_history
            .iter()
            .map(|r| (-r).to_string().parse().unwrap_or(0.0))
            .collect();
        
        losses.sort_by(|a, b| b.partial_cmp(a).unwrap());
        
        // 取最大的10%损失
        let tail_size = (losses.len() as f64 * 0.1).max(1.0) as usize;
        let tail_losses = &losses[0..tail_size.min(losses.len())];
        
        if tail_losses.is_empty() {
            return Ok(0.0);
        }
        
        // 计算尾部损失的平均值
        let tail_mean = tail_losses.iter().sum::<f64>() / tail_losses.len() as f64;
        
        // 计算整体损失的平均值
        let overall_mean = losses.iter().sum::<f64>() / losses.len() as f64;
        
        // 尾部风险 = 尾部平均损失 / 整体平均损失
        let tail_risk = if overall_mean > 0.0 {
            tail_mean / overall_mean
        } else {
            0.0
        };
        
        Ok(tail_risk)
    }

    /// 计算最大损失期望
    /// 
    /// 基于历史数据估计可能的最大损失
    pub fn calculate_maximum_loss_expectation(&self) -> SigmaXResult<Amount> {
        if self.returns_history.is_empty() {
            return Ok(Amount::ZERO);
        }

        // 找到历史最大损失
        let max_loss_rate = self.returns_history
            .iter()
            .map(|r| -r) // 转换为损失率
            .max()
            .unwrap_or(Decimal::ZERO);
        
        // 应用安全边际（增加20%）
        let safety_margin = Decimal::from_f64_retain(1.2).unwrap_or(Decimal::from(12) / Decimal::from(10));
        let expected_max_loss_rate = max_loss_rate * safety_margin;
        
        let max_loss_amount = self.current_portfolio_value * expected_max_loss_rate;
        
        Ok(max_loss_amount)
    }

    /// 计算风险贡献度
    /// 
    /// 衡量当前策略对整体风险的贡献
    pub fn calculate_risk_contribution(&self) -> SigmaXResult<f64> {
        if self.returns_history.len() < 10 {
            return Ok(0.0);
        }

        // 计算收益率的标准差
        let mean_return = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        
        let variance = self.returns_history
            .iter()
            .map(|r| {
                let diff = *r - mean_return;
                diff * diff
            })
            .sum::<Decimal>() / Decimal::from(self.returns_history.len());
        
        // 转换为f64进行计算
        let variance_f64: f64 = variance.to_string().parse().unwrap_or(0.0);
        let volatility = variance_f64.sqrt();
        
        // 风险贡献度 = 波动率 * 投资组合权重
        // 这里简化为波动率本身，实际应用中需要考虑投资组合权重
        Ok(volatility)
    }

    /// 获取历史收益率统计信息
    pub fn get_returns_statistics(&self) -> ReturnsStatistics {
        if self.returns_history.is_empty() {
            return ReturnsStatistics::default();
        }

        let count = self.returns_history.len();
        let sum = self.returns_history.iter().sum::<Decimal>();
        let mean = sum / Decimal::from(count);
        
        let variance = self.returns_history
            .iter()
            .map(|r| {
                let diff = *r - mean;
                diff * diff
            })
            .sum::<Decimal>() / Decimal::from(count);
        
        let min_return = self.returns_history.iter().min().copied().unwrap_or(Decimal::ZERO);
        let max_return = self.returns_history.iter().max().copied().unwrap_or(Decimal::ZERO);
        
        ReturnsStatistics {
            count,
            mean,
            variance,
            min_return,
            max_return,
        }
    }
}

/// 收益率统计信息
#[derive(Debug, Clone, Default)]
pub struct ReturnsStatistics {
    pub count: usize,
    pub mean: Decimal,
    pub variance: Decimal,
    pub min_return: Decimal,
    pub max_return: Decimal,
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;

    #[test]
    fn test_cvar_calculation() {
        let mut calculator = AdvancedRiskCalculator::new(100);
        calculator.update_portfolio_value(dec!(100000));
        
        // 添加一些测试数据
        let test_returns = vec![
            dec!(0.02), dec!(-0.01), dec!(0.015), dec!(-0.03), dec!(0.01),
            dec!(-0.02), dec!(0.025), dec!(-0.015), dec!(0.005), dec!(-0.04),
            dec!(0.03), dec!(-0.025), dec!(0.02), dec!(-0.01), dec!(0.015),
            dec!(-0.035), dec!(0.01), dec!(-0.02), dec!(0.025), dec!(-0.015),
            dec!(0.005), dec!(-0.05), dec!(0.03), dec!(-0.025), dec!(0.02),
            dec!(-0.01), dec!(0.015), dec!(-0.03), dec!(0.01), dec!(-0.02),
        ];
        
        for return_rate in test_returns {
            calculator.add_return(return_rate);
        }
        
        let cvar_95 = calculator.calculate_cvar(0.05).unwrap();
        assert!(cvar_95 > Amount::ZERO);
        
        let cvar_99 = calculator.calculate_cvar(0.01).unwrap();
        assert!(cvar_99 >= cvar_95); // CVaR 99% 应该 >= CVaR 95%
    }

    #[test]
    fn test_advanced_metrics() {
        let mut calculator = AdvancedRiskCalculator::new(100);
        calculator.update_portfolio_value(dec!(100000));
        
        // 添加足够的测试数据
        for i in 0..50 {
            let return_rate = if i % 5 == 0 {
                dec!(-0.03) // 偶尔的大损失
            } else {
                dec!(0.01) // 正常收益
            };
            calculator.add_return(return_rate);
        }
        
        let metrics = calculator.calculate_advanced_metrics().unwrap();
        
        assert!(metrics.cvar_95 >= Amount::ZERO);
        assert!(metrics.cvar_99 >= metrics.cvar_95);
        assert!(metrics.cvar_999 >= metrics.cvar_99);
        assert!(metrics.tail_risk >= 0.0);
    }
}
