//! 指标收集器实现

use async_trait::async_trait;
use sigmax_core::SigmaXResult;
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info};

/// 指标收集器trait
#[async_trait]
pub trait MetricsCollector: Send + Sync {
    /// 记录风控检查
    async fn record_risk_check(&self, passed: bool);
    
    /// 记录缓存命中
    async fn record_cache_hit(&self);
    
    /// 记录延迟
    async fn record_latency(&self, operation: &str, duration: Duration);
    
    /// 获取指标
    async fn get_metrics(&self) -> SigmaXResult<HashMap<String, f64>>;
}

/// 内存指标收集器
pub struct MemoryMetricsCollector {
    /// 风控检查计数器
    risk_checks_total: AtomicU64,
    risk_checks_passed: AtomicU64,
    risk_checks_failed: AtomicU64,
    
    /// 缓存指标
    cache_hits: AtomicU64,
    cache_misses: AtomicU64,
    
    /// 延迟指标
    latency_metrics: Arc<RwLock<HashMap<String, LatencyMetric>>>,
    
    /// 自定义指标
    custom_metrics: Arc<RwLock<HashMap<String, f64>>>,
    
    /// 启动时间
    start_time: Instant,
}

/// 延迟指标
#[derive(Debug, Clone)]
struct LatencyMetric {
    total_duration: Duration,
    count: u64,
    min_duration: Duration,
    max_duration: Duration,
    recent_durations: Vec<Duration>, // 用于计算P99等
}

impl LatencyMetric {
    fn new() -> Self {
        Self {
            total_duration: Duration::ZERO,
            count: 0,
            min_duration: Duration::MAX,
            max_duration: Duration::ZERO,
            recent_durations: Vec::new(),
        }
    }
    
    fn record(&mut self, duration: Duration) {
        self.total_duration += duration;
        self.count += 1;
        
        if duration < self.min_duration {
            self.min_duration = duration;
        }
        if duration > self.max_duration {
            self.max_duration = duration;
        }
        
        // 保持最近1000个样本用于百分位计算
        self.recent_durations.push(duration);
        if self.recent_durations.len() > 1000 {
            self.recent_durations.remove(0);
        }
    }
    
    fn avg_duration(&self) -> Duration {
        if self.count > 0 {
            self.total_duration / self.count as u32
        } else {
            Duration::ZERO
        }
    }
    
    fn p99_duration(&self) -> Duration {
        if self.recent_durations.is_empty() {
            return Duration::ZERO;
        }
        
        let mut sorted = self.recent_durations.clone();
        sorted.sort();
        
        let index = (sorted.len() as f64 * 0.99) as usize;
        sorted.get(index).copied().unwrap_or(Duration::ZERO)
    }
}

impl MemoryMetricsCollector {
    /// 创建新的指标收集器
    pub fn new() -> Self {
        Self {
            risk_checks_total: AtomicU64::new(0),
            risk_checks_passed: AtomicU64::new(0),
            risk_checks_failed: AtomicU64::new(0),
            cache_hits: AtomicU64::new(0),
            cache_misses: AtomicU64::new(0),
            latency_metrics: Arc::new(RwLock::new(HashMap::new())),
            custom_metrics: Arc::new(RwLock::new(HashMap::new())),
            start_time: Instant::now(),
        }
    }
    
    /// 记录缓存未命中
    pub async fn record_cache_miss(&self) {
        self.cache_misses.fetch_add(1, Ordering::SeqCst);
    }
    
    /// 设置自定义指标
    pub async fn set_custom_metric(&self, name: String, value: f64) {
        let mut metrics = self.custom_metrics.write().await;
        metrics.insert(name, value);
    }
    
    /// 增加自定义指标
    pub async fn increment_custom_metric(&self, name: String, delta: f64) {
        let mut metrics = self.custom_metrics.write().await;
        let current = metrics.get(&name).copied().unwrap_or(0.0);
        metrics.insert(name, current + delta);
    }
    
    /// 获取风控通过率
    pub fn get_risk_pass_rate(&self) -> f64 {
        let total = self.risk_checks_total.load(Ordering::SeqCst);
        if total > 0 {
            let passed = self.risk_checks_passed.load(Ordering::SeqCst);
            passed as f64 / total as f64
        } else {
            0.0
        }
    }
    
    /// 获取缓存命中率
    pub fn get_cache_hit_rate(&self) -> f64 {
        let hits = self.cache_hits.load(Ordering::SeqCst);
        let misses = self.cache_misses.load(Ordering::SeqCst);
        let total = hits + misses;
        
        if total > 0 {
            hits as f64 / total as f64
        } else {
            0.0
        }
    }
    
    /// 获取运行时间
    pub fn get_uptime_seconds(&self) -> f64 {
        self.start_time.elapsed().as_secs_f64()
    }
    
    /// 重置所有指标
    pub async fn reset(&self) {
        self.risk_checks_total.store(0, Ordering::SeqCst);
        self.risk_checks_passed.store(0, Ordering::SeqCst);
        self.risk_checks_failed.store(0, Ordering::SeqCst);
        self.cache_hits.store(0, Ordering::SeqCst);
        self.cache_misses.store(0, Ordering::SeqCst);
        
        let mut latency_metrics = self.latency_metrics.write().await;
        latency_metrics.clear();
        
        let mut custom_metrics = self.custom_metrics.write().await;
        custom_metrics.clear();
        
        info!("All metrics have been reset");
    }
}

#[async_trait]
impl MetricsCollector for MemoryMetricsCollector {
    async fn record_risk_check(&self, passed: bool) {
        self.risk_checks_total.fetch_add(1, Ordering::SeqCst);
        
        if passed {
            self.risk_checks_passed.fetch_add(1, Ordering::SeqCst);
        } else {
            self.risk_checks_failed.fetch_add(1, Ordering::SeqCst);
        }
        
        debug!("Recorded risk check: passed={}", passed);
    }
    
    async fn record_cache_hit(&self) {
        self.cache_hits.fetch_add(1, Ordering::SeqCst);
        debug!("Recorded cache hit");
    }
    
    async fn record_latency(&self, operation: &str, duration: Duration) {
        let mut latency_metrics = self.latency_metrics.write().await;
        
        let metric = latency_metrics
            .entry(operation.to_string())
            .or_insert_with(LatencyMetric::new);
        
        metric.record(duration);
        
        debug!("Recorded latency for {}: {:?}", operation, duration);
    }
    
    async fn get_metrics(&self) -> SigmaXResult<HashMap<String, f64>> {
        let mut metrics = HashMap::new();
        
        // 风控指标
        metrics.insert("risk_checks_total".to_string(), self.risk_checks_total.load(Ordering::SeqCst) as f64);
        metrics.insert("risk_checks_passed".to_string(), self.risk_checks_passed.load(Ordering::SeqCst) as f64);
        metrics.insert("risk_checks_failed".to_string(), self.risk_checks_failed.load(Ordering::SeqCst) as f64);
        metrics.insert("risk_pass_rate".to_string(), self.get_risk_pass_rate());
        
        // 缓存指标
        metrics.insert("cache_hits".to_string(), self.cache_hits.load(Ordering::SeqCst) as f64);
        metrics.insert("cache_misses".to_string(), self.cache_misses.load(Ordering::SeqCst) as f64);
        metrics.insert("cache_hit_rate".to_string(), self.get_cache_hit_rate());
        
        // 系统指标
        metrics.insert("uptime_seconds".to_string(), self.get_uptime_seconds());
        
        // 延迟指标
        {
            let latency_metrics = self.latency_metrics.read().await;
            for (operation, metric) in latency_metrics.iter() {
                let prefix = format!("{}_latency", operation);
                metrics.insert(format!("{}_avg_ms", prefix), metric.avg_duration().as_millis() as f64);
                metrics.insert(format!("{}_min_ms", prefix), metric.min_duration.as_millis() as f64);
                metrics.insert(format!("{}_max_ms", prefix), metric.max_duration.as_millis() as f64);
                metrics.insert(format!("{}_p99_ms", prefix), metric.p99_duration().as_millis() as f64);
                metrics.insert(format!("{}_count", prefix), metric.count as f64);
            }
        }
        
        // 自定义指标
        {
            let custom_metrics = self.custom_metrics.read().await;
            for (name, value) in custom_metrics.iter() {
                metrics.insert(name.clone(), *value);
            }
        }
        
        Ok(metrics)
    }
}

/// Prometheus指标收集器 (占位符实现)
pub struct PrometheusMetricsCollector {
    // Prometheus客户端等
    _config: MetricsConfig,
}

/// 指标配置
#[derive(Debug, Clone)]
pub struct MetricsConfig {
    pub enable_prometheus: bool,
    pub prometheus_port: u16,
    pub metrics_prefix: String,
    pub collection_interval: Duration,
}

impl Default for MetricsConfig {
    fn default() -> Self {
        Self {
            enable_prometheus: false,
            prometheus_port: 9090,
            metrics_prefix: "sigmax_".to_string(),
            collection_interval: Duration::from_secs(10),
        }
    }
}

impl PrometheusMetricsCollector {
    pub fn new(config: MetricsConfig) -> Self {
        Self {
            _config: config,
        }
    }
}

#[async_trait]
impl MetricsCollector for PrometheusMetricsCollector {
    async fn record_risk_check(&self, _passed: bool) {
        // Prometheus实现占位符
    }
    
    async fn record_cache_hit(&self) {
        // Prometheus实现占位符
    }
    
    async fn record_latency(&self, _operation: &str, _duration: Duration) {
        // Prometheus实现占位符
    }
    
    async fn get_metrics(&self) -> SigmaXResult<HashMap<String, f64>> {
        // Prometheus实现占位符
        Ok(HashMap::new())
    }
}

/// 复合指标收集器
pub struct CompositeMetricsCollector {
    collectors: Vec<Arc<dyn MetricsCollector>>,
}

impl CompositeMetricsCollector {
    pub fn new(collectors: Vec<Arc<dyn MetricsCollector>>) -> Self {
        Self { collectors }
    }
}

#[async_trait]
impl MetricsCollector for CompositeMetricsCollector {
    async fn record_risk_check(&self, passed: bool) {
        for collector in &self.collectors {
            collector.record_risk_check(passed).await;
        }
    }
    
    async fn record_cache_hit(&self) {
        for collector in &self.collectors {
            collector.record_cache_hit().await;
        }
    }
    
    async fn record_latency(&self, operation: &str, duration: Duration) {
        for collector in &self.collectors {
            collector.record_latency(operation, duration).await;
        }
    }
    
    async fn get_metrics(&self) -> SigmaXResult<HashMap<String, f64>> {
        // 合并所有收集器的指标
        let mut combined_metrics = HashMap::new();
        
        for (i, collector) in self.collectors.iter().enumerate() {
            let metrics = collector.get_metrics().await?;
            for (key, value) in metrics {
                let prefixed_key = format!("collector_{}_{}", i, key);
                combined_metrics.insert(prefixed_key, value);
            }
        }
        
        Ok(combined_metrics)
    }
}

// 注意：PrometheusMetricsCollector已在上面定义，重复定义已移除

// PrometheusMetricsCollector的实现已在上面定义

/// 统一风控指标收集器
pub struct UnifiedRiskMetricsCollector {
    /// 内存指标收集器
    memory_collector: Arc<MemoryMetricsCollector>,
    /// Prometheus指标收集器 (可选)
    prometheus_collector: Option<Arc<PrometheusMetricsCollector>>,
    /// 告警管理器
    alert_manager: Arc<RwLock<AlertManager>>,
    /// 配置
    config: UnifiedMetricsConfig,
}

/// 统一指标配置
#[derive(Debug, Clone)]
pub struct UnifiedMetricsConfig {
    /// 启用Prometheus
    pub enable_prometheus: bool,
    /// 启用告警
    pub enable_alerting: bool,
    /// 指标聚合间隔 (秒)
    pub aggregation_interval_secs: u64,
    /// 告警检查间隔 (秒)
    pub alert_check_interval_secs: u64,
    /// 历史数据保留天数
    pub history_retention_days: u32,
}

impl Default for UnifiedMetricsConfig {
    fn default() -> Self {
        Self {
            enable_prometheus: false,
            enable_alerting: true,
            aggregation_interval_secs: 60,
            alert_check_interval_secs: 30,
            history_retention_days: 7,
        }
    }
}

/// 告警管理器
pub struct AlertManager {
    /// 告警规则
    rules: Vec<AlertRule>,
    /// 活跃告警
    active_alerts: HashMap<String, Alert>,
    /// 告警历史
    alert_history: Vec<Alert>,
}

/// 告警规则
#[derive(Debug, Clone)]
pub struct AlertRule {
    /// 规则名称
    pub name: String,
    /// 指标名称
    pub metric_name: String,
    /// 阈值
    pub threshold: f64,
    /// 比较操作
    pub operator: AlertOperator,
    /// 持续时间 (秒)
    pub duration_secs: u64,
    /// 严重级别
    pub severity: AlertSeverity,
    /// 是否启用
    pub enabled: bool,
}

/// 告警操作符
#[derive(Debug, Clone)]
pub enum AlertOperator {
    GreaterThan,
    LessThan,
    Equal,
    NotEqual,
}

/// 告警严重级别
#[derive(Debug, Clone)]
pub enum AlertSeverity {
    Info,
    Warning,
    Critical,
}

/// 告警
#[derive(Debug, Clone)]
pub struct Alert {
    /// 告警ID
    pub id: String,
    /// 规则名称
    pub rule_name: String,
    /// 告警消息
    pub message: String,
    /// 严重级别
    pub severity: AlertSeverity,
    /// 触发时间
    pub triggered_at: chrono::DateTime<chrono::Utc>,
    /// 是否已解决
    pub resolved: bool,
    /// 解决时间
    pub resolved_at: Option<chrono::DateTime<chrono::Utc>>,
}

impl UnifiedRiskMetricsCollector {
    /// 创建新的统一指标收集器
    pub async fn new(config: UnifiedMetricsConfig) -> SigmaXResult<Self> {
        let memory_collector = Arc::new(MemoryMetricsCollector::new());

        let prometheus_collector = if config.enable_prometheus {
            Some(Arc::new(PrometheusMetricsCollector::new(MetricsConfig::default())))
        } else {
            None
        };

        let alert_manager = Arc::new(RwLock::new(AlertManager::new()));

        let collector = Self {
            memory_collector,
            prometheus_collector,
            alert_manager,
            config,
        };

        // 启动后台任务
        collector.start_background_tasks().await;

        Ok(collector)
    }

    /// 添加告警规则
    pub async fn add_alert_rule(&self, rule: AlertRule) {
        let mut alert_manager = self.alert_manager.write().await;
        alert_manager.rules.push(rule);
    }

    /// 获取活跃告警
    pub async fn get_active_alerts(&self) -> Vec<Alert> {
        let alert_manager = self.alert_manager.read().await;
        alert_manager.active_alerts.values().cloned().collect()
    }

    /// 获取告警历史
    pub async fn get_alert_history(&self, limit: Option<usize>) -> Vec<Alert> {
        let alert_manager = self.alert_manager.read().await;
        let limit = limit.unwrap_or(100);
        alert_manager.alert_history.iter()
            .rev()
            .take(limit)
            .cloned()
            .collect()
    }

    /// 启动后台任务
    async fn start_background_tasks(&self) {
        if self.config.enable_alerting {
            self.start_alert_checking().await;
        }

        self.start_metrics_aggregation().await;
    }

    /// 启动告警检查
    async fn start_alert_checking(&self) {
        let alert_manager = self.alert_manager.clone();
        let memory_collector = self.memory_collector.clone();
        let check_interval = self.config.alert_check_interval_secs;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(check_interval));

            loop {
                interval.tick().await;

                if let Ok(metrics) = memory_collector.get_metrics().await {
                    let mut alert_mgr = alert_manager.write().await;
                    alert_mgr.check_alerts(&metrics).await;
                }
            }
        });
    }

    /// 启动指标聚合
    async fn start_metrics_aggregation(&self) {
        let memory_collector = self.memory_collector.clone();
        let aggregation_interval = self.config.aggregation_interval_secs;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(aggregation_interval));

            loop {
                interval.tick().await;

                // 这里可以添加指标聚合逻辑
                debug!("Aggregating metrics");
            }
        });
    }
}

#[async_trait]
impl MetricsCollector for UnifiedRiskMetricsCollector {
    async fn record_risk_check(&self, passed: bool) {
        self.memory_collector.record_risk_check(passed).await;

        if let Some(prometheus) = &self.prometheus_collector {
            prometheus.record_risk_check(passed).await;
        }
    }

    async fn record_cache_hit(&self) {
        self.memory_collector.record_cache_hit().await;

        if let Some(prometheus) = &self.prometheus_collector {
            prometheus.record_cache_hit().await;
        }
    }

    async fn record_latency(&self, operation: &str, duration: Duration) {
        self.memory_collector.record_latency(operation, duration).await;

        if let Some(prometheus) = &self.prometheus_collector {
            prometheus.record_latency(operation, duration).await;
        }
    }

    async fn get_metrics(&self) -> SigmaXResult<HashMap<String, f64>> {
        // 合并内存和Prometheus指标
        let mut combined_metrics = self.memory_collector.get_metrics().await?;

        if let Some(prometheus) = &self.prometheus_collector {
            let prometheus_metrics = prometheus.get_metrics().await?;
            for (key, value) in prometheus_metrics {
                combined_metrics.insert(format!("prometheus_{}", key), value);
            }
        }

        Ok(combined_metrics)
    }
}

impl AlertManager {
    fn new() -> Self {
        Self {
            rules: Vec::new(),
            active_alerts: HashMap::new(),
            alert_history: Vec::new(),
        }
    }

    async fn check_alerts(&mut self, metrics: &HashMap<String, f64>) {
        for rule in &self.rules {
            if !rule.enabled {
                continue;
            }

            if let Some(metric_value) = metrics.get(&rule.metric_name) {
                let should_alert = match rule.operator {
                    AlertOperator::GreaterThan => *metric_value > rule.threshold,
                    AlertOperator::LessThan => *metric_value < rule.threshold,
                    AlertOperator::Equal => (*metric_value - rule.threshold).abs() < f64::EPSILON,
                    AlertOperator::NotEqual => (*metric_value - rule.threshold).abs() > f64::EPSILON,
                };

                if should_alert {
                    self.trigger_alert(rule, *metric_value).await;
                } else {
                    self.resolve_alert(&rule.name).await;
                }
            }
        }
    }

    async fn trigger_alert(&mut self, rule: &AlertRule, metric_value: f64) {
        let alert_id = format!("{}_{}", rule.name, chrono::Utc::now().timestamp());

        if !self.active_alerts.contains_key(&rule.name) {
            let alert = Alert {
                id: alert_id,
                rule_name: rule.name.clone(),
                message: format!("Metric {} value {} {} threshold {}",
                    rule.metric_name, metric_value,
                    match rule.operator {
                        AlertOperator::GreaterThan => ">",
                        AlertOperator::LessThan => "<",
                        AlertOperator::Equal => "==",
                        AlertOperator::NotEqual => "!=",
                    },
                    rule.threshold),
                severity: rule.severity.clone(),
                triggered_at: chrono::Utc::now(),
                resolved: false,
                resolved_at: None,
            };

            self.active_alerts.insert(rule.name.clone(), alert.clone());
            self.alert_history.push(alert);

            info!("Alert triggered: {}", rule.name);
        }
    }

    async fn resolve_alert(&mut self, rule_name: &str) {
        if let Some(mut alert) = self.active_alerts.remove(rule_name) {
            alert.resolved = true;
            alert.resolved_at = Some(chrono::Utc::now());

            // 更新历史记录
            if let Some(history_alert) = self.alert_history.iter_mut()
                .rev()
                .find(|a| a.rule_name == rule_name && !a.resolved) {
                history_alert.resolved = true;
                history_alert.resolved_at = alert.resolved_at;
            }

            info!("Alert resolved: {}", rule_name);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_memory_metrics_collector() {
        let collector = MemoryMetricsCollector::new();

        // 记录一些指标
        collector.record_risk_check(true).await;
        collector.record_risk_check(false).await;
        collector.record_risk_check(true).await;

        collector.record_cache_hit().await;
        collector.record_cache_miss().await;
        collector.record_cache_hit().await;

        collector.record_latency("order_check", Duration::from_millis(50)).await;
        collector.record_latency("order_check", Duration::from_millis(75)).await;

        // 获取指标
        let metrics = collector.get_metrics().await.unwrap();

        assert_eq!(metrics.get("risk_checks_total"), Some(&3.0));
        assert_eq!(metrics.get("risk_checks_passed"), Some(&2.0));
        assert_eq!(metrics.get("risk_checks_failed"), Some(&1.0));
        assert!((metrics.get("risk_pass_rate").unwrap() - 0.6666666666666666).abs() < 0.001);

        assert_eq!(metrics.get("cache_hits"), Some(&2.0));
        assert_eq!(metrics.get("cache_misses"), Some(&1.0));
        assert!((metrics.get("cache_hit_rate").unwrap() - 0.6666666666666666).abs() < 0.001);

        assert!(metrics.contains_key("order_check_latency_avg_ms"));
        assert!(metrics.contains_key("uptime_seconds"));
    }

    #[tokio::test]
    async fn test_custom_metrics() {
        let collector = MemoryMetricsCollector::new();

        collector.set_custom_metric("custom_counter".to_string(), 10.0).await;
        collector.increment_custom_metric("custom_counter".to_string(), 5.0).await;

        let metrics = collector.get_metrics().await.unwrap();
        assert_eq!(metrics.get("custom_counter"), Some(&15.0));
    }

    #[tokio::test]
    async fn test_unified_metrics_collector() {
        let config = UnifiedMetricsConfig::default();
        let collector = UnifiedRiskMetricsCollector::new(config).await.unwrap();

        // 测试基本指标收集
        collector.record_risk_check(true).await;
        collector.record_cache_hit().await;
        collector.record_latency("test_operation", Duration::from_millis(10)).await;

        let metrics = collector.get_metrics().await.unwrap();
        assert!(metrics.len() > 0);
    }

    #[tokio::test]
    async fn test_alert_manager() {
        let mut alert_manager = AlertManager::new();

        // 添加告警规则
        let rule = AlertRule {
            name: "high_error_rate".to_string(),
            metric_name: "error_rate".to_string(),
            threshold: 0.1,
            operator: AlertOperator::GreaterThan,
            duration_secs: 60,
            severity: AlertSeverity::Warning,
            enabled: true,
        };

        alert_manager.rules.push(rule);

        // 模拟指标数据
        let mut metrics = HashMap::new();
        metrics.insert("error_rate".to_string(), 0.15);

        // 检查告警
        alert_manager.check_alerts(&metrics).await;

        assert_eq!(alert_manager.active_alerts.len(), 1);
        assert_eq!(alert_manager.alert_history.len(), 1);
    }
}
