//! 统一的风险指标计算器
//! 
//! 协调各个子模块的计算，提供统一的接口

use sigmax_core::{Amount, SigmaXResult, SigmaXError};
use rust_decimal::Decimal;
use std::collections::VecDeque;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

use super::{
    BasicRiskMetrics, BasicRiskCalculator,
    RatioMetrics, RatioCalculator,
    DrawdownMetrics, DrawdownCalculator,
    MarketMetrics, MarketCalculator,
};

/// 综合风险指标（重构后的版本）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComprehensiveRiskMetrics {
    /// 基础风险指标
    pub basic: BasicRiskMetrics,
    /// 收益风险比率
    pub ratios: RatioMetrics,
    /// 回撤指标
    pub drawdown: DrawdownMetrics,
    /// 市场相关指标
    pub market: MarketMetrics,
    /// 计算时间戳
    pub calculated_at: DateTime<Utc>,
}

/// 统一的风险指标计算器
pub struct MetricsCalculator {
    returns_history: VecDeque<Decimal>,
    benchmark_returns: VecDeque<Decimal>,
    equity_curve: VecDeque<Amount>,
    current_portfolio_value: Amount,
    risk_free_rate: Decimal,
    max_history_length: usize,
}

impl MetricsCalculator {
    /// 创建新的计算器
    pub fn new(max_history_length: usize) -> Self {
        Self {
            returns_history: VecDeque::new(),
            benchmark_returns: VecDeque::new(),
            equity_curve: VecDeque::new(),
            current_portfolio_value: Amount::ZERO,
            risk_free_rate: Decimal::new(2, 2), // 2% 年化无风险利率
            max_history_length,
        }
    }

    /// 设置无风险利率
    pub fn set_risk_free_rate(&mut self, rate: Decimal) {
        self.risk_free_rate = rate;
    }

    /// 更新收益率数据
    pub fn update_returns(&mut self, portfolio_return: Decimal, benchmark_return: Option<Decimal>) {
        self.returns_history.push_back(portfolio_return);
        if self.returns_history.len() > self.max_history_length {
            self.returns_history.pop_front();
        }

        if let Some(bench_return) = benchmark_return {
            self.benchmark_returns.push_back(bench_return);
            if self.benchmark_returns.len() > self.max_history_length {
                self.benchmark_returns.pop_front();
            }
        }
    }

    /// 更新投资组合价值
    pub fn update_portfolio_value(&mut self, value: Amount) {
        self.current_portfolio_value = value;
        self.equity_curve.push_back(value);
        if self.equity_curve.len() > self.max_history_length {
            self.equity_curve.pop_front();
        }
    }

    /// 计算综合风险指标
    pub fn calculate_comprehensive_metrics(&self) -> SigmaXResult<ComprehensiveRiskMetrics> {
        if self.returns_history.len() < 30 {
            return Err(SigmaXError::InvalidOperation(
                "需要至少30个历史数据点来计算综合风险指标".to_string()
            ));
        }

        // 计算基础指标
        let basic_calculator = BasicRiskCalculator::new(
            &self.returns_history,
            self.current_portfolio_value,
            self.risk_free_rate,
        );
        let basic = basic_calculator.calculate()?;

        // 计算回撤指标
        let drawdown_calculator = DrawdownCalculator::new(
            &self.equity_curve,
            &self.returns_history,
        );
        let drawdown = drawdown_calculator.calculate()?;

        // 计算比率指标
        let ratio_calculator = RatioCalculator::new(
            &self.returns_history,
            &self.benchmark_returns,
            self.risk_free_rate,
            drawdown.max_drawdown,
            basic.downside_volatility,
            basic.volatility,
        );
        let ratios = ratio_calculator.calculate()?;

        // 计算市场指标
        let market_calculator = MarketCalculator::new(
            &self.returns_history,
            &self.benchmark_returns,
            self.risk_free_rate,
        );
        let market = market_calculator.calculate()?;

        Ok(ComprehensiveRiskMetrics {
            basic,
            ratios,
            drawdown,
            market,
            calculated_at: Utc::now(),
        })
    }

    /// 计算基础风险指标
    pub fn calculate_basic_metrics(&self) -> SigmaXResult<BasicRiskMetrics> {
        let calculator = BasicRiskCalculator::new(
            &self.returns_history,
            self.current_portfolio_value,
            self.risk_free_rate,
        );
        calculator.calculate()
    }

    /// 计算比率指标
    pub fn calculate_ratio_metrics(&self) -> SigmaXResult<RatioMetrics> {
        if self.returns_history.len() < 30 {
            return Err(SigmaXError::InvalidOperation(
                "需要至少30个历史数据点来计算比率指标".to_string()
            ));
        }

        // 先计算必要的基础指标
        let basic_calculator = BasicRiskCalculator::new(
            &self.returns_history,
            self.current_portfolio_value,
            self.risk_free_rate,
        );
        let basic = basic_calculator.calculate()?;

        let drawdown_calculator = DrawdownCalculator::new(
            &self.equity_curve,
            &self.returns_history,
        );
        let drawdown = drawdown_calculator.calculate()?;

        let calculator = RatioCalculator::new(
            &self.returns_history,
            &self.benchmark_returns,
            self.risk_free_rate,
            drawdown.max_drawdown,
            basic.downside_volatility,
            basic.volatility,
        );
        calculator.calculate()
    }

    /// 计算回撤指标
    pub fn calculate_drawdown_metrics(&self) -> SigmaXResult<DrawdownMetrics> {
        let calculator = DrawdownCalculator::new(
            &self.equity_curve,
            &self.returns_history,
        );
        calculator.calculate()
    }

    /// 计算市场指标
    pub fn calculate_market_metrics(&self) -> SigmaXResult<MarketMetrics> {
        let calculator = MarketCalculator::new(
            &self.returns_history,
            &self.benchmark_returns,
            self.risk_free_rate,
        );
        calculator.calculate()
    }

    /// 获取历史数据统计
    pub fn get_data_statistics(&self) -> DataStatistics {
        DataStatistics {
            returns_count: self.returns_history.len(),
            benchmark_count: self.benchmark_returns.len(),
            equity_points: self.equity_curve.len(),
            max_history_length: self.max_history_length,
            has_benchmark: !self.benchmark_returns.is_empty(),
        }
    }

    /// 清空历史数据
    pub fn clear_history(&mut self) {
        self.returns_history.clear();
        self.benchmark_returns.clear();
        self.equity_curve.clear();
    }

    /// 获取当前投资组合价值
    pub fn current_portfolio_value(&self) -> Amount {
        self.current_portfolio_value
    }

    /// 获取无风险利率
    pub fn risk_free_rate(&self) -> Decimal {
        self.risk_free_rate
    }
}

/// 数据统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataStatistics {
    /// 收益率数据点数量
    pub returns_count: usize,
    /// 基准收益率数据点数量
    pub benchmark_count: usize,
    /// 净值曲线数据点数量
    pub equity_points: usize,
    /// 最大历史长度
    pub max_history_length: usize,
    /// 是否有基准数据
    pub has_benchmark: bool,
}

impl Default for MetricsCalculator {
    fn default() -> Self {
        Self::new(1000) // 默认保留1000个数据点
    }
}
