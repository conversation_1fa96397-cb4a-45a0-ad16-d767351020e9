//! 适配器工厂 - 依赖注入管理
//! 
//! 设计原则：
//! - 工厂模式：统一的适配器创建接口
//! - 依赖注入：配置驱动的对象创建
//! - 生命周期管理：适配器的创建和销毁

use async_trait::async_trait;
use sigmax_core::{EngineType, SigmaXResult, EngineRiskAdapter};
use std::sync::Arc;
use tracing::{debug, info};

use super::{
    RiskServiceContainer, RiskServiceConfig, RiskMode,
    CacheService, MetricsCollector, RiskEngine
};

/// 适配器工厂
pub struct AdapterFactory {
    container: Arc<RiskServiceContainer>,
}

impl AdapterFactory {
    /// 创建新的适配器工厂
    pub fn new(container: Arc<RiskServiceContainer>) -> Self {
        Self { container }
    }
    
    /// 创建引擎风控适配器
    pub async fn create_adapter(&self, engine_type: EngineType) -> SigmaXResult<Arc<dyn EngineRiskAdapter>> {
        info!("Creating risk adapter for engine type: {:?}", engine_type);
        
        let risk_mode = RiskMode::from(engine_type);
        
        match risk_mode {
            RiskMode::Backtest => {
                debug!("Creating BacktestRiskAdapter with high-throughput optimization");
                // TODO: 实际实现将在 engines/src/backtest/risk_adapter.rs 中
                self.create_backtest_adapter().await
            }
            RiskMode::LiveTrading => {
                debug!("Creating LiveTradingRiskAdapter with low-latency optimization");
                // TODO: 实际实现将在 engines/src/live/risk_adapter.rs 中
                self.create_live_adapter().await
            }
            RiskMode::WebApi => {
                debug!("Creating WebApiRiskAdapter with detailed results");
                // TODO: 实际实现将在 web 模块中
                self.create_web_adapter().await
            }
            RiskMode::Strategy => {
                debug!("Creating StrategyRiskAdapter with strategy switching support");
                // TODO: 实际实现将在 strategies 模块中
                self.create_strategy_adapter().await
            }
        }
    }
    
    /// 创建回测适配器 (伪代码)
    async fn create_backtest_adapter(&self) -> SigmaXResult<Arc<dyn EngineRiskAdapter>> {
        // 伪代码：实际实现将在 engines/src/backtest/risk_adapter.rs
        /*
        let adapter = BacktestRiskAdapter::new(
            self.container.get_risk_engine(),
            self.container.get_cache_service(),
            self.container.get_metrics_collector(),
            BacktestAdapterConfig {
                batch_size: self.container.config().batch_size,
                enable_batch_cache: true,
                cache_ttl: Duration::from_secs(300), // 5分钟
                high_throughput_mode: true,
            }
        ).await?;
        
        Ok(Arc::new(adapter))
        */
        
        // 临时返回，实际实现时替换
        Err(sigmax_core::SigmaXError::NotImplemented("BacktestRiskAdapter not implemented yet".to_string()))
    }
    
    /// 创建实盘适配器 (伪代码)
    async fn create_live_adapter(&self) -> SigmaXResult<Arc<dyn EngineRiskAdapter>> {
        // 伪代码：实际实现将在 engines/src/live/risk_adapter.rs
        /*
        let adapter = LiveTradingRiskAdapter::new(
            self.container.get_risk_engine(),
            self.container.get_cache_service(),
            self.container.get_metrics_collector(),
            LiveAdapterConfig {
                low_latency_mode: true,
                hot_cache_size: 1000,
                cache_ttl: Duration::from_secs(60), // 1分钟
                circuit_breaker_threshold: 0.1,
                timeout_ms: 100, // 100ms超时
            }
        ).await?;
        
        Ok(Arc::new(adapter))
        */
        
        // 临时返回，实际实现时替换
        Err(sigmax_core::SigmaXError::NotImplemented("LiveTradingRiskAdapter not implemented yet".to_string()))
    }
    
    /// 创建Web API适配器 (伪代码)
    async fn create_web_adapter(&self) -> SigmaXResult<Arc<dyn EngineRiskAdapter>> {
        // 伪代码：实际实现将在 web 模块中
        /*
        let adapter = WebApiRiskAdapter::new(
            self.container.get_risk_engine(),
            self.container.get_cache_service(),
            self.container.get_metrics_collector(),
            WebApiAdapterConfig {
                detailed_results: true,
                include_rule_details: true,
                cache_ttl: Duration::from_secs(30), // 30秒
                max_response_size: 1024 * 1024, // 1MB
            }
        ).await?;
        
        Ok(Arc::new(adapter))
        */
        
        // 临时返回，实际实现时替换
        Err(sigmax_core::SigmaXError::NotImplemented("WebApiRiskAdapter not implemented yet".to_string()))
    }
    
    /// 创建策略适配器 (伪代码)
    async fn create_strategy_adapter(&self) -> SigmaXResult<Arc<dyn EngineRiskAdapter>> {
        // 伪代码：实际实现将在 strategies 模块中
        /*
        let adapter = StrategyRiskAdapter::new(
            self.container.get_risk_engine(),
            self.container.get_cache_service(),
            self.container.get_metrics_collector(),
            StrategyAdapterConfig {
                strategy_switching_support: true,
                risk_assessment_mode: true,
                cache_ttl: Duration::from_secs(120), // 2分钟
                performance_analysis: true,
            }
        ).await?;
        
        Ok(Arc::new(adapter))
        */
        
        // 临时返回，实际实现时替换
        Err(sigmax_core::SigmaXError::NotImplemented("StrategyRiskAdapter not implemented yet".to_string()))
    }
    
    /// 批量创建适配器
    pub async fn create_adapters(&self, engine_types: &[EngineType]) -> SigmaXResult<Vec<(EngineType, Arc<dyn EngineRiskAdapter>)>> {
        let mut adapters = Vec::new();
        
        for &engine_type in engine_types {
            match self.create_adapter(engine_type).await {
                Ok(adapter) => {
                    adapters.push((engine_type, adapter));
                }
                Err(e) => {
                    tracing::warn!("Failed to create adapter for engine {:?}: {}", engine_type, e);
                    // 继续创建其他适配器，不因单个失败而中断
                }
            }
        }
        
        Ok(adapters)
    }
    
    /// 获取服务容器
    pub fn container(&self) -> &Arc<RiskServiceContainer> {
        &self.container
    }
}

/// 适配器工厂构建器
pub struct AdapterFactoryBuilder {
    risk_engine: Option<Arc<dyn RiskEngine>>,
    cache_service: Option<Arc<dyn CacheService>>,
    metrics_collector: Option<Arc<dyn MetricsCollector>>,
    config: RiskServiceConfig,
}

impl AdapterFactoryBuilder {
    pub fn new() -> Self {
        Self {
            risk_engine: None,
            cache_service: None,
            metrics_collector: None,
            config: RiskServiceConfig::default(),
        }
    }
    
    pub fn with_risk_engine(mut self, engine: Arc<dyn RiskEngine>) -> Self {
        self.risk_engine = Some(engine);
        self
    }
    
    pub fn with_cache_service(mut self, cache: Arc<dyn CacheService>) -> Self {
        self.cache_service = Some(cache);
        self
    }
    
    pub fn with_metrics_collector(mut self, metrics: Arc<dyn MetricsCollector>) -> Self {
        self.metrics_collector = Some(metrics);
        self
    }
    
    pub fn with_config(mut self, config: RiskServiceConfig) -> Self {
        self.config = config;
        self
    }
    
    pub async fn build(self) -> SigmaXResult<AdapterFactory> {
        let container = RiskServiceContainer::new(
            self.risk_engine.ok_or_else(|| sigmax_core::SigmaXError::Configuration("Risk engine required".to_string()))?,
            self.cache_service.ok_or_else(|| sigmax_core::SigmaXError::Configuration("Cache service required".to_string()))?,
            self.metrics_collector.ok_or_else(|| sigmax_core::SigmaXError::Configuration("Metrics collector required".to_string()))?,
            self.config,
        ).await?;
        
        Ok(AdapterFactory::new(Arc::new(container)))
    }
}

impl Default for AdapterFactoryBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_adapter_factory_builder() {
        let builder = AdapterFactoryBuilder::new()
            .with_config(RiskServiceConfig {
                enable_cache: true,
                batch_size: 50,
                ..Default::default()
            });
        
        // 注意：这个测试需要Mock实现才能完成
        // 实际测试将在具体适配器实现后添加
        assert_eq!(builder.config.batch_size, 50);
    }
}
