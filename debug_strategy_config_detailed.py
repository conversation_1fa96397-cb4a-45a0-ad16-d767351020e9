#!/usr/bin/env python3
"""
详细调试策略配置传递问题
检查策略配置在每个环节的状态
"""

import requests
import json

def debug_strategy_config_detailed():
    """详细调试策略配置传递问题"""
    base_url = "http://127.0.0.1:8080/api/v1"
    
    print("🔍 详细调试策略配置传递问题")
    print("=" * 70)
    
    try:
        # 1. 创建引擎
        print("\n🔧 创建回测引擎...")
        engine_response = requests.post(f"{base_url}/engines", json={
            "engine_type": "Backtest",
            "trading_pairs": [{"base": "BNB", "quote": "USDT"}],
            "initial_capital": "5000.00"
        })
        
        if engine_response.status_code != 200:
            print(f"❌ 创建引擎失败: {engine_response.status_code} - {engine_response.text}")
            return False
            
        engine_data = engine_response.json()
        engine_id = engine_data["data"]["id"]
        print(f"✅ 引擎创建成功: {engine_id}")
        
        # 2. 准备策略配置 - 使用最简单的配置
        print("\n⚙️ 准备策略配置...")
        strategy_config = {
            "type": "asymmetric_volatility_grid",
            "base_price": 50.0,  # 设置一个具体的基础价格
            "order_amount": 100.0,
            "trading_pair": "BNB/USDT",
            "exchange_id": "binance",
            "down_range_start": -0.02,
            "down_range_end": -0.10,
            "down_grid_count": 3,
            "down_base_quantity": 0.05,
            "up_range_start": 0.02,
            "up_range_end": 0.15,
            "up_grid_count": 3,
            "up_base_quantity": 0.05,
            "volatility_window_hours": 24,
            "volatility_multiplier": 1.0,
            "enable_dynamic_volatility": True,
            "max_position_amount": 1000.0,
            "max_daily_trades": 50,
            "strategy_preset": "Balanced",
            "stop_loss_config": "Disabled"
        }
        
        config_data = {
            "data_file": "BNB_USDT_1d_60.json",
            "start_time": "2021-01-01T00:00:00Z",
            "end_time": "2021-01-03T23:59:59Z",  # 只测试3天
            "initial_capital": "5000.00",
            "trading_pairs": ["BNB_USDT"],
            "timeframe": "1d",
            "strategy_config": strategy_config
        }
        
        print("📋 发送的完整配置:")
        print(json.dumps(config_data, indent=2, ensure_ascii=False))
        
        # 3. 发送配置
        print("\n📤 发送回测配置...")
        config_response = requests.post(f"{base_url}/engines/{engine_id}/backtest/config", 
                                      json=config_data)
        
        if config_response.status_code != 200:
            print(f"❌ 配置失败: {config_response.status_code} - {config_response.text}")
            return False
            
        print("✅ 回测配置发送成功")
        config_result = config_response.json()
        print("📋 服务器返回的配置:")
        print(json.dumps(config_result, indent=2, ensure_ascii=False))
        
        # 4. 立即检查配置是否正确保存
        print("\n🔍 检查配置是否正确保存...")
        check_response = requests.get(f"{base_url}/engines/{engine_id}/backtest/config")
        
        if check_response.status_code != 200:
            print(f"❌ 获取配置失败: {check_response.status_code} - {check_response.text}")
            return False
            
        saved_config = check_response.json()["data"]
        print("✅ 获取保存的配置成功")
        print("📋 保存的配置:")
        print(json.dumps(saved_config, indent=2, ensure_ascii=False))
        
        # 5. 详细检查策略配置
        print("\n🔍 详细检查策略配置...")
        if "strategy_config" in saved_config and saved_config["strategy_config"] is not None:
            strategy_config_saved = saved_config["strategy_config"]
            print("✅ 策略配置存在")
            print(f"  策略类型: {strategy_config_saved.get('type', 'N/A')}")
            print(f"  基础价格: {strategy_config_saved.get('base_price', 'N/A')}")
            print(f"  订单金额: {strategy_config_saved.get('order_amount', 'N/A')}")
            print(f"  交易对: {strategy_config_saved.get('trading_pair', 'N/A')}")
            print(f"  下跌网格数: {strategy_config_saved.get('down_grid_count', 'N/A')}")
            print(f"  上涨网格数: {strategy_config_saved.get('up_grid_count', 'N/A')}")
            
            # 检查必要字段
            required_fields = ['type', 'base_price', 'order_amount', 'trading_pair']
            missing_fields = [field for field in required_fields if field not in strategy_config_saved]
            
            if missing_fields:
                print(f"⚠️ 缺少必要字段: {missing_fields}")
                return False
            else:
                print("✅ 所有必要字段都存在")
        else:
            print("❌ 策略配置丢失！")
            return False
        
        # 6. 启动引擎并监控
        print("\n🚀 启动引擎...")
        start_response = requests.post(f"{base_url}/engines/{engine_id}/start")
        
        if start_response.status_code != 200:
            print(f"❌ 启动引擎失败: {start_response.status_code} - {start_response.text}")
            return False
            
        print("✅ 引擎启动成功")
        
        # 7. 等待一段时间让引擎处理
        print("\n⏳ 等待引擎处理...")
        import time
        time.sleep(5)
        
        # 8. 检查进度
        print("\n📊 检查回测进度...")
        progress_response = requests.get(f"{base_url}/engines/{engine_id}/backtest/progress")
        if progress_response.status_code == 200:
            progress_data = progress_response.json()["data"]
            current = progress_data.get("current", 0)
            total = progress_data.get("total", 0)
            percentage = progress_data.get("percentage", 0.0)
            
            print(f"  进度: {current}/{total} ({percentage:.1f}%)")
            
            if total == 0:
                print("❌ 关键问题：没有加载任何K线数据！")
                print("🔍 这表明数据加载失败")
                return False
            elif current == total:
                print("✅ 回测已完成")
            else:
                print("⏳ 回测正在进行中")
        else:
            print(f"⚠️ 获取进度失败: {progress_response.status_code}")
        
        # 9. 获取结果
        print("\n📈 获取回测结果...")
        result_response = requests.get(f"{base_url}/engines/{engine_id}/backtest/result")
        
        if result_response.status_code != 200:
            print(f"❌ 获取结果失败: {result_response.status_code} - {result_response.text}")
            return False
            
        result_data = result_response.json()["data"]
        print("✅ 获取回测结果成功")
        
        # 10. 分析结果
        print("\n📊 分析回测结果:")
        print(f"  总收益率: {result_data.get('total_return', 0):.2f}%")
        print(f"  最大回撤: {result_data.get('max_drawdown', 0):.2f}%")
        print(f"  胜率: {result_data.get('win_rate', 0):.2f}%")
        print(f"  总交易数: {result_data.get('total_trades', 0)}")
        
        # 11. 最终诊断
        total_trades = result_data.get('total_trades', 0)
        if total_trades == 0:
            print("\n❌ 关键问题：没有生成任何交易！")
            print("🔍 可能的原因:")
            print("  1. 策略没有正确初始化")
            print("  2. 策略配置有问题")
            print("  3. 策略的 on_market_data 方法没有被调用")
            print("  4. 策略的 on_market_data 方法没有生成订单")
            print("  5. 生成的订单被风险检查拒绝")
            
            print("\n💡 建议检查服务器日志中的以下信息:")
            print("  - '使用直接传入的策略配置创建策略'")
            print("  - '创建策略: 类型=asymmetric_volatility_grid'")
            print("  - '非对称波动率网格策略创建成功'")
            print("  - '✅ 策略已成功添加到回测引擎'")
            print("  - 策略的 on_market_data 调用日志")
            print("  - 订单生成日志")
            
            return False
        else:
            print(f"\n✅ 成功生成了 {total_trades} 笔交易")
            return True
            
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始详细调试策略配置传递问题")
    print("=" * 70)
    
    success = debug_strategy_config_detailed()
    
    print("\n📊 调试结果总结")
    print("=" * 70)
    if success:
        print("✅ 策略配置调试成功，策略正常工作")
    else:
        print("❌ 策略配置调试失败，需要进一步检查")
        print("\n🔧 下一步行动:")
        print("1. 检查服务器日志中的策略初始化信息")
        print("2. 确认策略配置是否正确传递到回测引擎")
        print("3. 确认策略是否被正确创建和添加")
        print("4. 检查数据加载是否成功")
        print("5. 检查策略的 on_market_data 方法是否被调用")
