//! 自动响应系统接口定义
//! 
//! 定义策略控制器和订单管理器的接口，用于执行风险响应动作

use sigmax_core::{SigmaXResult, Order, StrategyId, TradingPair};
use async_trait::async_trait;
use std::collections::HashMap;

/// 策略控制器接口
/// 
/// 提供策略级别的控制操作，如暂停、停止、调整参数等
#[async_trait]
pub trait StrategyController: Send + Sync {
    /// 暂停策略
    /// 
    /// # 参数
    /// * `strategy_id` - 策略ID
    /// * `duration_minutes` - 暂停时长（分钟），None表示无限期暂停
    async fn pause_strategy(
        &self,
        strategy_id: StrategyId,
        duration_minutes: Option<u32>,
    ) -> SigmaXResult<()>;

    /// 停止策略
    /// 
    /// # 参数
    /// * `strategy_id` - 策略ID
    async fn stop_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<()>;

    /// 减少仓位大小
    /// 
    /// # 参数
    /// * `strategy_id` - 策略ID
    /// * `reduction_percentage` - 减少百分比 (0.0-100.0)
    async fn reduce_position_size(
        &self,
        strategy_id: StrategyId,
        reduction_percentage: f64,
    ) -> SigmaXResult<()>;

    /// 调整风险参数
    /// 
    /// # 参数
    /// * `strategy_id` - 策略ID
    /// * `parameter_adjustments` - 参数调整映射
    async fn adjust_risk_parameters(
        &self,
        strategy_id: StrategyId,
        parameter_adjustments: HashMap<String, f64>,
    ) -> SigmaXResult<()>;

    /// 获取策略状态
    /// 
    /// # 参数
    /// * `strategy_id` - 策略ID
    async fn get_strategy_status(&self, strategy_id: StrategyId) -> SigmaXResult<StrategyStatus>;

    /// 恢复策略
    /// 
    /// # 参数
    /// * `strategy_id` - 策略ID
    async fn resume_strategy(&self, strategy_id: StrategyId) -> SigmaXResult<()>;
}

/// 订单管理器接口
/// 
/// 提供订单级别的操作，如取消订单、创建平仓订单等
#[async_trait]
pub trait OrderManager: Send + Sync {
    /// 取消订单
    /// 
    /// # 参数
    /// * `strategy_id` - 策略ID（可选）
    /// * `trading_pair` - 交易对（可选）
    /// * `order_side` - 订单方向（可选）
    async fn cancel_orders(
        &self,
        strategy_id: Option<StrategyId>,
        trading_pair: Option<TradingPair>,
        order_side: Option<sigmax_core::OrderSide>,
    ) -> SigmaXResult<Vec<String>>;

    /// 创建平仓订单
    /// 
    /// # 参数
    /// * `strategy_id` - 策略ID（可选）
    /// * `trading_pair` - 交易对（可选）
    /// * `close_percentage` - 平仓百分比 (0.0-100.0)
    async fn create_close_orders(
        &self,
        strategy_id: Option<StrategyId>,
        trading_pair: Option<TradingPair>,
        close_percentage: f64,
    ) -> SigmaXResult<Vec<Order>>;

    /// 获取活跃订单
    /// 
    /// # 参数
    /// * `strategy_id` - 策略ID（可选）
    /// * `trading_pair` - 交易对（可选）
    async fn get_active_orders(
        &self,
        strategy_id: Option<StrategyId>,
        trading_pair: Option<TradingPair>,
    ) -> SigmaXResult<Vec<Order>>;

    /// 批量取消订单
    /// 
    /// # 参数
    /// * `order_ids` - 订单ID列表
    async fn batch_cancel_orders(&self, order_ids: Vec<String>) -> SigmaXResult<Vec<String>>;
}

/// 策略状态
#[derive(Debug, Clone, PartialEq)]
pub enum StrategyStatus {
    /// 运行中
    Running,
    /// 已暂停
    Paused {
        /// 暂停时间
        paused_at: chrono::DateTime<chrono::Utc>,
        /// 恢复时间（如果有）
        resume_at: Option<chrono::DateTime<chrono::Utc>>,
    },
    /// 已停止
    Stopped {
        /// 停止时间
        stopped_at: chrono::DateTime<chrono::Utc>,
    },
    /// 错误状态
    Error {
        /// 错误信息
        error_message: String,
        /// 错误时间
        error_at: chrono::DateTime<chrono::Utc>,
    },
}

/// 通知发送器接口
/// 
/// 用于发送各种类型的通知
#[async_trait]
pub trait NotificationSender: Send + Sync {
    /// 发送紧急通知
    async fn send_emergency_notification(&self, message: &str) -> SigmaXResult<()>;

    /// 发送警告通知
    async fn send_warning_notification(&self, message: &str) -> SigmaXResult<()>;

    /// 发送信息通知
    async fn send_info_notification(&self, message: &str) -> SigmaXResult<()>;

    /// 发送自定义通知
    async fn send_custom_notification(
        &self,
        message: &str,
        severity: crate::alert_system::AlertSeverity,
        channels: Vec<NotificationChannel>,
    ) -> SigmaXResult<()>;
}

/// 通知渠道
#[derive(Debug, Clone, PartialEq)]
pub enum NotificationChannel {
    /// 邮件
    Email,
    /// 短信
    SMS,
    /// 微信
    WeChat,
    /// 钉钉
    DingTalk,
    /// Slack
    Slack,
    /// 系统日志
    SystemLog,
    /// 数据库记录
    Database,
}

impl StrategyStatus {
    /// 检查策略是否处于活跃状态
    pub fn is_active(&self) -> bool {
        matches!(self, StrategyStatus::Running)
    }

    /// 检查策略是否已暂停
    pub fn is_paused(&self) -> bool {
        matches!(self, StrategyStatus::Paused { .. })
    }

    /// 检查策略是否已停止
    pub fn is_stopped(&self) -> bool {
        matches!(self, StrategyStatus::Stopped { .. })
    }

    /// 检查策略是否处于错误状态
    pub fn is_error(&self) -> bool {
        matches!(self, StrategyStatus::Error { .. })
    }

    /// 获取状态描述
    pub fn description(&self) -> String {
        match self {
            StrategyStatus::Running => "运行中".to_string(),
            StrategyStatus::Paused { paused_at, resume_at } => {
                if let Some(resume_time) = resume_at {
                    format!("已暂停，将于 {} 恢复", resume_time.format("%Y-%m-%d %H:%M:%S"))
                } else {
                    format!("已暂停于 {}", paused_at.format("%Y-%m-%d %H:%M:%S"))
                }
            }
            StrategyStatus::Stopped { stopped_at } => {
                format!("已停止于 {}", stopped_at.format("%Y-%m-%d %H:%M:%S"))
            }
            StrategyStatus::Error { error_message, error_at } => {
                format!("错误状态: {} ({})", error_message, error_at.format("%Y-%m-%d %H:%M:%S"))
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;

    #[test]
    fn test_strategy_status() {
        let running = StrategyStatus::Running;
        assert!(running.is_active());
        assert!(!running.is_paused());

        let paused = StrategyStatus::Paused {
            paused_at: Utc::now(),
            resume_at: None,
        };
        assert!(!paused.is_active());
        assert!(paused.is_paused());

        let stopped = StrategyStatus::Stopped {
            stopped_at: Utc::now(),
        };
        assert!(stopped.is_stopped());
        assert!(!stopped.is_active());
    }

    #[test]
    fn test_status_description() {
        let running = StrategyStatus::Running;
        assert_eq!(running.description(), "运行中");

        let error = StrategyStatus::Error {
            error_message: "测试错误".to_string(),
            error_at: Utc::now(),
        };
        assert!(error.description().contains("错误状态: 测试错误"));
    }
}
