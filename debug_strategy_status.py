#!/usr/bin/env python3
"""
调试策略状态问题
检查策略是否正确初始化和运行
"""

import requests
import json
import time

def debug_strategy_status():
    """调试策略状态问题"""
    base_url = "http://127.0.0.1:8080/api/v1"
    
    print("🔍 调试策略状态问题")
    print("=" * 70)
    
    try:
        # 1. 创建引擎
        print("\n🔧 创建回测引擎...")
        engine_response = requests.post(f"{base_url}/engines", json={
            "engine_type": "Backtest",
            "trading_pairs": [{"base": "BNB", "quote": "USDT"}],
            "initial_capital": "5000.00"
        })
        
        if engine_response.status_code != 200:
            print(f"❌ 创建引擎失败: {engine_response.status_code} - {engine_response.text}")
            return False
            
        engine_data = engine_response.json()
        engine_id = engine_data["data"]["id"]
        print(f"✅ 引擎创建成功: {engine_id}")
        
        # 2. 配置回测参数 - 使用简单的网格策略
        print("\n⚙️ 配置回测参数...")
        config_data = {
            "data_file": "BNB_USDT_1d_60.json",
            "start_time": "2021-01-01T00:00:00Z",
            "end_time": "2021-01-05T23:59:59Z",  # 只测试几天
            "initial_capital": "5000.00",
            "trading_pairs": ["BNB_USDT"],
            "timeframe": "1d",
            "strategy_config": {
                "type": "asymmetric_volatility_grid",  # 添加策略类型
                "base_price": 0.0,  # 使用当前市价
                "order_amount": 100.0,
                "trading_pair": "BNB/USDT",
                "exchange_id": "binance",
                "down_range_start": -0.02,  # -2%
                "down_range_end": -0.10,    # -10%
                "down_grid_count": 3,
                "down_base_quantity": 0.05,
                "up_range_start": 0.02,     # +2%
                "up_range_end": 0.15,       # +15%
                "up_grid_count": 3,
                "up_base_quantity": 0.05,
                "volatility_window_hours": 24,
                "volatility_multiplier": 1.0,
                "enable_dynamic_volatility": True,
                "max_position_amount": 1000.0,
                "max_daily_trades": 50,
                "strategy_preset": "Balanced",
                "stop_loss_config": "Disabled"
            }
        }
        
        print("📋 发送的配置:")
        print(json.dumps(config_data, indent=2, ensure_ascii=False))
        
        config_response = requests.post(f"{base_url}/engines/{engine_id}/backtest/config", 
                                      json=config_data)
        
        if config_response.status_code != 200:
            print(f"❌ 配置失败: {config_response.status_code} - {config_response.text}")
            return False
            
        print("✅ 回测配置成功")
        
        # 3. 检查配置是否正确保存
        print("\n🔍 检查配置是否正确保存...")
        check_response = requests.get(f"{base_url}/engines/{engine_id}/backtest/config")
        
        if check_response.status_code != 200:
            print(f"❌ 获取配置失败: {check_response.status_code} - {check_response.text}")
            return False
            
        saved_config = check_response.json()["data"]
        print("✅ 获取保存的配置成功")
        
        if "strategy_config" in saved_config and saved_config["strategy_config"] is not None:
            print("✅ 策略配置已正确保存")
            strategy_config = saved_config["strategy_config"]
            print(f"  策略类型: {strategy_config.get('type', 'N/A')}")
        else:
            print("❌ 策略配置丢失")
            return False
        
        # 4. 启动引擎并监控日志
        print("\n🚀 启动引擎...")
        start_response = requests.post(f"{base_url}/engines/{engine_id}/start")
        
        if start_response.status_code != 200:
            print(f"❌ 启动引擎失败: {start_response.status_code} - {start_response.text}")
            return False
            
        print("✅ 引擎启动成功")
        
        # 5. 监控回测进度
        print("\n📊 监控回测进度...")
        max_wait_time = 60  # 最多等待60秒
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                progress_response = requests.get(f"{base_url}/engines/{engine_id}/backtest/progress")
                if progress_response.status_code == 200:
                    progress_data = progress_response.json()["data"]
                    current = progress_data.get("current", 0)
                    total = progress_data.get("total", 0)
                    percentage = progress_data.get("percentage", 0.0)
                    
                    print(f"  进度: {current}/{total} ({percentage:.1f}%)")
                    
                    if percentage >= 100.0:
                        print("✅ 回测完成")
                        break
                else:
                    print(f"⚠️ 获取进度失败: {progress_response.status_code}")
                    
            except Exception as e:
                print(f"⚠️ 获取进度时发生错误: {e}")
            
            time.sleep(2)
        
        # 6. 获取回测结果
        print("\n📈 获取回测结果...")
        result_response = requests.get(f"{base_url}/engines/{engine_id}/backtest/result")
        
        if result_response.status_code != 200:
            print(f"❌ 获取结果失败: {result_response.status_code} - {result_response.text}")
            return False
            
        result_data = result_response.json()["data"]
        print("✅ 获取回测结果成功")
        
        # 7. 分析结果
        print("\n📊 分析回测结果:")
        print(f"  总收益率: {result_data.get('total_return', 0):.2f}%")
        print(f"  最大回撤: {result_data.get('max_drawdown', 0):.2f}%")
        print(f"  胜率: {result_data.get('win_rate', 0):.2f}%")
        print(f"  总交易数: {result_data.get('total_trades', 0)}")
        
        # 8. 关键诊断
        total_trades = result_data.get('total_trades', 0)
        if total_trades == 0:
            print("\n❌ 关键问题：没有生成任何交易！")
            print("🔍 可能的原因:")
            print("  1. 策略没有正确初始化")
            print("  2. 策略状态不是 Running")
            print("  3. 策略的 on_market_data 方法没有被调用")
            print("  4. 策略的 on_market_data 方法没有生成订单")
            print("  5. 生成的订单被风险检查拒绝")
            
            print("\n💡 建议检查服务器日志中的以下信息:")
            print("  - 策略初始化日志")
            print("  - 策略状态检查日志")
            print("  - on_market_data 调用日志")
            print("  - 订单生成日志")
            print("  - 风险检查日志")
            
            return False
        else:
            print(f"\n✅ 成功生成了 {total_trades} 笔交易")
            return True
            
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始调试策略状态问题")
    print("=" * 70)
    
    success = debug_strategy_status()
    
    print("\n📊 调试结果总结")
    print("=" * 70)
    if success:
        print("✅ 策略状态调试成功，策略正常工作")
    else:
        print("❌ 策略状态调试失败，需要进一步检查")
        print("\n🔧 下一步行动:")
        print("1. 检查服务器日志中的策略初始化信息")
        print("2. 确认策略的 is_running() 方法返回 true")
        print("3. 确认策略的 on_market_data() 方法被正确调用")
        print("4. 检查策略是否生成了订单")
        print("5. 检查生成的订单是否通过了风险检查")
