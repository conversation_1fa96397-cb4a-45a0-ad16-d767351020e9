//! V2风控数据仓库接口
//!
//! 定义统一的数据访问接口，支持批量查询优化和缓存

use async_trait::async_trait;
use sigmax_core::SigmaXResult;
use super::super::core::RiskResult;
use super::CacheManager;
use rust_decimal::Decimal;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use uuid::Uuid;

/// 交易限制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingLimits {
    /// 最大订单价值
    pub max_order_value: Decimal,
    /// 最大持仓比例
    pub max_position_ratio: Decimal,
    /// 最大日损失比例
    pub max_daily_loss: Decimal,
}

/// 风控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskConfig {
    /// 配置键
    pub key: String,
    /// 配置值
    pub value: serde_json::Value,
    /// 配置描述
    pub description: Option<String>,
}

/// 批量查询请求
#[derive(Debug, Clone)]
pub struct BatchQueryRequest<T> {
    pub keys: Vec<T>,
    pub cache_ttl: Option<Duration>,
}

/// 批量查询结果
#[derive(Debug, Clone)]
pub struct BatchQueryResult<K, V> {
    pub results: HashMap<K, V>,
    pub cache_hits: usize,
    pub cache_misses: usize,
    pub query_duration: Duration,
}

/// 查询统计信息
#[derive(Debug, Default, Clone)]
pub struct QueryStats {
    pub total_queries: u64,
    pub cache_hits: u64,
    pub cache_misses: u64,
    pub avg_query_time: Duration,
    pub batch_queries: u64,
    pub single_queries: u64,
}

/// 风控数据仓库特征
#[async_trait]
pub trait RiskRepository: Send + Sync {
    /// 获取交易限制
    async fn get_trading_limits(&self, strategy_id: &str) -> SigmaXResult<TradingLimits>;

    /// 批量获取交易限制
    async fn get_trading_limits_batch(
        &self,
        request: BatchQueryRequest<String>
    ) -> SigmaXResult<BatchQueryResult<String, TradingLimits>>;

    /// 获取风控配置
    async fn get_risk_config(&self, key: &str) -> SigmaXResult<Option<RiskConfig>>;

    /// 批量获取风控配置
    async fn get_risk_configs_batch(
        &self,
        request: BatchQueryRequest<String>
    ) -> SigmaXResult<BatchQueryResult<String, RiskConfig>>;

    /// 保存风控检查结果
    async fn save_risk_check_result(&self, result: &RiskResult) -> SigmaXResult<()>;

    /// 批量保存风控检查结果
    async fn save_risk_check_results_batch(&self, results: &[RiskResult]) -> SigmaXResult<()>;

    /// 获取查询统计信息
    async fn get_query_stats(&self) -> SigmaXResult<QueryStats>;

    /// 重置查询统计信息
    async fn reset_query_stats(&self) -> SigmaXResult<()>;

    /// 预热缓存
    async fn warm_cache(&self, keys: Vec<String>) -> SigmaXResult<()>;

    /// 清空缓存
    async fn clear_cache(&self) -> SigmaXResult<()>;
}