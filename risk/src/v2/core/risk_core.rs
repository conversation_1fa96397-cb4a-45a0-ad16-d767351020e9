//! V2风控系统微内核引擎
//!
//! 设计原则：
//! - 轻量级：核心逻辑控制在300行以内
//! - 高性能：并行执行规则，最小化延迟
//! - 可扩展：支持运行时添加/移除规则

use super::types::*;
use crate::v2::rules::RiskRule;
use crate::v2::data::RiskRepository;
use crate::v2::events::{RiskEventBus, RiskEvent};
use sigmax_core::SigmaXResult;
use std::sync::Arc;
use tokio::sync::RwLock;
use std::time::Instant;
use tracing::{debug, info, warn};
use uuid::Uuid;

/// V2微内核风控引擎
pub struct RiskCore {
    /// 已注册的风控规则
    rules: Arc<RwLock<Vec<Box<dyn RiskRule>>>>,
    /// 数据访问层
    repository: Arc<dyn RiskRepository>,
    /// 事件总线
    event_bus: Option<Arc<RiskEventBus>>,
    /// 执行统计
    stats: Arc<RwLock<ExecutionStats>>,
}

/// 执行统计信息
#[derive(Debug, Default, Clone)]
pub struct ExecutionStats {
    pub total_checks: u64,
    pub total_duration_us: u64,
    pub cache_hits: u64,
    pub cache_misses: u64,
    pub failed_checks: u64,
}

impl RiskCore {
    /// 创建新的风控核心实例
    pub fn new(
        repository: Arc<dyn RiskRepository>,
        event_bus: Option<Arc<RiskEventBus>>,
    ) -> Self {
        Self {
            rules: Arc::new(RwLock::new(Vec::new())),
            repository,
            event_bus,
            stats: Arc::new(RwLock::new(ExecutionStats::default())),
        }
    }

    /// 核心风控检查方法 - 系统唯一入口
    pub async fn check_risk(&self, request: RiskRequest) -> SigmaXResult<RiskResult> {
        let start_time = Instant::now();
        let request_id = request.request_id;
        
        debug!("开始风控检查: request_id={}", request_id);

        // 1. 获取规则列表
        let rules = self.rules.read().await;
        if rules.is_empty() {
            warn!("没有可用的风控规则");
            let total_duration = start_time.elapsed();
            // 即使没有规则，也要更新统计信息
            self.update_stats_with_result(total_duration, true).await;
            return Ok(RiskResult::pass(request_id));
        }

        // 2. 并行执行所有规则
        let rule_results = self.execute_rules_parallel(&rules, &request).await?;

        // 3. 聚合结果
        let mut violations = Vec::new();
        let mut max_risk_level = RiskLevel::None;
        let mut rule_durations = std::collections::HashMap::new();

        for (rule_name, result) in rule_results {
            match result {
                Ok(rule_result) => {
                    if let Some(violation) = rule_result.violation {
                        violations.push(violation);
                    }
                    if rule_result.risk_level > max_risk_level {
                        max_risk_level = rule_result.risk_level;
                    }
                    rule_durations.insert(rule_name, rule_result.duration_us);
                }
                Err(e) => {
                    warn!("规则执行失败: {} - {}", rule_name, e);
                    // 规则执行失败时，采用保守策略，视为高风险
                    max_risk_level = RiskLevel::High;
                    violations.push(RuleViolation {
                        rule_name: rule_name.clone(),
                        violation_type: ViolationType::Custom("规则执行失败".to_string()),
                        message: format!("规则 {} 执行失败: {}", rule_name, e),
                        current_value: None,
                        threshold_value: None,
                        suggested_action: Some("请检查规则配置或联系管理员".to_string()),
                    });
                }
            }
        }

        // 4. 构建最终结果
        let total_duration = start_time.elapsed();
        let passed = violations.is_empty();
        
        let result = RiskResult {
            request_id,
            passed,
            risk_level: max_risk_level.clone(),
            violations,
            metrics: PerformanceMetrics {
                total_duration_us: total_duration.as_micros() as u64,
                rule_durations,
                db_query_duration_us: 0, // TODO: 从repository获取
                cache_hit_rate: 0.0, // TODO: 从缓存获取
                rules_executed: rules.len(),
            },
            suggestions: self.generate_suggestions(&max_risk_level),
            timestamp: chrono::Utc::now(),
        };

        // 5. 更新统计信息
        self.update_stats_with_result(total_duration, passed).await;

        // 6. 发送异步事件
        self.emit_risk_event(&result).await;

        debug!(
            "风控检查完成: request_id={}, passed={}, duration={}μs", 
            request_id, passed, total_duration.as_micros()
        );

        Ok(result)
    }

    /// 添加规则
    pub async fn add_rule(&self, rule: Box<dyn RiskRule>) -> SigmaXResult<()> {
        let mut rules = self.rules.write().await;
        info!("添加风控规则: {}", rule.name());
        rules.push(rule);
        Ok(())
    }

    /// 移除规则
    pub async fn remove_rule(&self, rule_name: &str) -> SigmaXResult<bool> {
        let mut rules = self.rules.write().await;
        let initial_len = rules.len();
        rules.retain(|rule| rule.name() != rule_name);
        let removed = rules.len() < initial_len;
        
        if removed {
            info!("移除风控规则: {}", rule_name);
        }
        
        Ok(removed)
    }

    /// 获取规则列表
    pub async fn list_rules(&self) -> Vec<String> {
        let rules = self.rules.read().await;
        rules.iter().map(|rule| rule.name().to_string()).collect()
    }

    /// 获取执行统计信息
    pub async fn get_execution_stats(&self) -> ExecutionStats {
        let stats = self.stats.read().await;
        stats.clone()
    }
}

// 为了编译通过，先提供简化的实现，稍后完善
impl RiskCore {
    async fn execute_rules_parallel(
        &self,
        _rules: &[Box<dyn RiskRule>],
        _request: &RiskRequest,
    ) -> SigmaXResult<Vec<(String, Result<RuleExecutionResult, Box<dyn std::error::Error + Send>>)>> {
        // TODO: 实现并行规则执行
        Ok(Vec::new())
    }

    fn generate_suggestions(&self, risk_level: &RiskLevel) -> Vec<String> {
        match risk_level {
            RiskLevel::None => vec!["风险检查通过，可以继续交易".to_string()],
            RiskLevel::Low => vec!["发现轻微风险，建议关注市场变化".to_string()],
            RiskLevel::Medium => vec![
                "发现中等风险，建议减少仓位或等待更好时机".to_string(),
            ],
            RiskLevel::High => vec![
                "发现高风险，强烈建议暂停交易".to_string(),
            ],
            RiskLevel::Critical => vec![
                "发现极高风险，交易已被阻断".to_string(),
            ],
        }
    }

    async fn update_stats(&self, duration: std::time::Duration) {
        let mut stats = self.stats.write().await;
        stats.total_checks += 1;
        stats.total_duration_us += duration.as_micros() as u64;
    }

    async fn update_stats_with_result(&self, duration: std::time::Duration, passed: bool) {
        let mut stats = self.stats.write().await;
        stats.total_checks += 1;
        stats.total_duration_us += duration.as_micros() as u64;
        if !passed {
            stats.failed_checks += 1;
        }
    }

    async fn emit_risk_event(&self, result: &RiskResult) {
        if let Some(event_bus) = &self.event_bus {
            if !result.passed {
                let event = RiskEvent::ViolationDetected {
                    request_id: result.request_id,
                    risk_level: result.risk_level.clone(),
                    violations: result.violations.clone(),
                    timestamp: result.timestamp,
                };
                
                if let Err(e) = event_bus.emit(event).await {
                    warn!("发送风控事件失败: {}", e);
                }
            }
        }
    }
}

/// 单个规则的执行结果
struct RuleExecutionResult {
    violation: Option<RuleViolation>,
    risk_level: RiskLevel,
    duration_us: u64,
}

// 兼容接口实现
use sigmax_core::{Order, Balance};

impl RiskCore {
    /// 检查订单风险（兼容接口）
    pub async fn check_order_risk(&self, order: &Order, strategy_type: Option<String>) -> SigmaXResult<bool> {
        let request = RiskRequest::from_order(order.clone(), strategy_type);
        let result = self.check_risk(request).await?;
        Ok(result.passed)
    }

    /// 检查持仓风险（兼容接口）
    pub async fn check_position_risk(&self, balances: &[Balance], strategy_type: Option<String>) -> SigmaXResult<bool> {
        let request = RiskRequest::from_balances(balances.to_vec(), strategy_type);
        let result = self.check_risk(request).await?;
        Ok(result.passed)
    }
}
