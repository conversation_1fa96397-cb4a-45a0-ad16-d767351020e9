//! 自动响应执行器
//! 
//! 负责执行风险响应动作并记录执行结果

use crate::alert_system::{RiskAlert, AlertSeverity};
use crate::response_actions::{AutoResponseAction, LogLevel};
use crate::response_interfaces::{StrategyController, OrderManager, NotificationSender};
use crate::response_rules::AutoResponseRule;
use sigmax_core::{SigmaXResult, SigmaXError};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use tokio::sync::{mpsc, RwLock};
use std::sync::Arc;
use std::collections::HashMap;
use tracing::{info, warn, error, debug};

/// 响应执行记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseExecutionRecord {
    /// 记录ID
    pub record_id: Uuid,
    /// 规则ID
    pub rule_id: Uuid,
    /// 预警ID
    pub alert_id: Uuid,
    /// 执行的动作
    pub executed_actions: Vec<AutoResponseAction>,
    /// 执行状态
    pub execution_status: ExecutionStatus,
    /// 执行时间
    pub executed_at: DateTime<Utc>,
    /// 执行结果
    pub execution_results: Vec<ActionResult>,
    /// 错误信息
    pub error_message: Option<String>,
}

/// 执行状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ExecutionStatus {
    /// 执行中
    Executing,
    /// 执行成功
    Success,
    /// 部分成功
    PartialSuccess,
    /// 执行失败
    Failed,
}

/// 动作执行结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActionResult {
    /// 动作
    pub action: AutoResponseAction,
    /// 是否成功
    pub success: bool,
    /// 结果消息
    pub message: String,
    /// 执行时间
    pub executed_at: DateTime<Utc>,
}

/// 响应执行统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseStatistics {
    /// 总执行次数
    pub total_executions: u64,
    /// 成功执行次数
    pub successful_executions: u64,
    /// 失败执行次数
    pub failed_executions: u64,
    /// 部分成功执行次数
    pub partial_success_executions: u64,
    /// 最后执行时间
    pub last_execution_time: Option<DateTime<Utc>>,
    /// 按动作类型分组的统计
    pub action_type_stats: HashMap<String, ActionTypeStats>,
}

/// 动作类型统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActionTypeStats {
    /// 执行次数
    pub execution_count: u64,
    /// 成功次数
    pub success_count: u64,
    /// 失败次数
    pub failure_count: u64,
    /// 平均执行时间（毫秒）
    pub avg_execution_time_ms: f64,
}

/// 响应执行器
pub struct ResponseExecutor {
    /// 执行记录
    execution_records: Arc<RwLock<Vec<ResponseExecutionRecord>>>,
    /// 策略控制器
    strategy_controller: Option<Arc<dyn StrategyController>>,
    /// 订单管理器
    order_manager: Option<Arc<dyn OrderManager>>,
    /// 通知发送器
    notification_sender: Option<Arc<dyn NotificationSender>>,
    /// 执行统计
    statistics: Arc<RwLock<ResponseStatistics>>,
}

impl ResponseExecutor {
    /// 创建新的响应执行器
    pub fn new() -> Self {
        Self {
            execution_records: Arc::new(RwLock::new(Vec::new())),
            strategy_controller: None,
            order_manager: None,
            notification_sender: None,
            statistics: Arc::new(RwLock::new(ResponseStatistics::new())),
        }
    }

    /// 设置策略控制器
    pub fn set_strategy_controller(&mut self, controller: Arc<dyn StrategyController>) {
        self.strategy_controller = Some(controller);
    }

    /// 设置订单管理器
    pub fn set_order_manager(&mut self, manager: Arc<dyn OrderManager>) {
        self.order_manager = Some(manager);
    }

    /// 设置通知发送器
    pub fn set_notification_sender(&mut self, sender: Arc<dyn NotificationSender>) {
        self.notification_sender = Some(sender);
    }

    /// 执行响应动作
    pub async fn execute_response(
        &self,
        rule: &AutoResponseRule,
        alert: &RiskAlert,
    ) -> SigmaXResult<ResponseExecutionRecord> {
        let record_id = Uuid::new_v4();
        
        let mut record = ResponseExecutionRecord {
            record_id,
            rule_id: rule.rule_id,
            alert_id: alert.alert_id,
            executed_actions: rule.response_actions.clone(),
            execution_status: ExecutionStatus::Executing,
            executed_at: Utc::now(),
            execution_results: Vec::new(),
            error_message: None,
        };

        info!("开始执行响应规则: {} ({})", rule.name, rule.rule_id);

        // 执行所有动作
        let execution_result = self.execute_actions(&rule.response_actions, &mut record).await;

        // 更新执行状态
        match execution_result {
            Ok(_) => {
                let success_count = record.execution_results.iter().filter(|r| r.success).count();
                let total_count = record.execution_results.len();
                
                if success_count == total_count {
                    record.execution_status = ExecutionStatus::Success;
                } else if success_count > 0 {
                    record.execution_status = ExecutionStatus::PartialSuccess;
                } else {
                    record.execution_status = ExecutionStatus::Failed;
                }
            }
            Err(e) => {
                record.execution_status = ExecutionStatus::Failed;
                record.error_message = Some(e.to_string());
                error!("执行响应动作失败: {}", e);
            }
        }

        // 保存执行记录
        {
            let mut records = self.execution_records.write().await;
            records.push(record.clone());
        }

        // 更新统计信息
        self.update_statistics(&record).await;

        info!("响应规则执行完成: {} - 状态: {:?}", rule.name, record.execution_status);

        Ok(record)
    }

    /// 执行动作列表
    async fn execute_actions(
        &self,
        actions: &[AutoResponseAction],
        record: &mut ResponseExecutionRecord,
    ) -> SigmaXResult<()> {
        for action in actions {
            let start_time = std::time::Instant::now();
            
            let result = self.execute_single_action(action).await;
            
            let execution_time = start_time.elapsed();
            
            let action_result = ActionResult {
                action: action.clone(),
                success: result.is_ok(),
                message: match &result {
                    Ok(msg) => msg.clone(),
                    Err(e) => e.to_string(),
                },
                executed_at: Utc::now(),
            };

            record.execution_results.push(action_result);

            if let Err(e) = result {
                warn!("动作执行失败: {} - {}", action.action_name(), e);
            } else {
                debug!("动作执行成功: {} (耗时: {:?})", action.action_name(), execution_time);
            }
        }

        Ok(())
    }

    /// 执行单个动作
    async fn execute_single_action(&self, action: &AutoResponseAction) -> SigmaXResult<String> {
        match action {
            AutoResponseAction::PauseStrategy { strategy_id, duration_minutes } => {
                if let (Some(controller), Some(strategy_id)) = (&self.strategy_controller, strategy_id) {
                    controller.pause_strategy(*strategy_id, *duration_minutes).await?;
                    Ok(format!("策略 {} 已暂停", strategy_id))
                } else {
                    Err(SigmaXError::InvalidOperation("策略控制器或策略ID未设置".to_string()))
                }
            }

            AutoResponseAction::StopStrategy { strategy_id } => {
                if let (Some(controller), Some(strategy_id)) = (&self.strategy_controller, strategy_id) {
                    controller.stop_strategy(*strategy_id).await?;
                    Ok(format!("策略 {} 已停止", strategy_id))
                } else {
                    Err(SigmaXError::InvalidOperation("策略控制器或策略ID未设置".to_string()))
                }
            }

            AutoResponseAction::CancelOrders { strategy_id, trading_pair, order_side } => {
                if let Some(manager) = &self.order_manager {
                    let cancelled_orders = manager.cancel_orders(*strategy_id, trading_pair.clone(), *order_side).await?;
                    Ok(format!("已取消 {} 个订单", cancelled_orders.len()))
                } else {
                    Err(SigmaXError::InvalidOperation("订单管理器未设置".to_string()))
                }
            }

            AutoResponseAction::SendNotification { message, severity } => {
                if let Some(sender) = &self.notification_sender {
                    match severity {
                        AlertSeverity::Emergency => sender.send_emergency_notification(message).await?,
                        AlertSeverity::Critical => sender.send_warning_notification(message).await?,
                        AlertSeverity::Warning => sender.send_warning_notification(message).await?,
                        AlertSeverity::Info => sender.send_info_notification(message).await?,
                    }
                    Ok("通知已发送".to_string())
                } else {
                    // 如果没有通知发送器，使用日志记录
                    match severity {
                        AlertSeverity::Emergency => error!("🚨 紧急通知: {}", message),
                        AlertSeverity::Critical => error!("⚠️ 严重通知: {}", message),
                        AlertSeverity::Warning => warn!("⚠️ 警告通知: {}", message),
                        AlertSeverity::Info => info!("ℹ️ 信息通知: {}", message),
                    }
                    Ok("通知已记录到日志".to_string())
                }
            }

            AutoResponseAction::LogEvent { message, level } => {
                match level {
                    LogLevel::Debug => debug!("{}", message),
                    LogLevel::Info => info!("{}", message),
                    LogLevel::Warn => warn!("{}", message),
                    LogLevel::Error => error!("{}", message),
                }
                Ok("日志已记录".to_string())
            }

            AutoResponseAction::PartialClose { strategy_id, close_percentage, trading_pair } => {
                if let Some(manager) = &self.order_manager {
                    let close_orders = manager.create_close_orders(*strategy_id, trading_pair.clone(), *close_percentage).await?;
                    Ok(format!("已创建 {} 个平仓订单，平仓比例 {}%", close_orders.len(), close_percentage))
                } else {
                    Err(SigmaXError::InvalidOperation("订单管理器未设置".to_string()))
                }
            }

            AutoResponseAction::FullClose { strategy_id, trading_pair } => {
                if let Some(manager) = &self.order_manager {
                    let close_orders = manager.create_close_orders(*strategy_id, trading_pair.clone(), 100.0).await?;
                    Ok(format!("已创建 {} 个全部平仓订单", close_orders.len()))
                } else {
                    Err(SigmaXError::InvalidOperation("订单管理器未设置".to_string()))
                }
            }

            AutoResponseAction::ReducePositionSize { strategy_id, reduction_percentage } => {
                if let (Some(controller), Some(strategy_id)) = (&self.strategy_controller, strategy_id) {
                    controller.reduce_position_size(*strategy_id, *reduction_percentage).await?;
                    Ok(format!("策略 {} 仓位已减少 {}%", strategy_id, reduction_percentage))
                } else {
                    Err(SigmaXError::InvalidOperation("策略控制器或策略ID未设置".to_string()))
                }
            }

            AutoResponseAction::AdjustRiskParameters { strategy_id, parameter_adjustments } => {
                if let (Some(controller), Some(strategy_id)) = (&self.strategy_controller, strategy_id) {
                    controller.adjust_risk_parameters(*strategy_id, parameter_adjustments.clone()).await?;
                    Ok(format!("策略 {} 风险参数已调整", strategy_id))
                } else {
                    Err(SigmaXError::InvalidOperation("策略控制器或策略ID未设置".to_string()))
                }
            }

            AutoResponseAction::IncreaseLiquidityBuffer { buffer_percentage } => {
                // 这里需要实现流动性缓冲调整逻辑
                info!("增加流动性缓冲至 {}%", buffer_percentage);
                Ok(format!("流动性缓冲已增加至 {}%", buffer_percentage))
            }

            AutoResponseAction::CustomAction { action_name, parameters } => {
                info!("执行自定义动作: {} - 参数: {:?}", action_name, parameters);
                Ok(format!("自定义动作 {} 已执行", action_name))
            }
        }
    }

    /// 更新统计信息
    async fn update_statistics(&self, record: &ResponseExecutionRecord) {
        let mut stats = self.statistics.write().await;
        
        stats.total_executions += 1;
        stats.last_execution_time = Some(record.executed_at);

        match record.execution_status {
            ExecutionStatus::Success => stats.successful_executions += 1,
            ExecutionStatus::Failed => stats.failed_executions += 1,
            ExecutionStatus::PartialSuccess => stats.partial_success_executions += 1,
            _ => {}
        }

        // 更新动作类型统计
        for result in &record.execution_results {
            let action_name = result.action.action_name().to_string();
            let action_stats = stats.action_type_stats.entry(action_name).or_insert_with(|| ActionTypeStats {
                execution_count: 0,
                success_count: 0,
                failure_count: 0,
                avg_execution_time_ms: 0.0,
            });

            action_stats.execution_count += 1;
            if result.success {
                action_stats.success_count += 1;
            } else {
                action_stats.failure_count += 1;
            }
        }
    }

    /// 获取执行统计
    pub async fn get_statistics(&self) -> ResponseStatistics {
        self.statistics.read().await.clone()
    }

    /// 获取执行记录
    pub async fn get_execution_records(&self) -> Vec<ResponseExecutionRecord> {
        self.execution_records.read().await.clone()
    }

    /// 清理旧的执行记录
    pub async fn cleanup_old_records(&self, max_records: usize) {
        let mut records = self.execution_records.write().await;
        if records.len() > max_records {
            records.sort_by(|a, b| b.executed_at.cmp(&a.executed_at));
            records.truncate(max_records);
        }
    }
}

impl ResponseStatistics {
    /// 创建新的统计信息
    pub fn new() -> Self {
        Self {
            total_executions: 0,
            successful_executions: 0,
            failed_executions: 0,
            partial_success_executions: 0,
            last_execution_time: None,
            action_type_stats: HashMap::new(),
        }
    }

    /// 计算成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_executions == 0 {
            0.0
        } else {
            (self.successful_executions as f64 / self.total_executions as f64) * 100.0
        }
    }
}

impl Default for ResponseExecutor {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for ResponseStatistics {
    fn default() -> Self {
        Self::new()
    }
}
