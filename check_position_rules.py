#!/usr/bin/env python3
"""
查询持仓相关的风险规则
找到 USDT 持仓占比 25% 限制的具体配置
"""

import requests
import json

def check_position_rules():
    """查询持仓相关的风险规则"""
    print("🔍 查询持仓相关的风险规则")
    print("🎯 找到 USDT 持仓占比 25% 限制的具体配置")
    print("=" * 70)
    
    try:
        # 查询风险规则
        response = requests.get('http://127.0.0.1:8080/api/risk/rules')
        if response.status_code == 200:
            rules = response.json()['data']
            print(f"📋 找到 {len(rules)} 条风险规则")
            
            position_rules = []
            for rule in rules:
                # 查找持仓相关的规则
                if ('持仓' in rule['name'] or 
                    'position' in rule['rule_type'] or 
                    'position' in rule['name'].lower()):
                    position_rules.append(rule)
            
            if position_rules:
                print(f"\n🎯 找到 {len(position_rules)} 条持仓相关规则:")
                print("=" * 70)
                
                for i, rule in enumerate(position_rules, 1):
                    print(f"\n📌 规则 {i}: {rule['name']}")
                    print(f"   ID: {rule['id']}")
                    print(f"   类型: {rule['rule_type']}")
                    print(f"   分类: {rule['category']}")
                    print(f"   启用: {rule['enabled']}")
                    print(f"   优先级: {rule['priority']}")
                    
                    # 重点显示参数
                    print(f"   📋 参数配置:")
                    params = rule['parameters']
                    for key, value in params.items():
                        if 'percent' in key.lower() or 'position' in key.lower():
                            print(f"      🔥 {key}: {value}")
                        else:
                            print(f"         {key}: {value}")
                    
                    print(f"   📝 描述: {rule['description']}")
                    print("-" * 50)
                
                # 查找具体的 25% 限制
                print("\n🔍 查找 25% 持仓限制:")
                found_25_percent = False
                for rule in position_rules:
                    params = rule['parameters']
                    for key, value in params.items():
                        if isinstance(value, (int, float)) and value == 25.0:
                            print(f"✅ 找到 25% 限制!")
                            print(f"   规则: {rule['name']}")
                            print(f"   参数: {key} = {value}")
                            print(f"   表: unified_risk_rules")
                            print(f"   ID: {rule['id']}")
                            found_25_percent = True
                
                if not found_25_percent:
                    print("⚠️ 未找到 25% 的具体限制参数")
                    
            else:
                print("⚠️ 未找到持仓相关的风险规则")
                
        else:
            print(f"❌ API调用失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def show_table_info():
    """显示相关表的信息"""
    print("\n" + "=" * 70)
    print("📊 相关数据库表信息")
    print("=" * 70)
    
    print("1. 主要表: unified_risk_rules")
    print("   - 存储所有统一风险规则")
    print("   - 字段: parameters (JSONB) - 存储规则参数")
    print("   - 示例: max_position_percent: 25.0")
    
    print("\n2. 备用表: risk_config")
    print("   - 存储风险配置")
    print("   - 字段: risk_parameters (JSONB)")
    print("   - 示例: position_size_limit_percent: 25")
    
    print("\n3. 旧表: risk_rules")
    print("   - 旧的风险规则表")
    print("   - 可能包含一些历史配置")

def main():
    """主函数"""
    check_position_rules()
    show_table_info()
    
    print("\n💡 总结:")
    print("   USDT 持仓占比 25% 的限制参数存储在:")
    print("   📋 表: unified_risk_rules")
    print("   📋 字段: parameters -> max_position_percent")
    print("   📋 规则名称: 单个持仓大小限制")
    print("   📋 规则类型: single_position_limit")

if __name__ == "__main__":
    main()
