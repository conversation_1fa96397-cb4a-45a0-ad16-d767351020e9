//! 实时风险监控系统
//! 
//! 提供实时风险监控、预警和自动响应功能

use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::{mpsc, RwLock, broadcast};
use tokio::time::{Duration, interval};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use tracing::{debug, warn, info, error};

use sigmax_core::SigmaXResult;
use crate::unified_interface::{UnifiedRiskInterface, RiskContext};
use crate::data_cache::{RiskDataCache, CacheManager};
use crate::core::types::{RiskMetrics, RiskAlert, RiskLevel};

/// 风险阈值定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskThreshold {
    /// 阈值名称
    pub name: String,
    /// 阈值描述
    pub description: String,
    /// 指标类型
    pub metric_type: String,
    /// 阈值
    pub threshold: f64,
    /// 比较操作符
    pub operator: ThresholdOperator,
    /// 严重程度
    pub severity: RiskLevel,
    /// 是否启用
    pub enabled: bool,
    /// 冷却时间（秒）
    pub cooldown_seconds: u64,
    /// 最后触发时间
    pub last_triggered_at: Option<DateTime<Utc>>,
}

/// 阈值比较操作符
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThresholdOperator {
    /// 大于
    GreaterThan,
    /// 大于等于
    GreaterThanOrEqual,
    /// 小于
    LessThan,
    /// 小于等于
    LessThanOrEqual,
    /// 等于
    Equal,
    /// 不等于
    NotEqual,
}

impl ThresholdOperator {
    /// 比较值
    pub fn compare(&self, value: f64, threshold: f64) -> bool {
        match self {
            ThresholdOperator::GreaterThan => value > threshold,
            ThresholdOperator::GreaterThanOrEqual => value >= threshold,
            ThresholdOperator::LessThan => value < threshold,
            ThresholdOperator::LessThanOrEqual => value <= threshold,
            ThresholdOperator::Equal => (value - threshold).abs() < f64::EPSILON,
            ThresholdOperator::NotEqual => (value - threshold).abs() >= f64::EPSILON,
        }
    }
}

/// 风险监控事件
#[derive(Debug, Clone, Serialize)]
pub enum RiskMonitorEvent {
    /// 阈值触发
    ThresholdTriggered {
        threshold_name: String,
        current_value: f64,
        threshold_value: f64,
        severity: RiskLevel,
        timestamp: DateTime<Utc>,
    },
    /// 风险预警
    RiskAlert {
        alert: RiskAlert,
        timestamp: DateTime<Utc>,
    },
    /// 监控状态变更
    MonitoringStatusChanged {
        is_monitoring: bool,
        timestamp: DateTime<Utc>,
    },
    /// 指标更新
    MetricsUpdated {
        metrics: RiskMetrics,
        timestamp: DateTime<Utc>,
    },
}

/// 风险监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMonitorConfig {
    /// 监控间隔（秒）
    pub monitor_interval_seconds: u64,
    /// 是否启用自动响应
    pub enable_auto_response: bool,
    /// 最大事件队列大小
    pub max_event_queue_size: usize,
    /// 阈值配置
    pub thresholds: Vec<RiskThreshold>,
}

impl Default for RiskMonitorConfig {
    fn default() -> Self {
        Self {
            monitor_interval_seconds: 30,
            enable_auto_response: false,
            max_event_queue_size: 1000,
            thresholds: Self::default_thresholds(),
        }
    }
}

impl RiskMonitorConfig {
    /// 默认阈值配置
    fn default_thresholds() -> Vec<RiskThreshold> {
        vec![
            RiskThreshold {
                name: "max_drawdown".to_string(),
                description: "最大回撤阈值".to_string(),
                metric_type: "max_drawdown".to_string(),
                threshold: 0.15, // 15%
                operator: ThresholdOperator::GreaterThan,
                severity: RiskLevel::High,
                enabled: true,
                cooldown_seconds: 300, // 5分钟冷却
                last_triggered_at: None,
            },
            RiskThreshold {
                name: "volatility_spike".to_string(),
                description: "波动率异常阈值".to_string(),
                metric_type: "volatility".to_string(),
                threshold: 0.50, // 50%
                operator: ThresholdOperator::GreaterThan,
                severity: RiskLevel::Medium,
                enabled: true,
                cooldown_seconds: 180, // 3分钟冷却
                last_triggered_at: None,
            },
            RiskThreshold {
                name: "var_95_breach".to_string(),
                description: "VaR 95%突破阈值".to_string(),
                metric_type: "var_95".to_string(),
                threshold: 0.05, // 5%
                operator: ThresholdOperator::GreaterThan,
                severity: RiskLevel::Critical,
                enabled: true,
                cooldown_seconds: 600, // 10分钟冷却
                last_triggered_at: None,
            },
        ]
    }
}

/// 实时风险监控器
pub struct RealTimeRiskMonitor {
    /// 风控引擎
    engine: Arc<dyn UnifiedRiskInterface>,
    /// 缓存管理器
    cache_manager: Option<CacheManager>,
    /// 监控配置
    config: Arc<RwLock<RiskMonitorConfig>>,
    /// 阈值状态
    thresholds: Arc<RwLock<HashMap<String, RiskThreshold>>>,
    /// 事件发送器
    event_tx: broadcast::Sender<RiskMonitorEvent>,
    /// 监控状态
    is_monitoring: Arc<RwLock<bool>>,
    /// 最新指标
    latest_metrics: Arc<RwLock<Option<RiskMetrics>>>,
}

impl RealTimeRiskMonitor {
    /// 创建新的实时风险监控器
    pub fn new(
        engine: Arc<dyn UnifiedRiskInterface>,
        cache_manager: Option<CacheManager>,
        config: Option<RiskMonitorConfig>,
    ) -> Self {
        let config = config.unwrap_or_default();
        let (event_tx, _) = broadcast::channel(config.max_event_queue_size);
        
        // 初始化阈值状态
        let mut thresholds = HashMap::new();
        for threshold in &config.thresholds {
            thresholds.insert(threshold.name.clone(), threshold.clone());
        }
        
        Self {
            engine,
            cache_manager,
            config: Arc::new(RwLock::new(config)),
            thresholds: Arc::new(RwLock::new(thresholds)),
            event_tx,
            is_monitoring: Arc::new(RwLock::new(false)),
            latest_metrics: Arc::new(RwLock::new(None)),
        }
    }
    
    /// 启动监控
    pub async fn start(&self) -> SigmaXResult<()> {
        let mut is_monitoring = self.is_monitoring.write().await;
        if *is_monitoring {
            return Ok(());
        }
        
        *is_monitoring = true;
        info!("启动实时风险监控");
        
        // 发送状态变更事件
        let _ = self.event_tx.send(RiskMonitorEvent::MonitoringStatusChanged {
            is_monitoring: true,
            timestamp: Utc::now(),
        });
        
        // 启动监控任务
        self.spawn_monitoring_task().await;
        
        Ok(())
    }
    
    /// 停止监控
    pub async fn stop(&self) -> SigmaXResult<()> {
        let mut is_monitoring = self.is_monitoring.write().await;
        if !*is_monitoring {
            return Ok(());
        }
        
        *is_monitoring = false;
        info!("停止实时风险监控");
        
        // 发送状态变更事件
        let _ = self.event_tx.send(RiskMonitorEvent::MonitoringStatusChanged {
            is_monitoring: false,
            timestamp: Utc::now(),
        });
        
        Ok(())
    }
    
    /// 检查是否正在监控
    pub async fn is_monitoring(&self) -> bool {
        *self.is_monitoring.read().await
    }
    
    /// 订阅监控事件
    pub fn subscribe_events(&self) -> broadcast::Receiver<RiskMonitorEvent> {
        self.event_tx.subscribe()
    }
    
    /// 获取最新风险指标
    pub async fn get_latest_metrics(&self) -> Option<RiskMetrics> {
        self.latest_metrics.read().await.clone()
    }
    
    /// 更新监控配置
    pub async fn update_config(&self, new_config: RiskMonitorConfig) -> SigmaXResult<()> {
        let mut config = self.config.write().await;
        *config = new_config.clone();
        
        // 更新阈值状态
        let mut thresholds = self.thresholds.write().await;
        thresholds.clear();
        for threshold in &new_config.thresholds {
            thresholds.insert(threshold.name.clone(), threshold.clone());
        }
        
        info!("更新风险监控配置");
        Ok(())
    }
    
    /// 添加阈值
    pub async fn add_threshold(&self, threshold: RiskThreshold) -> SigmaXResult<()> {
        let mut thresholds = self.thresholds.write().await;
        thresholds.insert(threshold.name.clone(), threshold.clone());
        
        // 更新配置
        let mut config = self.config.write().await;
        config.thresholds.push(threshold.clone());

        info!("添加风险阈值: {}", threshold.name);
        Ok(())
    }
    
    /// 移除阈值
    pub async fn remove_threshold(&self, threshold_name: &str) -> SigmaXResult<()> {
        let mut thresholds = self.thresholds.write().await;
        thresholds.remove(threshold_name);
        
        // 更新配置
        let mut config = self.config.write().await;
        config.thresholds.retain(|t| t.name != threshold_name);
        
        info!("移除风险阈值: {}", threshold_name);
        Ok(())
    }
    
    /// 启动监控任务
    async fn spawn_monitoring_task(&self) {
        let engine = self.engine.clone();
        let cache_manager = self.cache_manager.clone();
        let config = self.config.clone();
        let thresholds = self.thresholds.clone();
        let event_tx = self.event_tx.clone();
        let is_monitoring = self.is_monitoring.clone();
        let latest_metrics = self.latest_metrics.clone();
        
        tokio::spawn(async move {
            let mut interval_timer = {
                let config = config.read().await;
                interval(Duration::from_secs(config.monitor_interval_seconds))
            };
            
            loop {
                interval_timer.tick().await;
                
                // 检查是否仍在监控
                if !*is_monitoring.read().await {
                    break;
                }
                
                // 执行监控检查
                if let Err(e) = Self::perform_monitoring_check(
                    &engine,
                    &cache_manager,
                    &thresholds,
                    &event_tx,
                    &latest_metrics,
                ).await {
                    error!("风险监控检查失败: {}", e);
                }
            }
            
            info!("风险监控任务已退出");
        });
    }
    
    /// 执行监控检查
    async fn perform_monitoring_check(
        engine: &Arc<dyn UnifiedRiskInterface>,
        cache_manager: &Option<CacheManager>,
        thresholds: &Arc<RwLock<HashMap<String, RiskThreshold>>>,
        event_tx: &broadcast::Sender<RiskMonitorEvent>,
        latest_metrics: &Arc<RwLock<Option<RiskMetrics>>>,
    ) -> SigmaXResult<()> {
        debug!("执行风险监控检查");
        
        // 创建风险上下文
        let context = RiskContext::new();
        
        // 获取风险指标
        let metrics = match engine.get_risk_metrics(&context).await {
            Ok(metrics) => metrics,
            Err(e) => {
                warn!("获取风险指标失败: {}", e);
                return Ok(());
            }
        };
        
        // 更新最新指标
        {
            let mut latest = latest_metrics.write().await;
            *latest = Some(metrics.clone());
        }
        
        // 发送指标更新事件
        let _ = event_tx.send(RiskMonitorEvent::MetricsUpdated {
            metrics: metrics.clone(),
            timestamp: Utc::now(),
        });
        
        // 检查阈值
        let mut thresholds_guard = thresholds.write().await;
        let now = Utc::now();
        
        for (name, threshold) in thresholds_guard.iter_mut() {
            if !threshold.enabled {
                continue;
            }
            
            // 检查冷却时间
            if let Some(last_triggered) = threshold.last_triggered_at {
                let elapsed = now.signed_duration_since(last_triggered).num_seconds() as u64;
                if elapsed < threshold.cooldown_seconds {
                    continue;
                }
            }
            
            // 获取指标值
            let current_value = match threshold.metric_type.as_str() {
                "max_drawdown" => metrics.max_drawdown,
                "volatility" => metrics.volatility,
                "var_95" => metrics.var_95,
                "var_99" => metrics.var_99,
                "cvar_95" => metrics.cvar_95,
                "cvar_99" => metrics.cvar_99,
                "sharpe_ratio" => metrics.sharpe_ratio,
                _ => {
                    warn!("未知的指标类型: {}", threshold.metric_type);
                    continue;
                }
            };
            
            // 检查阈值
            if threshold.operator.compare(current_value, threshold.threshold) {
                // 更新最后触发时间
                threshold.last_triggered_at = Some(now);
                
                // 发送阈值触发事件
                let _ = event_tx.send(RiskMonitorEvent::ThresholdTriggered {
                    threshold_name: name.clone(),
                    current_value,
                    threshold_value: threshold.threshold,
                    severity: threshold.severity.clone(),
                    timestamp: now,
                });
                
                // 创建风险预警
                let alert = RiskAlert {
                    id: Uuid::new_v4(),
                    alert_type: format!("threshold_{}", name),
                    severity: threshold.severity.clone(),
                    message: format!(
                        "风险阈值触发: {} = {:.4}, 阈值 = {:.4}",
                        threshold.description,
                        current_value,
                        threshold.threshold
                    ),
                    data: {
                        let mut data = HashMap::new();
                        data.insert("threshold_name".to_string(), serde_json::Value::String(name.clone()));
                        data.insert("current_value".to_string(), serde_json::Value::Number(serde_json::Number::from_f64(current_value).unwrap()));
                        data.insert("threshold_value".to_string(), serde_json::Value::Number(serde_json::Number::from_f64(threshold.threshold).unwrap()));
                        data.insert("metric_type".to_string(), serde_json::Value::String(threshold.metric_type.clone()));
                        data
                    },
                    created_at: now,
                    resolved: false,
                };
                
                // 发送风险预警事件
                let _ = event_tx.send(RiskMonitorEvent::RiskAlert {
                    alert,
                    timestamp: now,
                });
                
                info!(
                    "风险阈值触发: {} = {:.4} (阈值: {:.4})",
                    name, current_value, threshold.threshold
                );
            }
        }
        
        Ok(())
    }
}

/// 风险监控管理器
pub struct RiskMonitorManager {
    /// 风险监控器
    monitor: Arc<RealTimeRiskMonitor>,
}

impl RiskMonitorManager {
    /// 创建新的风险监控管理器
    pub fn new(
        engine: Arc<dyn UnifiedRiskInterface>,
        cache_manager: Option<CacheManager>,
        config: Option<RiskMonitorConfig>,
    ) -> Self {
        let monitor = Arc::new(RealTimeRiskMonitor::new(engine, cache_manager, config));
        
        Self { monitor }
    }
    
    /// 获取监控器
    pub fn monitor(&self) -> &Arc<RealTimeRiskMonitor> {
        &self.monitor
    }
    
    /// 初始化监控管理器
    pub async fn initialize(&self) -> SigmaXResult<()> {
        info!("初始化风险监控管理器");
        Ok(())
    }
    
    /// 启动监控
    pub async fn start(&self) -> SigmaXResult<()> {
        self.monitor.start().await
    }
    
    /// 停止监控
    pub async fn stop(&self) -> SigmaXResult<()> {
        self.monitor.stop().await
    }
    
    /// 检查监控状态
    pub async fn is_monitoring(&self) -> bool {
        self.monitor.is_monitoring().await
    }
}