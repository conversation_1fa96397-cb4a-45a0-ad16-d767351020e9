//! 统一风控系统集成模块
//! 
//! 负责将新的统一风控系统与现有模块集成，保持向后兼容性

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, SigmaXError, Order, Balance, EngineRiskAdapter};
use std::sync::Arc;
use std::collections::HashMap;
use tracing::{info, warn, error};

use crate::risk::{
    RiskServiceFacade, FacadeConfig,
    BasicRiskEngine, RiskEngineConfig,
    RiskCacheService, RiskCacheServiceConfig,
    UnifiedRiskMetricsCollector, UnifiedMetricsConfig,
    MemoryConfigService,
    SqlxUnifiedRiskRepository, RepositoryConfig
};
use crate::risk::facade::{RiskCheckMode, RiskCheckContext};
use crate::backtest::BacktestRiskAdapter;
use crate::live::LiveTradingRiskAdapter;
use crate::webapi::WebApiRiskAdapter;
use crate::strategy::StrategyRiskAdapter;

/// 统一风控系统集成器
pub struct UnifiedRiskIntegration {
    /// 统一门面
    facade: Arc<RiskServiceFacade>,
    /// 回测适配器
    backtest_adapter: Arc<BacktestRiskAdapter>,
    /// 实盘适配器
    live_adapter: Arc<LiveTradingRiskAdapter>,
    /// WebAPI适配器
    webapi_adapter: Arc<WebApiRiskAdapter>,
    /// 策略适配器
    strategy_adapter: Arc<StrategyRiskAdapter>,
    /// 统一指标收集器
    metrics_collector: Arc<UnifiedRiskMetricsCollector>,
    /// 数据访问层
    repository: Arc<SqlxUnifiedRiskRepository>,
}

/// 集成配置
#[derive(Debug, Clone)]
pub struct IntegrationConfig {
    /// 启用回测适配器
    pub enable_backtest_adapter: bool,
    /// 启用实盘适配器
    pub enable_live_adapter: bool,
    /// 启用WebAPI适配器
    pub enable_webapi_adapter: bool,
    /// 启用策略适配器
    pub enable_strategy_adapter: bool,
    /// 启用统一指标收集
    pub enable_unified_metrics: bool,
    /// 启用数据持久化
    pub enable_data_persistence: bool,
    /// 向后兼容模式
    pub backward_compatibility_mode: bool,
}

impl Default for IntegrationConfig {
    fn default() -> Self {
        Self {
            enable_backtest_adapter: true,
            enable_live_adapter: true,
            enable_webapi_adapter: true,
            enable_strategy_adapter: true,
            enable_unified_metrics: true,
            enable_data_persistence: true,
            backward_compatibility_mode: true,
        }
    }
}

impl UnifiedRiskIntegration {
    /// 创建新的统一风控集成器
    pub async fn new(config: IntegrationConfig) -> SigmaXResult<Self> {
        info!("Creating UnifiedRiskIntegration with config: {:?}", config);
        
        // 1. 创建核心服务
        let risk_engine = Arc::new(BasicRiskEngine::new(RiskEngineConfig::default()).await?);
        let cache_service = Arc::new(RiskCacheService::new(RiskCacheServiceConfig::default()).await?);
        let config_service = Arc::new(MemoryConfigService::new());
        
        // 2. 创建统一指标收集器
        let metrics_collector = if config.enable_unified_metrics {
            Arc::new(UnifiedRiskMetricsCollector::new(UnifiedMetricsConfig::default()).await?)
        } else {
            // 使用基础指标收集器的包装
            Arc::new(UnifiedRiskMetricsCollector::new(UnifiedMetricsConfig {
                enable_prometheus: false,
                enable_alerting: false,
                ..Default::default()
            }).await?)
        };
        
        // 3. 创建数据访问层
        let repository = if config.enable_data_persistence {
            Arc::new(SqlxUnifiedRiskRepository::new(RepositoryConfig::default()).await?)
        } else {
            Arc::new(SqlxUnifiedRiskRepository::new(RepositoryConfig {
                enable_caching: false,
                ..Default::default()
            }).await?)
        };
        
        // 4. 创建统一门面
        let facade = Arc::new(RiskServiceFacade::new_with_services(
            FacadeConfig::default(),
            risk_engine.clone(),
            cache_service.clone(),
            metrics_collector.clone(),
            config_service.clone(),
        ).await?);
        
        // 5. 创建专用适配器
        let backtest_adapter = if config.enable_backtest_adapter {
            Arc::new(BacktestRiskAdapter::new(
                risk_engine.clone(),
                cache_service.clone(),
                metrics_collector.clone(),
                crate::backtest::BacktestAdapterConfig::default(),
            ).await?)
        } else {
            // 创建一个禁用的适配器
            Arc::new(BacktestRiskAdapter::new(
                risk_engine.clone(),
                cache_service.clone(),
                metrics_collector.clone(),
                crate::backtest::BacktestAdapterConfig {
                    enable_batch_cache: false,
                    high_throughput_mode: false,
                    ..Default::default()
                },
            ).await?)
        };
        
        let live_adapter = if config.enable_live_adapter {
            Arc::new(LiveTradingRiskAdapter::new(
                risk_engine.clone(),
                cache_service.clone(),
                metrics_collector.clone(),
                crate::live::LiveAdapterConfig::default(),
            ).await?)
        } else {
            Arc::new(LiveTradingRiskAdapter::new(
                risk_engine.clone(),
                cache_service.clone(),
                metrics_collector.clone(),
                crate::live::LiveAdapterConfig {
                    low_latency_mode: false,
                    preload_hot_data: false,
                    ..Default::default()
                },
            ).await?)
        };
        
        let webapi_adapter = if config.enable_webapi_adapter {
            Arc::new(WebApiRiskAdapter::new(
                risk_engine.clone(),
                cache_service.clone(),
                metrics_collector.clone(),
                crate::webapi::WebApiAdapterConfig::default(),
            ).await?)
        } else {
            Arc::new(WebApiRiskAdapter::new(
                risk_engine.clone(),
                cache_service.clone(),
                metrics_collector.clone(),
                crate::webapi::WebApiAdapterConfig {
                    detailed_results: false,
                    enable_audit_logging: false,
                    ..Default::default()
                },
            ).await?)
        };
        
        let strategy_adapter = if config.enable_strategy_adapter {
            Arc::new(StrategyRiskAdapter::new(
                risk_engine.clone(),
                cache_service.clone(),
                metrics_collector.clone(),
                crate::strategy::StrategyAdapterConfig::default(),
            ).await?)
        } else {
            Arc::new(StrategyRiskAdapter::new(
                risk_engine.clone(),
                cache_service.clone(),
                metrics_collector.clone(),
                crate::strategy::StrategyAdapterConfig {
                    enable_strategy_specific_checks: false,
                    enable_switch_evaluation: false,
                    ..Default::default()
                },
            ).await?)
        };
        
        // 6. 注册适配器到门面
        facade.register_adapter(RiskCheckMode::Backtest, backtest_adapter.clone()).await?;
        facade.register_adapter(RiskCheckMode::Live, live_adapter.clone()).await?;
        facade.register_adapter(RiskCheckMode::WebApi, webapi_adapter.clone()).await?;
        facade.register_adapter(RiskCheckMode::Strategy, strategy_adapter.clone()).await?;
        
        let integration = Self {
            facade,
            backtest_adapter,
            live_adapter,
            webapi_adapter,
            strategy_adapter,
            metrics_collector,
            repository,
        };
        
        info!("UnifiedRiskIntegration created successfully");
        Ok(integration)
    }
    
    /// 获取统一门面
    pub fn get_facade(&self) -> Arc<RiskServiceFacade> {
        self.facade.clone()
    }
    
    /// 获取回测适配器
    pub fn get_backtest_adapter(&self) -> Arc<BacktestRiskAdapter> {
        self.backtest_adapter.clone()
    }
    
    /// 获取实盘适配器
    pub fn get_live_adapter(&self) -> Arc<LiveTradingRiskAdapter> {
        self.live_adapter.clone()
    }
    
    /// 获取WebAPI适配器
    pub fn get_webapi_adapter(&self) -> Arc<WebApiRiskAdapter> {
        self.webapi_adapter.clone()
    }
    
    /// 获取策略适配器
    pub fn get_strategy_adapter(&self) -> Arc<StrategyRiskAdapter> {
        self.strategy_adapter.clone()
    }
    
    /// 获取统一指标收集器
    pub fn get_metrics_collector(&self) -> Arc<UnifiedRiskMetricsCollector> {
        self.metrics_collector.clone()
    }
    
    /// 获取数据访问层
    pub fn get_repository(&self) -> Arc<SqlxUnifiedRiskRepository> {
        self.repository.clone()
    }
    
    /// 向后兼容：获取传统风控检查接口
    pub async fn legacy_check_order_risk(&self, order: &Order) -> SigmaXResult<bool> {
        let context = RiskCheckContext {
            mode: RiskCheckMode::Live,
            ..Default::default()
        };
        
        let result = self.facade.check_order_risk(order, &context).await?;
        Ok(result.passed)
    }
    
    /// 向后兼容：获取传统持仓检查接口
    pub async fn legacy_check_position_risk(&self, balances: &[Balance]) -> SigmaXResult<bool> {
        let context = RiskCheckContext {
            mode: RiskCheckMode::Live,
            ..Default::default()
        };
        
        let result = self.facade.check_position_risk(balances, &context).await?;
        Ok(result.passed)
    }
    
    /// 健康检查
    pub async fn health_check(&self) -> SigmaXResult<IntegrationHealthStatus> {
        let mut status = IntegrationHealthStatus {
            overall_healthy: true,
            facade_healthy: false,
            adapters_healthy: HashMap::new(),
            metrics_healthy: false,
            repository_healthy: false,
            error_messages: Vec::new(),
        };
        
        // 检查门面健康状态
        match self.facade.get_facade_stats().await {
            Ok(_) => status.facade_healthy = true,
            Err(e) => {
                status.overall_healthy = false;
                status.error_messages.push(format!("Facade error: {}", e));
            }
        }
        
        // 检查适配器健康状态
        let adapters: Vec<(&str, Arc<dyn EngineRiskAdapter>)> = vec![
            ("backtest", self.backtest_adapter.clone() as Arc<dyn EngineRiskAdapter>),
            ("live", self.live_adapter.clone() as Arc<dyn EngineRiskAdapter>),
            ("webapi", self.webapi_adapter.clone() as Arc<dyn EngineRiskAdapter>),
            ("strategy", self.strategy_adapter.clone() as Arc<dyn EngineRiskAdapter>),
        ];
        
        for (name, adapter) in adapters {
            match EngineRiskAdapter::get_metrics(adapter.as_ref()).await {
                Ok(_) => {
                    status.adapters_healthy.insert(name.to_string(), true);
                }
                Err(e) => {
                    status.adapters_healthy.insert(name.to_string(), false);
                    status.overall_healthy = false;
                    status.error_messages.push(format!("{} adapter error: {}", name, e));
                }
            }
        }
        
        // 检查指标收集器健康状态
        match crate::risk::MetricsCollector::get_metrics(self.metrics_collector.as_ref()).await {
            Ok(_) => status.metrics_healthy = true,
            Err(e) => {
                status.overall_healthy = false;
                status.error_messages.push(format!("Metrics error: {}", e));
            }
        }
        
        // 检查数据访问层健康状态
        match self.repository.get_enabled_rules().await {
            Ok(_) => status.repository_healthy = true,
            Err(e) => {
                status.overall_healthy = false;
                status.error_messages.push(format!("Repository error: {}", e));
            }
        }
        
        Ok(status)
    }
    
    /// 获取集成统计信息
    pub async fn get_integration_stats(&self) -> SigmaXResult<IntegrationStats> {
        let facade_stats = self.facade.get_facade_stats().await?;
        let metrics = crate::risk::MetricsCollector::get_metrics(self.metrics_collector.as_ref()).await?;
        let active_alerts = self.metrics_collector.get_active_alerts().await;
        
        Ok(IntegrationStats {
            total_checks: facade_stats.total_checks,
            passed_checks: facade_stats.passed_checks,
            failed_checks: facade_stats.failed_checks,
            cache_hit_rate: facade_stats.cache_hit_rate(),
            avg_latency_ms: facade_stats.avg_latency_ms,
            registered_adapters: facade_stats.registered_adapters,
            active_alerts_count: active_alerts.len(),
            metrics_count: metrics.len(),
        })
    }
}

/// 集成健康状态
#[derive(Debug, Clone)]
pub struct IntegrationHealthStatus {
    pub overall_healthy: bool,
    pub facade_healthy: bool,
    pub adapters_healthy: std::collections::HashMap<String, bool>,
    pub metrics_healthy: bool,
    pub repository_healthy: bool,
    pub error_messages: Vec<String>,
}

/// 集成统计信息
#[derive(Debug, Clone)]
pub struct IntegrationStats {
    pub total_checks: u64,
    pub passed_checks: u64,
    pub failed_checks: u64,
    pub cache_hit_rate: f64,
    pub avg_latency_ms: f64,
    pub registered_adapters: usize,
    pub active_alerts_count: usize,
    pub metrics_count: usize,
}

/// 全局统一风控集成实例
static mut GLOBAL_INTEGRATION: Option<Arc<UnifiedRiskIntegration>> = None;
static INIT_ONCE: std::sync::Once = std::sync::Once::new();

/// 初始化全局统一风控集成
pub async fn initialize_global_integration(config: IntegrationConfig) -> SigmaXResult<()> {
    let integration = Arc::new(UnifiedRiskIntegration::new(config).await?);
    
    unsafe {
        GLOBAL_INTEGRATION = Some(integration);
    }
    
    info!("Global unified risk integration initialized");
    Ok(())
}

/// 获取全局统一风控集成实例
pub fn get_global_integration() -> Option<Arc<UnifiedRiskIntegration>> {
    unsafe { GLOBAL_INTEGRATION.clone() }
}

/// 向后兼容的全局风控检查函数
pub async fn global_check_order_risk(order: &Order) -> SigmaXResult<bool> {
    if let Some(integration) = get_global_integration() {
        integration.legacy_check_order_risk(order).await
    } else {
        Err(SigmaXError::Configuration("Global integration not initialized".to_string()))
    }
}

/// 向后兼容的全局持仓检查函数
pub async fn global_check_position_risk(balances: &[Balance]) -> SigmaXResult<bool> {
    if let Some(integration) = get_global_integration() {
        integration.legacy_check_position_risk(balances).await
    } else {
        Err(SigmaXError::Configuration("Global integration not initialized".to_string()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_unified_integration_creation() {
        let config = IntegrationConfig::default();
        let integration = UnifiedRiskIntegration::new(config).await.unwrap();
        
        // 测试基本功能
        let health = integration.health_check().await.unwrap();
        assert!(health.facade_healthy);
        
        let stats = integration.get_integration_stats().await.unwrap();
        assert_eq!(stats.registered_adapters, 4);
    }
    
    #[tokio::test]
    async fn test_backward_compatibility() {
        let config = IntegrationConfig::default();
        let integration = UnifiedRiskIntegration::new(config).await.unwrap();
        
        // 测试向后兼容接口
        let order = sigmax_core::Order::default(); // 假设有默认实现
        let result = integration.legacy_check_order_risk(&order).await;
        assert!(result.is_ok());
    }
}
