//! 风控服务容器 - IoC容器和服务管理
//! 
//! 设计原则：
//! - 依赖注入：统一的服务依赖管理
//! - 生命周期管理：服务的创建、配置和销毁
//! - 单例模式：确保服务的唯一性和一致性

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, SigmaXError};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

use super::{RiskServiceConfig, RiskEngine, CacheService, MetricsCollector};

/// 风控服务容器 - IoC容器
pub struct RiskServiceContainer {
    /// 风控引擎
    risk_engine: Arc<dyn RiskEngine>,
    /// 缓存服务
    cache_service: Arc<dyn CacheService>,
    /// 指标收集器
    metrics_collector: Arc<dyn MetricsCollector>,
    /// 配置
    config: RiskServiceConfig,
    /// 容器状态
    state: Arc<RwLock<ContainerState>>,
}

/// 容器状态
#[derive(Debug, Clone, PartialEq)]
pub enum ContainerState {
    /// 未初始化
    Uninitialized,
    /// 初始化中
    Initializing,
    /// 已初始化
    Initialized,
    /// 错误状态
    Error(String),
    /// 已销毁
    Destroyed,
}

impl RiskServiceContainer {
    /// 创建新的服务容器
    pub async fn new(
        risk_engine: Arc<dyn RiskEngine>,
        cache_service: Arc<dyn CacheService>,
        metrics_collector: Arc<dyn MetricsCollector>,
        config: RiskServiceConfig,
    ) -> SigmaXResult<Self> {
        info!("Creating risk service container");
        
        let container = Self {
            risk_engine,
            cache_service,
            metrics_collector,
            config,
            state: Arc::new(RwLock::new(ContainerState::Uninitialized)),
        };
        
        // 初始化容器
        container.initialize().await?;
        
        Ok(container)
    }
    
    /// 初始化容器
    async fn initialize(&self) -> SigmaXResult<()> {
        {
            let mut state = self.state.write().await;
            *state = ContainerState::Initializing;
        }
        
        debug!("Initializing risk service container");
        
        // 初始化各个服务
        match self.initialize_services().await {
            Ok(_) => {
                let mut state = self.state.write().await;
                *state = ContainerState::Initialized;
                info!("Risk service container initialized successfully");
                Ok(())
            }
            Err(e) => {
                let mut state = self.state.write().await;
                *state = ContainerState::Error(e.to_string());
                Err(e)
            }
        }
    }
    
    /// 初始化各个服务
    async fn initialize_services(&self) -> SigmaXResult<()> {
        // 初始化风控引擎
        debug!("Initializing risk engine");
        // 这里可以添加风控引擎的初始化逻辑
        
        // 初始化缓存服务
        debug!("Initializing cache service");
        // 这里可以添加缓存服务的初始化逻辑
        
        // 初始化指标收集器
        debug!("Initializing metrics collector");
        // 这里可以添加指标收集器的初始化逻辑
        
        Ok(())
    }
    
    /// 获取风控引擎
    pub fn get_risk_engine(&self) -> Arc<dyn RiskEngine> {
        self.risk_engine.clone()
    }
    
    /// 获取缓存服务
    pub fn get_cache_service(&self) -> Arc<dyn CacheService> {
        self.cache_service.clone()
    }
    
    /// 获取指标收集器
    pub fn get_metrics_collector(&self) -> Arc<dyn MetricsCollector> {
        self.metrics_collector.clone()
    }
    
    /// 获取配置
    pub fn config(&self) -> &RiskServiceConfig {
        &self.config
    }
    
    /// 获取容器状态
    pub async fn get_state(&self) -> ContainerState {
        self.state.read().await.clone()
    }
    
    /// 检查容器是否已初始化
    pub async fn is_initialized(&self) -> bool {
        matches!(*self.state.read().await, ContainerState::Initialized)
    }
    
    /// 健康检查
    pub async fn health_check(&self) -> SigmaXResult<ContainerHealthStatus> {
        let state = self.get_state().await;
        
        if !matches!(state, ContainerState::Initialized) {
            return Ok(ContainerHealthStatus {
                overall_status: HealthStatus::Unhealthy,
                risk_engine_status: HealthStatus::Unknown,
                cache_service_status: HealthStatus::Unknown,
                metrics_collector_status: HealthStatus::Unknown,
                error_message: Some(format!("Container state: {:?}", state)),
            });
        }
        
        // 检查各个服务的健康状态
        let risk_engine_status = self.check_risk_engine_health().await;
        let cache_service_status = self.check_cache_service_health().await;
        let metrics_collector_status = self.check_metrics_collector_health().await;
        
        let overall_status = if risk_engine_status == HealthStatus::Healthy
            && cache_service_status == HealthStatus::Healthy
            && metrics_collector_status == HealthStatus::Healthy
        {
            HealthStatus::Healthy
        } else {
            HealthStatus::Degraded
        };
        
        Ok(ContainerHealthStatus {
            overall_status,
            risk_engine_status,
            cache_service_status,
            metrics_collector_status,
            error_message: None,
        })
    }
    
    /// 检查风控引擎健康状态
    async fn check_risk_engine_health(&self) -> HealthStatus {
        // 伪代码：实际实现中可以调用风控引擎的健康检查方法
        /*
        match self.risk_engine.health_check().await {
            Ok(_) => HealthStatus::Healthy,
            Err(_) => HealthStatus::Unhealthy,
        }
        */
        
        // 临时实现
        HealthStatus::Healthy
    }
    
    /// 检查缓存服务健康状态
    async fn check_cache_service_health(&self) -> HealthStatus {
        // 伪代码：可以尝试一个简单的缓存操作
        /*
        match self.cache_service.set("health_check", "ok", Duration::from_secs(1)).await {
            Ok(_) => HealthStatus::Healthy,
            Err(_) => HealthStatus::Unhealthy,
        }
        */
        
        // 临时实现
        HealthStatus::Healthy
    }
    
    /// 检查指标收集器健康状态
    async fn check_metrics_collector_health(&self) -> HealthStatus {
        // 伪代码：可以尝试记录一个测试指标
        /*
        self.metrics_collector.record_risk_check(true).await;
        HealthStatus::Healthy
        */
        
        // 临时实现
        HealthStatus::Healthy
    }
    
    /// 重新加载配置
    pub async fn reload_config(&mut self, new_config: RiskServiceConfig) -> SigmaXResult<()> {
        info!("Reloading risk service container configuration");
        
        // 验证新配置
        self.validate_config(&new_config)?;
        
        // 应用新配置
        self.config = new_config;
        
        // 重新初始化服务（如果需要）
        self.reinitialize_services().await?;
        
        info!("Configuration reloaded successfully");
        Ok(())
    }
    
    /// 验证配置
    fn validate_config(&self, config: &RiskServiceConfig) -> SigmaXResult<()> {
        if config.cache_ttl_secs == 0 {
            return Err(SigmaXError::Config("Cache TTL cannot be zero".to_string()));
        }

        if config.batch_size == 0 {
            return Err(SigmaXError::Config("Batch size cannot be zero".to_string()));
        }

        if config.timeout_ms == 0 {
            return Err(SigmaXError::Config("Timeout cannot be zero".to_string()));
        }
        
        Ok(())
    }
    
    /// 重新初始化服务
    async fn reinitialize_services(&self) -> SigmaXResult<()> {
        debug!("Reinitializing services with new configuration");
        
        // 这里可以添加需要重新初始化的服务逻辑
        // 例如：重新配置缓存TTL、批量大小等
        
        Ok(())
    }
    
    /// 销毁容器
    pub async fn destroy(&self) -> SigmaXResult<()> {
        info!("Destroying risk service container");
        
        {
            let mut state = self.state.write().await;
            *state = ContainerState::Destroyed;
        }
        
        // 这里可以添加清理逻辑
        // 例如：关闭连接、清理缓存等
        
        info!("Risk service container destroyed");
        Ok(())
    }
}

/// 健康状态
#[derive(Debug, Clone, PartialEq)]
pub enum HealthStatus {
    Healthy,
    Degraded,
    Unhealthy,
    Unknown,
}

/// 容器健康状态
#[derive(Debug, Clone)]
pub struct ContainerHealthStatus {
    pub overall_status: HealthStatus,
    pub risk_engine_status: HealthStatus,
    pub cache_service_status: HealthStatus,
    pub metrics_collector_status: HealthStatus,
    pub error_message: Option<String>,
}

impl ContainerHealthStatus {
    /// 是否健康
    pub fn is_healthy(&self) -> bool {
        matches!(self.overall_status, HealthStatus::Healthy)
    }
    
    /// 是否可用（健康或降级）
    pub fn is_available(&self) -> bool {
        matches!(self.overall_status, HealthStatus::Healthy | HealthStatus::Degraded)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_container_state_transitions() {
        // 注意：这个测试需要Mock实现才能完成
        // 实际测试将在具体服务实现后添加
        
        let config = RiskServiceConfig::default();
        assert!(config.enable_cache);
        assert_eq!(config.batch_size, 100);
    }
    
    #[test]
    fn test_config_validation() {
        // 测试配置验证逻辑
        let invalid_config = RiskServiceConfig {
            cache_ttl_secs: 0,
            ..Default::default()
        };
        
        // 这里需要实际的容器实例来测试验证
        // 暂时只测试配置结构
        assert_eq!(invalid_config.cache_ttl_secs, 0);
    }
}
