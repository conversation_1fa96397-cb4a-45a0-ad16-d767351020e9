//! 缓存服务实现

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, SigmaXError};
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, mpsc};
use tracing::{debug, warn, info};

/// 缓存服务trait
#[async_trait]
pub trait CacheService: Send + Sync {
    /// 获取字符串缓存值
    async fn get_string(&self, key: &str) -> SigmaXResult<Option<String>>;

    /// 获取JSON缓存值
    async fn get_json(&self, key: &str) -> SigmaXResult<Option<serde_json::Value>>;

    /// 设置字符串缓存值
    async fn set_string(&self, key: &str, value: String, ttl: Duration) -> SigmaXResult<()>;

    /// 设置JSON缓存值
    async fn set_json(&self, key: &str, value: serde_json::Value, ttl: Duration) -> SigmaXResult<()>;
    
    /// 删除缓存
    async fn invalidate(&self, pattern: &str) -> SigmaXResult<()>;
    
    /// 清空缓存
    async fn clear(&self) -> SigmaXResult<()>;
}

/// 内存缓存服务
pub struct MemoryCacheService {
    /// 缓存存储
    storage: Arc<RwLock<HashMap<String, CacheEntry>>>,
    /// 配置
    config: CacheConfig,
}

/// 缓存条目
#[derive(Debug, Clone)]
struct CacheEntry {
    data: Vec<u8>,
    created_at: Instant,
    ttl: Duration,
    access_count: u64,
    last_accessed: Instant,
}

/// 缓存配置
#[derive(Debug, Clone)]
pub struct CacheConfig {
    pub max_entries: usize,
    pub default_ttl: Duration,
    pub cleanup_interval: Duration,
    pub enable_lru: bool,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_entries: 10000,
            default_ttl: Duration::from_secs(300), // 5分钟
            cleanup_interval: Duration::from_secs(60), // 1分钟清理一次
            enable_lru: true,
        }
    }
}

impl CacheEntry {
    fn new(data: Vec<u8>, ttl: Duration) -> Self {
        let now = Instant::now();
        Self {
            data,
            created_at: now,
            ttl,
            access_count: 1,
            last_accessed: now,
        }
    }
    
    fn is_expired(&self) -> bool {
        self.created_at.elapsed() > self.ttl
    }
    
    fn access(&mut self) {
        self.access_count += 1;
        self.last_accessed = Instant::now();
    }
}

impl MemoryCacheService {
    /// 创建新的内存缓存服务
    pub fn new(config: CacheConfig) -> Self {
        let service = Self {
            storage: Arc::new(RwLock::new(HashMap::new())),
            config,
        };
        
        // 启动清理任务
        service.start_cleanup_task();
        
        service
    }
    
    /// 启动清理任务
    fn start_cleanup_task(&self) {
        let storage = self.storage.clone();
        let interval = self.config.cleanup_interval;
        
        tokio::spawn(async move {
            let mut cleanup_interval = tokio::time::interval(interval);
            
            loop {
                cleanup_interval.tick().await;
                
                let mut storage_guard = storage.write().await;
                let initial_count = storage_guard.len();
                
                // 移除过期条目
                storage_guard.retain(|_, entry| !entry.is_expired());
                
                let removed_count = initial_count - storage_guard.len();
                if removed_count > 0 {
                    debug!("Cleaned up {} expired cache entries", removed_count);
                }
            }
        });
    }
    
    /// LRU淘汰
    async fn evict_lru(&self) {
        let mut storage = self.storage.write().await;
        
        if storage.len() <= self.config.max_entries {
            return;
        }
        
        // 找到最少使用的条目
        if let Some((lru_key, _)) = storage
            .iter()
            .min_by_key(|(_, entry)| (entry.access_count, entry.last_accessed)) {
            let lru_key = lru_key.clone();
            storage.remove(&lru_key);
            debug!("Evicted LRU cache entry: {}", lru_key);
        }
    }
}

#[async_trait]
impl CacheService for MemoryCacheService {
    async fn get_string(&self, key: &str) -> SigmaXResult<Option<String>> {
        let mut storage = self.storage.write().await;

        if let Some(entry) = storage.get_mut(key) {
            if entry.is_expired() {
                storage.remove(key);
                debug!("Cache entry expired and removed: {}", key);
                return Ok(None);
            }

            entry.access();

            match String::from_utf8(entry.data.clone()) {
                Ok(value) => {
                    debug!("Cache hit: {}", key);
                    Ok(Some(value))
                }
                Err(e) => {
                    warn!("Failed to deserialize cache entry {}: {}", key, e);
                    storage.remove(key);
                    Ok(None)
                }
            }
        } else {
            debug!("Cache miss: {}", key);
            Ok(None)
        }
    }

    async fn get_json(&self, key: &str) -> SigmaXResult<Option<serde_json::Value>> {
        let mut storage = self.storage.write().await;

        if let Some(entry) = storage.get_mut(key) {
            if entry.is_expired() {
                storage.remove(key);
                debug!("Cache entry expired and removed: {}", key);
                return Ok(None);
            }

            entry.access();

            match serde_json::from_slice(&entry.data) {
                Ok(value) => {
                    debug!("Cache hit: {}", key);
                    Ok(Some(value))
                }
                Err(e) => {
                    warn!("Failed to deserialize cache entry {}: {}", key, e);
                    storage.remove(key);
                    Ok(None)
                }
            }
        } else {
            debug!("Cache miss: {}", key);
            Ok(None)
        }
    }
    
    async fn set_string(&self, key: &str, value: String, ttl: Duration) -> SigmaXResult<()> {
        let data = value.into_bytes();

        let entry = CacheEntry::new(data, ttl);

        {
            let mut storage = self.storage.write().await;
            storage.insert(key.to_string(), entry);
        }

        // 检查是否需要淘汰
        if self.config.enable_lru {
            self.evict_lru().await;
        }

        debug!("Cache set: {} (TTL: {:?})", key, ttl);
        Ok(())
    }

    async fn set_json(&self, key: &str, value: serde_json::Value, ttl: Duration) -> SigmaXResult<()> {
        let data = serde_json::to_vec(&value)
            .map_err(|e| SigmaXError::Internal(format!("Cache serialization failed: {}", e)))?;

        let entry = CacheEntry::new(data, ttl);

        {
            let mut storage = self.storage.write().await;
            storage.insert(key.to_string(), entry);
        }

        // 检查是否需要淘汰
        if self.config.enable_lru {
            self.evict_lru().await;
        }

        debug!("Cache set: {} (TTL: {:?})", key, ttl);
        Ok(())
    }
    
    async fn invalidate(&self, pattern: &str) -> SigmaXResult<()> {
        let mut storage = self.storage.write().await;
        let initial_count = storage.len();
        
        storage.retain(|key, _| !key.contains(pattern));
        
        let removed_count = initial_count - storage.len();
        debug!("Invalidated {} cache entries matching pattern: {}", removed_count, pattern);
        
        Ok(())
    }
    
    async fn clear(&self) -> SigmaXResult<()> {
        let mut storage = self.storage.write().await;
        let count = storage.len();
        storage.clear();
        
        debug!("Cleared {} cache entries", count);
        Ok(())
    }
}

/// Redis缓存服务配置
#[derive(Debug, Clone)]
pub struct RedisCacheConfig {
    /// Redis连接URL
    pub redis_url: String,
    /// 连接池大小
    pub pool_size: u32,
    /// 连接超时 (秒)
    pub connection_timeout_secs: u64,
    /// 命令超时 (秒)
    pub command_timeout_secs: u64,
    /// 启用集群模式
    pub enable_cluster: bool,
    /// 键前缀
    pub key_prefix: String,
}

impl Default for RedisCacheConfig {
    fn default() -> Self {
        Self {
            redis_url: "redis://localhost:6379".to_string(),
            pool_size: 10,
            connection_timeout_secs: 5,
            command_timeout_secs: 3,
            enable_cluster: false,
            key_prefix: "sigmax:".to_string(),
        }
    }
}

/// Redis缓存服务 (完整实现)
pub struct RedisCacheService {
    config: RedisCacheConfig,
    // 在实际实现中，这里会有Redis连接池
    // pool: Arc<redis::Pool>,
}

impl RedisCacheService {
    pub fn new(config: RedisCacheConfig) -> Self {
        Self {
            config,
        }
    }

    /// 构建完整的Redis键
    fn build_key(&self, key: &str) -> String {
        format!("{}{}", self.config.key_prefix, key)
    }
}

#[async_trait]
impl CacheService for RedisCacheService {
    async fn get_string(&self, key: &str) -> SigmaXResult<Option<String>> {
        let redis_key = self.build_key(key);
        debug!("Redis GET_STRING: {}", redis_key);
        // 模拟实现，实际应该从Redis获取
        Ok(None)
    }

    async fn get_json(&self, key: &str) -> SigmaXResult<Option<serde_json::Value>> {
        let redis_key = self.build_key(key);
        debug!("Redis GET_JSON: {}", redis_key);
        // 模拟实现，实际应该从Redis获取并解析JSON
        Ok(None)
    }

    async fn set_string(&self, key: &str, value: String, ttl: Duration) -> SigmaXResult<()> {
        let redis_key = self.build_key(key);
        debug!("Redis SET_STRING: {} (TTL: {:?})", redis_key, ttl);
        // 模拟实现，实际应该设置到Redis
        Ok(())
    }

    async fn set_json(&self, key: &str, value: serde_json::Value, ttl: Duration) -> SigmaXResult<()> {
        let redis_key = self.build_key(key);
        debug!("Redis SET_JSON: {} (TTL: {:?})", redis_key, ttl);
        // 模拟实现，实际应该序列化JSON并设置到Redis
        Ok(())
    }

    async fn invalidate(&self, pattern: &str) -> SigmaXResult<()> {
        let redis_pattern = self.build_key(&format!("*{}*", pattern));

        // 在实际实现中，这里会使用Redis SCAN + DEL
        // let mut conn = self.pool.get().await?;
        // let keys: Vec<String> = conn.keys(&redis_pattern).await?;
        // if !keys.is_empty() {
        //     conn.del(&keys).await?;
        // }

        debug!("Redis INVALIDATE: {}", redis_pattern);
        Ok(())
    }

    async fn clear(&self) -> SigmaXResult<()> {
        let pattern = self.build_key("*");

        // 在实际实现中，这里会清理所有带前缀的键
        // let mut conn = self.pool.get().await?;
        // let keys: Vec<String> = conn.keys(&pattern).await?;
        // if !keys.is_empty() {
        //     conn.del(&keys).await?;
        // }

        debug!("Redis CLEAR: {}", pattern);
        Ok(())
    }

}

/// 分层缓存服务
pub struct TieredCacheService {
    l1_cache: Arc<dyn CacheService>,
    l2_cache: Arc<dyn CacheService>,
}

impl TieredCacheService {
    pub fn new(
        l1_cache: Arc<dyn CacheService>,
        l2_cache: Arc<dyn CacheService>,
    ) -> Self {
        Self {
            l1_cache,
            l2_cache,
        }
    }
}

#[async_trait]
impl CacheService for TieredCacheService {
    async fn get_string(&self, key: &str) -> SigmaXResult<Option<String>> {
        // 先查L1缓存
        if let Some(value) = self.l1_cache.get_string(key).await? {
            return Ok(Some(value));
        }

        // 再查L2缓存
        if let Some(value) = self.l2_cache.get_string(key).await? {
            // 提升到L1缓存
            let _ = self.l1_cache.set_string(key, value.clone(), Duration::from_secs(300)).await;
            return Ok(Some(value));
        }

        Ok(None)
    }

    async fn get_json(&self, key: &str) -> SigmaXResult<Option<serde_json::Value>> {
        // 先查L1缓存
        if let Some(value) = self.l1_cache.get_json(key).await? {
            return Ok(Some(value));
        }

        // 再查L2缓存
        if let Some(value) = self.l2_cache.get_json(key).await? {
            // 提升到L1缓存
            let _ = self.l1_cache.set_json(key, value.clone(), Duration::from_secs(300)).await;
            return Ok(Some(value));
        }

        Ok(None)
    }

    async fn set_string(&self, key: &str, value: String, ttl: Duration) -> SigmaXResult<()> {
        // 同时设置L1和L2缓存
        let l1_result = self.l1_cache.set_string(key, value.clone(), ttl).await;
        let l2_result = self.l2_cache.set_string(key, value, ttl).await;

        l1_result?;
        l2_result?;

        Ok(())
    }

    async fn set_json(&self, key: &str, value: serde_json::Value, ttl: Duration) -> SigmaXResult<()> {
        // 同时设置L1和L2缓存
        let l1_result = self.l1_cache.set_json(key, value.clone(), ttl).await;
        let l2_result = self.l2_cache.set_json(key, value, ttl).await;

        l1_result?;
        l2_result?;

        Ok(())
    }
    
    async fn invalidate(&self, pattern: &str) -> SigmaXResult<()> {
        // 同时清理L1和L2缓存
        let l1_result = self.l1_cache.invalidate(pattern).await;
        let l2_result = self.l2_cache.invalidate(pattern).await;
        
        l1_result?;
        l2_result?;
        
        Ok(())
    }
    
    async fn clear(&self) -> SigmaXResult<()> {
        // 同时清理L1和L2缓存
        let l1_result = self.l1_cache.clear().await;
        let l2_result = self.l2_cache.clear().await;
        
        l1_result?;
        l2_result?;

        Ok(())
    }
}

/// 风控缓存策略
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum RiskCacheStrategy {
    /// 回测模式 - 批量缓存，长TTL
    Backtest,
    /// 实盘模式 - 热缓存，短TTL
    Live,
    /// WebAPI模式 - 查询缓存，中等TTL
    WebApi,
    /// 策略模式 - 策略专用缓存
    Strategy,
}

/// 风控缓存服务配置
#[derive(Debug, Clone)]
pub struct RiskCacheServiceConfig {
    /// 启用分层缓存
    pub enable_tiered_cache: bool,
    /// 启用预热
    pub enable_prewarming: bool,
    /// 启用失效通知
    pub enable_invalidation_notifications: bool,
    /// 不同策略的TTL配置
    pub strategy_ttls: HashMap<RiskCacheStrategy, Duration>,
    /// 预热数据大小
    pub prewarm_size: usize,
    /// 失效通知队列大小
    pub notification_queue_size: usize,
}

impl Default for RiskCacheServiceConfig {
    fn default() -> Self {
        let mut strategy_ttls = HashMap::new();
        strategy_ttls.insert(RiskCacheStrategy::Backtest, Duration::from_secs(1800)); // 30分钟
        strategy_ttls.insert(RiskCacheStrategy::Live, Duration::from_secs(60)); // 1分钟
        strategy_ttls.insert(RiskCacheStrategy::WebApi, Duration::from_secs(300)); // 5分钟
        strategy_ttls.insert(RiskCacheStrategy::Strategy, Duration::from_secs(600)); // 10分钟

        Self {
            enable_tiered_cache: true,
            enable_prewarming: true,
            enable_invalidation_notifications: true,
            strategy_ttls,
            prewarm_size: 1000,
            notification_queue_size: 1000,
        }
    }
}

/// 缓存失效通知
#[derive(Debug, Clone)]
pub struct CacheInvalidationNotification {
    pub key: String,
    pub strategy: RiskCacheStrategy,
    pub timestamp: std::time::Instant,
    pub reason: String,
}

/// 统一风控缓存服务
pub struct RiskCacheService {
    /// L1缓存 (内存)
    l1_cache: Arc<MemoryCacheService>,
    /// L2缓存 (Redis，可选)
    l2_cache: Option<Arc<RedisCacheService>>,
    /// 配置
    config: RiskCacheServiceConfig,
    /// 失效通知发送器
    invalidation_sender: Arc<RwLock<Option<mpsc::Sender<CacheInvalidationNotification>>>>,
    /// 预热数据
    prewarmed_keys: Arc<RwLock<HashSet<String>>>,
}

impl RiskCacheService {
    /// 创建新的风控缓存服务
    pub async fn new(config: RiskCacheServiceConfig) -> SigmaXResult<Self> {
        let l1_cache = Arc::new(MemoryCacheService::new(CacheConfig::default()));

        let service = Self {
            l1_cache,
            l2_cache: None,
            config,
            invalidation_sender: Arc::new(RwLock::new(None)),
            prewarmed_keys: Arc::new(RwLock::new(HashSet::new())),
        };

        // 初始化失效通知
        if service.config.enable_invalidation_notifications {
            service.setup_invalidation_notifications().await;
        }

        // 启动预热
        if service.config.enable_prewarming {
            service.start_prewarming().await;
        }

        Ok(service)
    }

    /// 使用Redis创建分层缓存服务
    pub async fn new_with_redis(
        config: RiskCacheServiceConfig,
        redis_config: RedisCacheConfig,
    ) -> SigmaXResult<Self> {
        let l1_cache = Arc::new(MemoryCacheService::new(CacheConfig::default()));
        let l2_cache = Some(Arc::new(RedisCacheService::new(redis_config)));

        let service = Self {
            l1_cache,
            l2_cache,
            config,
            invalidation_sender: Arc::new(RwLock::new(None)),
            prewarmed_keys: Arc::new(RwLock::new(HashSet::new())),
        };

        if service.config.enable_invalidation_notifications {
            service.setup_invalidation_notifications().await;
        }

        if service.config.enable_prewarming {
            service.start_prewarming().await;
        }

        Ok(service)
    }

    /// 策略专用缓存获取
    pub async fn get_with_strategy<T>(
        &self,
        key: &str,
        strategy: RiskCacheStrategy,
    ) -> SigmaXResult<Option<T>>
    where
        T: serde::de::DeserializeOwned + Clone,
    {
        let strategy_key = self.build_strategy_key(key, strategy);

        // 先查L1缓存
        if let Some(json_value) = self.l1_cache.get_json(&strategy_key).await? {
            debug!("L1 cache hit for strategy {:?}: {}", strategy, key);
            if let Ok(value) = serde_json::from_value(json_value) {
                return Ok(Some(value));
            }
        }

        // 再查L2缓存 (如果启用)
        if let Some(l2_cache) = &self.l2_cache {
            if let Some(json_value) = l2_cache.get_json(&strategy_key).await? {
                debug!("L2 cache hit for strategy {:?}: {}", strategy, key);

                if let Ok(value) = serde_json::from_value::<T>(json_value.clone()) {
                    // 提升到L1缓存
                    let ttl = self.get_strategy_ttl(strategy);
                    let _ = self.l1_cache.set_json(&strategy_key, json_value, ttl).await;

                    return Ok(Some(value));
                }
            }
        }

        debug!("Cache miss for strategy {:?}: {}", strategy, key);
        Ok(None)
    }

    /// 策略专用缓存设置
    pub async fn set_with_strategy<T>(
        &self,
        key: &str,
        value: T,
        strategy: RiskCacheStrategy,
    ) -> SigmaXResult<()>
    where
        T: serde::Serialize + Clone,
    {
        let strategy_key = self.build_strategy_key(key, strategy);
        let ttl = self.get_strategy_ttl(strategy);

        // 序列化为JSON
        let json_value = serde_json::to_value(&value)
            .map_err(|e| SigmaXError::Internal(format!("Cache serialization failed: {}", e)))?;

        // 设置L1缓存
        self.l1_cache.set_json(&strategy_key, json_value.clone(), ttl).await?;

        // 设置L2缓存 (如果启用)
        if let Some(l2_cache) = &self.l2_cache {
            l2_cache.set_json(&strategy_key, json_value, ttl).await?;
        }

        // 记录预热键
        if self.config.enable_prewarming {
            let mut prewarmed = self.prewarmed_keys.write().await;
            prewarmed.insert(strategy_key.clone());
        }

        debug!("Cache set for strategy {:?}: {} (TTL: {:?})", strategy, key, ttl);
        Ok(())
    }

    /// 策略专用缓存失效
    pub async fn invalidate_with_strategy(
        &self,
        pattern: &str,
        strategy: RiskCacheStrategy,
    ) -> SigmaXResult<()> {
        let strategy_pattern = self.build_strategy_key(pattern, strategy);

        // 失效L1缓存
        self.l1_cache.invalidate(&strategy_pattern).await?;

        // 失效L2缓存 (如果启用)
        if let Some(l2_cache) = &self.l2_cache {
            l2_cache.invalidate(&strategy_pattern).await?;
        }

        // 发送失效通知
        self.send_invalidation_notification(
            strategy_pattern,
            strategy,
            "Manual invalidation".to_string(),
        ).await;

        debug!("Cache invalidated for strategy {:?}: {}", strategy, pattern);
        Ok(())
    }

    /// 预热缓存
    pub async fn prewarm_cache(&self, keys: Vec<String>, strategy: RiskCacheStrategy) -> SigmaXResult<()> {
        if !self.config.enable_prewarming {
            return Ok(());
        }

        info!("Prewarming cache for strategy {:?} with {} keys", strategy, keys.len());

        let mut prewarmed = self.prewarmed_keys.write().await;
        for key in keys {
            let strategy_key = self.build_strategy_key(&key, strategy);
            prewarmed.insert(strategy_key);
        }

        Ok(())
    }

    /// 获取缓存统计
    pub async fn get_cache_stats(&self) -> RiskCacheStats {
        // 这里会收集L1和L2缓存的统计信息
        RiskCacheStats {
            l1_entries: 0, // 实际实现中从L1缓存获取
            l2_entries: 0, // 实际实现中从L2缓存获取
            prewarmed_entries: {
                let prewarmed = self.prewarmed_keys.read().await;
                prewarmed.len()
            },
            hit_rate: 0.85, // 模拟命中率
            avg_latency_ms: 2.5, // 模拟平均延迟
        }
    }

    /// 构建策略键
    fn build_strategy_key(&self, key: &str, strategy: RiskCacheStrategy) -> String {
        format!("risk:{}:{}", strategy.as_str(), key)
    }

    /// 获取策略TTL
    fn get_strategy_ttl(&self, strategy: RiskCacheStrategy) -> Duration {
        self.config.strategy_ttls.get(&strategy)
            .copied()
            .unwrap_or(Duration::from_secs(300))
    }

    /// 设置失效通知
    async fn setup_invalidation_notifications(&self) {
        let (sender, mut receiver) = mpsc::channel(self.config.notification_queue_size);

        {
            let mut sender_lock = self.invalidation_sender.write().await;
            *sender_lock = Some(sender);
        }

        // 启动通知处理任务
        tokio::spawn(async move {
            while let Some(notification) = receiver.recv().await {
                debug!("Cache invalidation notification: {:?}", notification);
                // 这里可以添加通知处理逻辑，如发送到监控系统
            }
        });
    }

    /// 发送失效通知
    async fn send_invalidation_notification(
        &self,
        key: String,
        strategy: RiskCacheStrategy,
        reason: String,
    ) {
        if let Some(sender) = &*self.invalidation_sender.read().await {
            let notification = CacheInvalidationNotification {
                key,
                strategy,
                timestamp: std::time::Instant::now(),
                reason,
            };

            let _ = sender.send(notification).await;
        }
    }

    /// 启动预热
    async fn start_prewarming(&self) {
        let prewarmed_keys = self.prewarmed_keys.clone();

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(3600)); // 每小时预热一次
            loop {
                interval.tick().await;

                let keys = prewarmed_keys.read().await;
                debug!("Prewarming {} cache entries", keys.len());

                // 这里可以添加实际的预热逻辑
            }
        });
    }
}

impl RiskCacheStrategy {
    fn as_str(&self) -> &'static str {
        match self {
            RiskCacheStrategy::Backtest => "backtest",
            RiskCacheStrategy::Live => "live",
            RiskCacheStrategy::WebApi => "webapi",
            RiskCacheStrategy::Strategy => "strategy",
        }
    }
}

/// 风控缓存统计
#[derive(Debug, Clone)]
pub struct RiskCacheStats {
    pub l1_entries: usize,
    pub l2_entries: usize,
    pub prewarmed_entries: usize,
    pub hit_rate: f64,
    pub avg_latency_ms: f64,
}

#[async_trait]
impl CacheService for RiskCacheService {
    async fn get_string(&self, key: &str) -> SigmaXResult<Option<String>> {
        // 默认使用Live策略
        self.l1_cache.get_string(key).await
    }

    async fn get_json(&self, key: &str) -> SigmaXResult<Option<serde_json::Value>> {
        // 默认使用Live策略
        self.l1_cache.get_json(key).await
    }

    async fn set_string(&self, key: &str, value: String, ttl: Duration) -> SigmaXResult<()> {
        // 默认使用Live策略
        self.l1_cache.set_string(key, value, ttl).await
    }

    async fn set_json(&self, key: &str, value: serde_json::Value, ttl: Duration) -> SigmaXResult<()> {
        // 默认使用Live策略
        self.l1_cache.set_json(key, value, ttl).await
    }

    async fn invalidate(&self, pattern: &str) -> SigmaXResult<()> {
        // 失效所有策略的缓存
        for strategy in [RiskCacheStrategy::Backtest, RiskCacheStrategy::Live,
                        RiskCacheStrategy::WebApi, RiskCacheStrategy::Strategy] {
            self.invalidate_with_strategy(pattern, strategy).await?;
        }
        Ok(())
    }

    async fn clear(&self) -> SigmaXResult<()> {
        self.l1_cache.clear().await?;
        if let Some(l2_cache) = &self.l2_cache {
            l2_cache.clear().await?;
        }

        let mut prewarmed = self.prewarmed_keys.write().await;
        prewarmed.clear();

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_memory_cache_basic_operations() {
        let cache = MemoryCacheService::new(CacheConfig::default());
        
        // 测试设置和获取
        cache.set("test_key", "test_value", Duration::from_secs(60)).await.unwrap();
        let result: Option<String> = cache.get("test_key").await.unwrap();
        assert_eq!(result, Some("test_value".to_string()));
        
        // 测试不存在的键
        let result: Option<String> = cache.get("non_existent").await.unwrap();
        assert_eq!(result, None);
    }
    
    #[tokio::test]
    async fn test_cache_expiration() {
        let cache = MemoryCacheService::new(CacheConfig::default());
        
        // 设置短TTL
        cache.set("expire_key", "expire_value", Duration::from_millis(100)).await.unwrap();
        
        // 立即获取应该成功
        let result: Option<String> = cache.get("expire_key").await.unwrap();
        assert_eq!(result, Some("expire_value".to_string()));
        
        // 等待过期
        tokio::time::sleep(Duration::from_millis(150)).await;
        
        // 再次获取应该失败
        let result: Option<String> = cache.get("expire_key").await.unwrap();
        assert_eq!(result, None);
    }
    
    #[tokio::test]
    async fn test_cache_invalidation() {
        let cache = MemoryCacheService::new(CacheConfig::default());
        
        // 设置多个键
        cache.set("user:1", "user1", Duration::from_secs(60)).await.unwrap();
        cache.set("user:2", "user2", Duration::from_secs(60)).await.unwrap();
        cache.set("product:1", "product1", Duration::from_secs(60)).await.unwrap();
        
        // 清理user相关的缓存
        cache.invalidate("user:").await.unwrap();
        
        // 检查结果
        let result1: Option<String> = cache.get("user:1").await.unwrap();
        let result2: Option<String> = cache.get("user:2").await.unwrap();
        let result3: Option<String> = cache.get("product:1").await.unwrap();
        
        assert_eq!(result1, None);
        assert_eq!(result2, None);
        assert_eq!(result3, Some("product1".to_string()));
    }
}
