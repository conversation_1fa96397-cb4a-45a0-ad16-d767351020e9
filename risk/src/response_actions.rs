//! 自动响应动作定义
//! 
//! 定义各种风险响应动作类型和相关的数据结构

use sigmax_core::{StrategyId, TradingPair, OrderSide};
use crate::alert_system::AlertSeverity;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// 自动响应动作类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AutoResponseAction {
    /// 暂停策略
    PauseStrategy {
        strategy_id: Option<StrategyId>,
        duration_minutes: Option<u32>,
    },
    /// 停止策略
    StopStrategy {
        strategy_id: Option<StrategyId>,
    },
    /// 取消订单
    CancelOrders {
        strategy_id: Option<StrategyId>,
        trading_pair: Option<TradingPair>,
        order_side: Option<OrderSide>,
    },
    /// 部分平仓
    PartialClose {
        strategy_id: Option<StrategyId>,
        close_percentage: f64,
        trading_pair: Option<TradingPair>,
    },
    /// 全部平仓
    FullClose {
        strategy_id: Option<StrategyId>,
        trading_pair: Option<TradingPair>,
    },
    /// 减少仓位大小
    ReducePositionSize {
        strategy_id: Option<StrategyId>,
        reduction_percentage: f64,
    },
    /// 调整风险参数
    AdjustRiskParameters {
        strategy_id: Option<StrategyId>,
        parameter_adjustments: HashMap<String, f64>,
    },
    /// 增加流动性缓冲
    IncreaseLiquidityBuffer {
        buffer_percentage: f64,
    },
    /// 发送通知
    SendNotification {
        message: String,
        severity: AlertSeverity,
    },
    /// 记录日志
    LogEvent {
        message: String,
        level: LogLevel,
    },
    /// 自定义动作
    CustomAction {
        action_name: String,
        parameters: HashMap<String, String>,
    },
}

/// 日志级别
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum LogLevel {
    Debug,
    Info,
    Warn,
    Error,
}

impl AutoResponseAction {
    /// 获取动作的描述性名称
    pub fn action_name(&self) -> &'static str {
        match self {
            AutoResponseAction::PauseStrategy { .. } => "暂停策略",
            AutoResponseAction::StopStrategy { .. } => "停止策略",
            AutoResponseAction::CancelOrders { .. } => "取消订单",
            AutoResponseAction::PartialClose { .. } => "部分平仓",
            AutoResponseAction::FullClose { .. } => "全部平仓",
            AutoResponseAction::ReducePositionSize { .. } => "减少仓位",
            AutoResponseAction::AdjustRiskParameters { .. } => "调整风险参数",
            AutoResponseAction::IncreaseLiquidityBuffer { .. } => "增加流动性缓冲",
            AutoResponseAction::SendNotification { .. } => "发送通知",
            AutoResponseAction::LogEvent { .. } => "记录日志",
            AutoResponseAction::CustomAction { .. } => "自定义动作",
        }
    }

    /// 检查动作是否需要策略控制器
    pub fn requires_strategy_controller(&self) -> bool {
        matches!(
            self,
            AutoResponseAction::PauseStrategy { .. }
                | AutoResponseAction::StopStrategy { .. }
                | AutoResponseAction::ReducePositionSize { .. }
                | AutoResponseAction::AdjustRiskParameters { .. }
        )
    }

    /// 检查动作是否需要订单管理器
    pub fn requires_order_manager(&self) -> bool {
        matches!(
            self,
            AutoResponseAction::CancelOrders { .. }
                | AutoResponseAction::PartialClose { .. }
                | AutoResponseAction::FullClose { .. }
        )
    }

    /// 获取动作的风险级别
    pub fn risk_level(&self) -> ActionRiskLevel {
        match self {
            AutoResponseAction::FullClose { .. } 
                | AutoResponseAction::StopStrategy { .. } => ActionRiskLevel::High,
            AutoResponseAction::PartialClose { .. }
                | AutoResponseAction::CancelOrders { .. }
                | AutoResponseAction::PauseStrategy { .. }
                | AutoResponseAction::ReducePositionSize { .. } => ActionRiskLevel::Medium,
            AutoResponseAction::AdjustRiskParameters { .. }
                | AutoResponseAction::IncreaseLiquidityBuffer { .. } => ActionRiskLevel::Low,
            AutoResponseAction::SendNotification { .. }
                | AutoResponseAction::LogEvent { .. }
                | AutoResponseAction::CustomAction { .. } => ActionRiskLevel::Minimal,
        }
    }
}

/// 动作风险级别
#[derive(Debug, Clone, PartialEq)]
pub enum ActionRiskLevel {
    /// 最小风险 - 仅记录或通知
    Minimal,
    /// 低风险 - 参数调整
    Low,
    /// 中等风险 - 部分操作
    Medium,
    /// 高风险 - 重大操作
    High,
}

impl LogLevel {
    /// 转换为 tracing 日志级别
    pub fn to_tracing_level(&self) -> tracing::Level {
        match self {
            LogLevel::Debug => tracing::Level::DEBUG,
            LogLevel::Info => tracing::Level::INFO,
            LogLevel::Warn => tracing::Level::WARN,
            LogLevel::Error => tracing::Level::ERROR,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_action_name() {
        let action = AutoResponseAction::PauseStrategy {
            strategy_id: None,
            duration_minutes: Some(30),
        };
        assert_eq!(action.action_name(), "暂停策略");
    }

    #[test]
    fn test_requires_strategy_controller() {
        let pause_action = AutoResponseAction::PauseStrategy {
            strategy_id: None,
            duration_minutes: Some(30),
        };
        assert!(pause_action.requires_strategy_controller());

        let notification_action = AutoResponseAction::SendNotification {
            message: "test".to_string(),
            severity: AlertSeverity::Info,
        };
        assert!(!notification_action.requires_strategy_controller());
    }

    #[test]
    fn test_risk_level() {
        let stop_action = AutoResponseAction::StopStrategy {
            strategy_id: None,
        };
        assert_eq!(stop_action.risk_level(), ActionRiskLevel::High);

        let log_action = AutoResponseAction::LogEvent {
            message: "test".to_string(),
            level: LogLevel::Info,
        };
        assert_eq!(log_action.risk_level(), ActionRiskLevel::Minimal);
    }

    #[test]
    fn test_log_level_conversion() {
        assert_eq!(LogLevel::Debug.to_tracing_level(), tracing::Level::DEBUG);
        assert_eq!(LogLevel::Error.to_tracing_level(), tracing::Level::ERROR);
    }
}
