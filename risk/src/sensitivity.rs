//! 敏感性分析
//! 
//! 实现参数变化对风险的影响分析

use sigmax_core::{Amount, SigmaXResult, SigmaXError};
use rust_decimal::Decimal;
use std::collections::HashMap;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use tracing::info;

/// 敏感性分析结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SensitivityAnalysisResult {
    /// 分析ID
    pub analysis_id: Uuid,
    /// 分析参数
    pub analysis_params: SensitivityParams,
    /// 参数敏感性
    pub parameter_sensitivities: HashMap<String, ParameterSensitivity>,
    /// 风险因子敏感性
    pub risk_factor_sensitivities: HashMap<String, RiskFactorSensitivity>,
    /// 希腊字母
    pub greeks: GreeksAnalysis,
    /// 情景敏感性
    pub scenario_sensitivities: Vec<ScenarioSensitivity>,
    /// 分析时间
    pub analyzed_at: DateTime<Utc>,
    /// 计算时间（毫秒）
    pub computation_time_ms: u64,
}

/// 敏感性分析参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SensitivityParams {
    /// 基准投资组合价值
    pub base_portfolio_value: Amount,
    /// 扰动幅度
    pub perturbation_size: f64,
    /// 分析的参数列表
    pub parameters_to_analyze: Vec<String>,
    /// 分析的风险因子
    pub risk_factors_to_analyze: Vec<String>,
    /// 是否计算二阶敏感性
    pub include_second_order: bool,
    /// 是否计算交叉敏感性
    pub include_cross_sensitivities: bool,
}

/// 参数敏感性
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParameterSensitivity {
    /// 参数名称
    pub parameter_name: String,
    /// 基准值
    pub base_value: f64,
    /// 一阶敏感性（Delta）
    pub first_order_sensitivity: f64,
    /// 二阶敏感性（Gamma）
    pub second_order_sensitivity: Option<f64>,
    /// 敏感性等级
    pub sensitivity_level: SensitivityLevel,
    /// 影响方向
    pub impact_direction: ImpactDirection,
    /// 置信区间
    pub confidence_interval: Option<(f64, f64)>,
}

/// 风险因子敏感性
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskFactorSensitivity {
    /// 风险因子名称
    pub factor_name: String,
    /// VaR敏感性
    pub var_sensitivity: f64,
    /// CVaR敏感性
    pub cvar_sensitivity: f64,
    /// 波动率敏感性
    pub volatility_sensitivity: f64,
    /// 相关性敏感性
    pub correlation_sensitivity: f64,
    /// 流动性敏感性
    pub liquidity_sensitivity: f64,
    /// 综合风险敏感性
    pub overall_risk_sensitivity: f64,
}

/// 希腊字母分析
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GreeksAnalysis {
    /// Delta - 价格敏感性
    pub delta: HashMap<String, f64>,
    /// Gamma - Delta的敏感性
    pub gamma: HashMap<String, f64>,
    /// Theta - 时间敏感性
    pub theta: HashMap<String, f64>,
    /// Vega - 波动率敏感性
    pub vega: HashMap<String, f64>,
    /// Rho - 利率敏感性
    pub rho: HashMap<String, f64>,
    /// 自定义希腊字母
    pub custom_greeks: HashMap<String, f64>,
}

/// 情景敏感性
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScenarioSensitivity {
    /// 情景名称
    pub scenario_name: String,
    /// 情景描述
    pub scenario_description: String,
    /// 参数变化
    pub parameter_changes: HashMap<String, f64>,
    /// 风险指标变化
    pub risk_metrics_change: RiskMetricsChange,
    /// 投资组合价值变化
    pub portfolio_value_change: Amount,
    /// 变化百分比
    pub change_percentage: f64,
}

/// 风险指标变化
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMetricsChange {
    /// VaR变化
    pub var_change: f64,
    /// CVaR变化
    pub cvar_change: f64,
    /// 最大回撤变化
    pub max_drawdown_change: f64,
    /// 夏普比率变化
    pub sharpe_ratio_change: f64,
    /// 波动率变化
    pub volatility_change: f64,
}

/// 敏感性等级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SensitivityLevel {
    /// 极低敏感性
    VeryLow,
    /// 低敏感性
    Low,
    /// 中等敏感性
    Medium,
    /// 高敏感性
    High,
    /// 极高敏感性
    VeryHigh,
}

/// 影响方向
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ImpactDirection {
    /// 正向影响
    Positive,
    /// 负向影响
    Negative,
    /// 双向影响
    Bidirectional,
    /// 无影响
    Neutral,
}

/// 敏感性分析引擎
pub struct SensitivityAnalyzer {
    /// 基准风险指标
    base_risk_metrics: Option<BaseRiskMetrics>,
    /// 参数范围
    parameter_ranges: HashMap<String, (f64, f64)>,
    /// 风险计算器
    risk_calculator: Option<Box<dyn RiskCalculator>>,
}

/// 基准风险指标
#[derive(Debug, Clone)]
pub struct BaseRiskMetrics {
    /// VaR
    pub var_95: f64,
    /// CVaR
    pub cvar_95: f64,
    /// 最大回撤
    pub max_drawdown: f64,
    /// 夏普比率
    pub sharpe_ratio: f64,
    /// 波动率
    pub volatility: f64,
    /// 投资组合价值
    pub portfolio_value: Amount,
}

/// 风险计算器接口
pub trait RiskCalculator: Send + Sync {
    /// 计算风险指标
    fn calculate_risk_metrics(&self, parameters: &HashMap<String, f64>) -> SigmaXResult<BaseRiskMetrics>;
}

impl SensitivityAnalyzer {
    /// 创建新的敏感性分析器
    pub fn new() -> Self {
        Self {
            base_risk_metrics: None,
            parameter_ranges: HashMap::new(),
            risk_calculator: None,
        }
    }

    /// 设置基准风险指标
    pub fn set_base_risk_metrics(&mut self, metrics: BaseRiskMetrics) {
        self.base_risk_metrics = Some(metrics);
    }

    /// 设置参数范围
    pub fn set_parameter_range(&mut self, parameter: String, min_value: f64, max_value: f64) {
        self.parameter_ranges.insert(parameter, (min_value, max_value));
    }

    /// 设置风险计算器
    pub fn set_risk_calculator(&mut self, calculator: Box<dyn RiskCalculator>) {
        self.risk_calculator = Some(calculator);
    }

    /// 运行敏感性分析
    pub fn run_sensitivity_analysis(&self, params: SensitivityParams) -> SigmaXResult<SensitivityAnalysisResult> {
        let start_time = std::time::Instant::now();
        
        let base_metrics = self.base_risk_metrics.as_ref()
            .ok_or_else(|| SigmaXError::InvalidOperation("基准风险指标未设置".to_string()))?;
        
        info!("开始敏感性分析，分析 {} 个参数", params.parameters_to_analyze.len());
        
        // 计算参数敏感性
        let parameter_sensitivities = self.calculate_parameter_sensitivities(&params, base_metrics)?;
        
        // 计算风险因子敏感性
        let risk_factor_sensitivities = self.calculate_risk_factor_sensitivities(&params, base_metrics)?;
        
        // 计算希腊字母
        let greeks = self.calculate_greeks(&params, base_metrics)?;
        
        // 计算情景敏感性
        let scenario_sensitivities = self.calculate_scenario_sensitivities(&params, base_metrics)?;
        
        let computation_time_ms = start_time.elapsed().as_millis() as u64;
        
        let result = SensitivityAnalysisResult {
            analysis_id: Uuid::new_v4(),
            analysis_params: params,
            parameter_sensitivities,
            risk_factor_sensitivities,
            greeks,
            scenario_sensitivities,
            analyzed_at: Utc::now(),
            computation_time_ms,
        };
        
        info!("敏感性分析完成，耗时 {} ms", computation_time_ms);
        
        Ok(result)
    }

    /// 计算参数敏感性
    fn calculate_parameter_sensitivities(
        &self,
        params: &SensitivityParams,
        base_metrics: &BaseRiskMetrics,
    ) -> SigmaXResult<HashMap<String, ParameterSensitivity>> {
        let mut sensitivities = HashMap::new();
        
        for parameter in &params.parameters_to_analyze {
            let sensitivity = self.calculate_single_parameter_sensitivity(
                parameter,
                params,
                base_metrics,
            )?;
            sensitivities.insert(parameter.clone(), sensitivity);
        }
        
        Ok(sensitivities)
    }

    /// 计算单个参数敏感性
    fn calculate_single_parameter_sensitivity(
        &self,
        parameter: &str,
        params: &SensitivityParams,
        base_metrics: &BaseRiskMetrics,
    ) -> SigmaXResult<ParameterSensitivity> {
        let base_value = self.get_parameter_base_value(parameter)?;
        let perturbation = base_value * params.perturbation_size;
        
        // 计算上扰动和下扰动的风险指标
        let up_metrics = self.calculate_perturbed_metrics(parameter, base_value + perturbation)?;
        let down_metrics = self.calculate_perturbed_metrics(parameter, base_value - perturbation)?;
        
        // 计算一阶敏感性（数值导数）
        let portfolio_value_change = up_metrics.portfolio_value - down_metrics.portfolio_value;
        let parameter_change = 2.0 * perturbation;
        let first_order_sensitivity = if parameter_change != 0.0 {
            portfolio_value_change.to_string().parse::<f64>().unwrap_or(0.0) / parameter_change
        } else {
            0.0
        };
        
        // 计算二阶敏感性（如果需要）
        let second_order_sensitivity = if params.include_second_order {
            let base_value_f64 = base_metrics.portfolio_value.to_string().parse::<f64>().unwrap_or(0.0);
            let up_value_f64 = up_metrics.portfolio_value.to_string().parse::<f64>().unwrap_or(0.0);
            let down_value_f64 = down_metrics.portfolio_value.to_string().parse::<f64>().unwrap_or(0.0);
            let second_derivative = (up_value_f64 + down_value_f64 - 2.0 * base_value_f64) / (perturbation * perturbation);
            Some(second_derivative)
        } else {
            None
        };
        
        // 确定敏感性等级
        let sensitivity_level = self.classify_sensitivity_level(first_order_sensitivity.abs());
        
        // 确定影响方向
        let impact_direction = if first_order_sensitivity > 0.01 {
            ImpactDirection::Positive
        } else if first_order_sensitivity < -0.01 {
            ImpactDirection::Negative
        } else {
            ImpactDirection::Neutral
        };
        
        Ok(ParameterSensitivity {
            parameter_name: parameter.to_string(),
            base_value,
            first_order_sensitivity,
            second_order_sensitivity,
            sensitivity_level,
            impact_direction,
            confidence_interval: None, // 可以通过蒙特卡洛方法计算
        })
    }

    /// 计算扰动后的风险指标
    fn calculate_perturbed_metrics(&self, parameter: &str, new_value: f64) -> SigmaXResult<BaseRiskMetrics> {
        if let Some(calculator) = &self.risk_calculator {
            let mut parameters = HashMap::new();
            parameters.insert(parameter.to_string(), new_value);
            calculator.calculate_risk_metrics(&parameters)
        } else {
            // 简化实现：基于基准指标估算
            let base_metrics = self.base_risk_metrics.as_ref().unwrap();
            let change_factor = new_value / self.get_parameter_base_value(parameter)?;
            
            Ok(BaseRiskMetrics {
                var_95: base_metrics.var_95 * change_factor,
                cvar_95: base_metrics.cvar_95 * change_factor,
                max_drawdown: base_metrics.max_drawdown * change_factor.sqrt(),
                sharpe_ratio: base_metrics.sharpe_ratio / change_factor.sqrt(),
                volatility: base_metrics.volatility * change_factor.sqrt(),
                portfolio_value: base_metrics.portfolio_value * Decimal::from_f64_retain(change_factor).unwrap_or(Decimal::ONE),
            })
        }
    }

    /// 获取参数基准值
    fn get_parameter_base_value(&self, parameter: &str) -> SigmaXResult<f64> {
        // 简化实现：返回预设的基准值
        let base_value = match parameter {
            "volatility" => 0.20,      // 20%波动率
            "correlation" => 0.50,     // 50%相关性
            "expected_return" => 0.10, // 10%预期收益
            "position_size" => 1.0,    // 100%仓位
            "leverage" => 1.0,         // 1倍杠杆
            "liquidity" => 1.0,        // 100%流动性
            _ => 1.0,                  // 默认值
        };
        
        Ok(base_value)
    }

    /// 分类敏感性等级
    fn classify_sensitivity_level(&self, sensitivity: f64) -> SensitivityLevel {
        match sensitivity {
            s if s < 0.01 => SensitivityLevel::VeryLow,
            s if s < 0.05 => SensitivityLevel::Low,
            s if s < 0.20 => SensitivityLevel::Medium,
            s if s < 0.50 => SensitivityLevel::High,
            _ => SensitivityLevel::VeryHigh,
        }
    }

    /// 计算风险因子敏感性
    fn calculate_risk_factor_sensitivities(
        &self,
        params: &SensitivityParams,
        base_metrics: &BaseRiskMetrics,
    ) -> SigmaXResult<HashMap<String, RiskFactorSensitivity>> {
        let mut sensitivities = HashMap::new();
        
        for factor in &params.risk_factors_to_analyze {
            let sensitivity = RiskFactorSensitivity {
                factor_name: factor.clone(),
                var_sensitivity: self.calculate_factor_var_sensitivity(factor, base_metrics)?,
                cvar_sensitivity: self.calculate_factor_cvar_sensitivity(factor, base_metrics)?,
                volatility_sensitivity: self.calculate_factor_volatility_sensitivity(factor, base_metrics)?,
                correlation_sensitivity: self.calculate_factor_correlation_sensitivity(factor, base_metrics)?,
                liquidity_sensitivity: self.calculate_factor_liquidity_sensitivity(factor, base_metrics)?,
                overall_risk_sensitivity: 0.0, // 将在后面计算
            };
            
            sensitivities.insert(factor.clone(), sensitivity);
        }
        
        Ok(sensitivities)
    }

    /// 计算因子VaR敏感性
    fn calculate_factor_var_sensitivity(&self, _factor: &str, _base_metrics: &BaseRiskMetrics) -> SigmaXResult<f64> {
        // 简化实现
        Ok(0.1)
    }

    /// 计算因子CVaR敏感性
    fn calculate_factor_cvar_sensitivity(&self, _factor: &str, _base_metrics: &BaseRiskMetrics) -> SigmaXResult<f64> {
        // 简化实现
        Ok(0.15)
    }

    /// 计算因子波动率敏感性
    fn calculate_factor_volatility_sensitivity(&self, _factor: &str, _base_metrics: &BaseRiskMetrics) -> SigmaXResult<f64> {
        // 简化实现
        Ok(0.8)
    }

    /// 计算因子相关性敏感性
    fn calculate_factor_correlation_sensitivity(&self, _factor: &str, _base_metrics: &BaseRiskMetrics) -> SigmaXResult<f64> {
        // 简化实现
        Ok(0.3)
    }

    /// 计算因子流动性敏感性
    fn calculate_factor_liquidity_sensitivity(&self, _factor: &str, _base_metrics: &BaseRiskMetrics) -> SigmaXResult<f64> {
        // 简化实现
        Ok(0.2)
    }

    /// 计算希腊字母
    fn calculate_greeks(&self, _params: &SensitivityParams, base_metrics: &BaseRiskMetrics) -> SigmaXResult<GreeksAnalysis> {
        let mut delta = HashMap::new();
        let mut gamma = HashMap::new();
        let mut theta = HashMap::new();
        let mut vega = HashMap::new();
        let mut rho = HashMap::new();
        
        // 简化实现：基于基准指标估算希腊字母
        delta.insert("BTC_USDT".to_string(), 0.6);
        delta.insert("ETH_USDT".to_string(), 0.4);
        
        gamma.insert("BTC_USDT".to_string(), 0.02);
        gamma.insert("ETH_USDT".to_string(), 0.03);
        
        theta.insert("BTC_USDT".to_string(), -0.01);
        theta.insert("ETH_USDT".to_string(), -0.015);
        
        vega.insert("BTC_USDT".to_string(), base_metrics.volatility * 0.5);
        vega.insert("ETH_USDT".to_string(), base_metrics.volatility * 0.6);
        
        rho.insert("BTC_USDT".to_string(), 0.1);
        rho.insert("ETH_USDT".to_string(), 0.12);
        
        Ok(GreeksAnalysis {
            delta,
            gamma,
            theta,
            vega,
            rho,
            custom_greeks: HashMap::new(),
        })
    }

    /// 计算情景敏感性
    fn calculate_scenario_sensitivities(
        &self,
        _params: &SensitivityParams,
        base_metrics: &BaseRiskMetrics,
    ) -> SigmaXResult<Vec<ScenarioSensitivity>> {
        let mut scenarios = Vec::new();
        
        // 波动率上升情景
        let volatility_up_scenario = ScenarioSensitivity {
            scenario_name: "波动率上升50%".to_string(),
            scenario_description: "市场波动率从当前水平上升50%".to_string(),
            parameter_changes: {
                let mut changes = HashMap::new();
                changes.insert("volatility".to_string(), 0.5);
                changes
            },
            risk_metrics_change: RiskMetricsChange {
                var_change: 0.4,
                cvar_change: 0.5,
                max_drawdown_change: 0.3,
                sharpe_ratio_change: -0.2,
                volatility_change: 0.5,
            },
            portfolio_value_change: base_metrics.portfolio_value * Decimal::from_f64_retain(-0.1).unwrap_or(Decimal::ZERO),
            change_percentage: -0.1,
        };
        scenarios.push(volatility_up_scenario);
        
        // 相关性上升情景
        let correlation_up_scenario = ScenarioSensitivity {
            scenario_name: "相关性上升至0.9".to_string(),
            scenario_description: "资产间相关性上升至0.9，分散化效益降低".to_string(),
            parameter_changes: {
                let mut changes = HashMap::new();
                changes.insert("correlation".to_string(), 0.4);
                changes
            },
            risk_metrics_change: RiskMetricsChange {
                var_change: 0.25,
                cvar_change: 0.3,
                max_drawdown_change: 0.2,
                sharpe_ratio_change: -0.15,
                volatility_change: 0.1,
            },
            portfolio_value_change: base_metrics.portfolio_value * Decimal::from_f64_retain(-0.05).unwrap_or(Decimal::ZERO),
            change_percentage: -0.05,
        };
        scenarios.push(correlation_up_scenario);
        
        // 流动性下降情景
        let liquidity_down_scenario = ScenarioSensitivity {
            scenario_name: "流动性下降30%".to_string(),
            scenario_description: "市场流动性下降30%，交易成本上升".to_string(),
            parameter_changes: {
                let mut changes = HashMap::new();
                changes.insert("liquidity".to_string(), -0.3);
                changes
            },
            risk_metrics_change: RiskMetricsChange {
                var_change: 0.15,
                cvar_change: 0.2,
                max_drawdown_change: 0.1,
                sharpe_ratio_change: -0.1,
                volatility_change: 0.05,
            },
            portfolio_value_change: base_metrics.portfolio_value * Decimal::from_f64_retain(-0.03).unwrap_or(Decimal::ZERO),
            change_percentage: -0.03,
        };
        scenarios.push(liquidity_down_scenario);
        
        Ok(scenarios)
    }

    /// 生成敏感性报告
    pub fn generate_sensitivity_report(&self, result: &SensitivityAnalysisResult) -> String {
        let mut report = String::new();
        
        report.push_str("=== 敏感性分析报告 ===\n\n");
        report.push_str(&format!("分析ID: {}\n", result.analysis_id));
        report.push_str(&format!("分析时间: {}\n", result.analyzed_at.format("%Y-%m-%d %H:%M:%S UTC")));
        report.push_str(&format!("计算时间: {} ms\n\n", result.computation_time_ms));
        
        // 参数敏感性摘要
        report.push_str("## 参数敏感性摘要\n");
        for (param, sensitivity) in &result.parameter_sensitivities {
            report.push_str(&format!(
                "- {}: {:.4} ({:?}, {:?})\n",
                param,
                sensitivity.first_order_sensitivity,
                sensitivity.sensitivity_level,
                sensitivity.impact_direction
            ));
        }
        
        // 风险因子敏感性摘要
        report.push_str("\n## 风险因子敏感性摘要\n");
        for (factor, sensitivity) in &result.risk_factor_sensitivities {
            report.push_str(&format!(
                "- {}: VaR敏感性 {:.4}, CVaR敏感性 {:.4}\n",
                factor,
                sensitivity.var_sensitivity,
                sensitivity.cvar_sensitivity
            ));
        }
        
        // 情景分析摘要
        report.push_str("\n## 情景分析摘要\n");
        for scenario in &result.scenario_sensitivities {
            report.push_str(&format!(
                "- {}: 投资组合变化 {:.2}%\n",
                scenario.scenario_name,
                scenario.change_percentage * 100.0
            ));
        }
        
        report
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;

    // 模拟风险计算器
    struct MockRiskCalculator;

    impl RiskCalculator for MockRiskCalculator {
        fn calculate_risk_metrics(&self, _parameters: &HashMap<String, f64>) -> SigmaXResult<BaseRiskMetrics> {
            Ok(BaseRiskMetrics {
                var_95: 0.05,
                cvar_95: 0.08,
                max_drawdown: 0.15,
                sharpe_ratio: 1.2,
                volatility: 0.20,
                portfolio_value: dec!(100000),
            })
        }
    }

    #[test]
    fn test_sensitivity_analysis() {
        let mut analyzer = SensitivityAnalyzer::new();
        
        let base_metrics = BaseRiskMetrics {
            var_95: 0.05,
            cvar_95: 0.08,
            max_drawdown: 0.15,
            sharpe_ratio: 1.2,
            volatility: 0.20,
            portfolio_value: dec!(100000),
        };
        
        analyzer.set_base_risk_metrics(base_metrics);
        analyzer.set_risk_calculator(Box::new(MockRiskCalculator));
        
        let params = SensitivityParams {
            base_portfolio_value: dec!(100000),
            perturbation_size: 0.01, // 1%扰动
            parameters_to_analyze: vec![
                "volatility".to_string(),
                "correlation".to_string(),
                "position_size".to_string(),
            ],
            risk_factors_to_analyze: vec![
                "market_risk".to_string(),
                "credit_risk".to_string(),
            ],
            include_second_order: true,
            include_cross_sensitivities: false,
        };
        
        let result = analyzer.run_sensitivity_analysis(params).unwrap();
        
        assert!(!result.parameter_sensitivities.is_empty());
        assert!(!result.risk_factor_sensitivities.is_empty());
        assert!(!result.scenario_sensitivities.is_empty());
        
        // 生成报告
        let report = analyzer.generate_sensitivity_report(&result);
        assert!(!report.is_empty());
        
        println!("敏感性分析报告:\n{}", report);
    }

    #[test]
    fn test_sensitivity_classification() {
        let analyzer = SensitivityAnalyzer::new();
        
        assert_eq!(analyzer.classify_sensitivity_level(0.005), SensitivityLevel::VeryLow);
        assert_eq!(analyzer.classify_sensitivity_level(0.03), SensitivityLevel::Low);
        assert_eq!(analyzer.classify_sensitivity_level(0.15), SensitivityLevel::Medium);
        assert_eq!(analyzer.classify_sensitivity_level(0.35), SensitivityLevel::High);
        assert_eq!(analyzer.classify_sensitivity_level(0.8), SensitivityLevel::VeryHigh);
    }
}
