//! 配置管理服务
//!
//! 提供风险配置的管理功能

use async_trait::async_trait;
use sigmax_core::SigmaXResult;

use sigmax_core::config::risk::RiskConfigParameters;

/// 配置管理器特征
#[async_trait]
pub trait ConfigManager: Send + Sync {
    /// 获取风险参数
    async fn get_parameters(&self) -> SigmaXResult<RiskConfigParameters>;

    /// 更新风险参数
    async fn update_parameters(&self, parameters: RiskConfigParameters) -> SigmaXResult<()>;
    
    /// 重置为默认参数
    async fn reset_to_defaults(&self) -> SigmaXResult<()>;
}

/// 默认配置管理器
pub struct DefaultConfigManager {
    parameters: std::sync::RwLock<RiskConfigParameters>,
}

impl DefaultConfigManager {
    pub fn new() -> Self {
        Self {
            parameters: std::sync::RwLock::new(RiskConfigParameters::default()),
        }
    }
}

impl Default for DefaultConfigManager {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl ConfigManager for DefaultConfigManager {
    async fn get_parameters(&self) -> SigmaXResult<RiskConfigParameters> {
        let params = self.parameters.read().unwrap();
        Ok(params.clone())
    }

    async fn update_parameters(&self, parameters: RiskConfigParameters) -> SigmaXResult<()> {
        let mut params = self.parameters.write().unwrap();
        *params = parameters;
        Ok(())
    }

    async fn reset_to_defaults(&self) -> SigmaXResult<()> {
        let mut params = self.parameters.write().unwrap();
        *params = RiskConfigParameters::default();
        Ok(())
    }
}
