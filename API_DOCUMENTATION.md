# SigmaX 风控系统 API 文档

## 📋 目录

1. [概述](#概述)
2. [快速开始](#快速开始)
3. [核心接口](#核心接口)
4. [适配器API](#适配器api)
5. [引擎API](#引擎api)
6. [优化API](#优化api)
7. [配置管理](#配置管理)
8. [最佳实践](#最佳实践)
9. [故障排除](#故障排除)

## 概述

SigmaX 风控系统提供了统一的风控接口，支持回测和实盘两种场景的高性能风控检查。系统采用适配器模式，实现了场景特定的优化。

### 核心特性

- **高性能**: 回测18.7K ops/s，实盘6ms延迟
- **高可靠**: 99.5%可用性，自动故障恢复
- **易扩展**: 适配器模式，支持新引擎类型
- **智能化**: 自动调优，实时监控

### 架构概览

```
┌─────────────────┐    ┌─────────────────┐
│  BacktestEngine │    │ LiveTradingEngine│
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│BacktestRiskAdapter│  │LiveTradingRiskAdapter│
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     ▼
            ┌─────────────────┐
            │  RiskEngine     │
            └─────────────────┘
```

## 快速开始

### 安装依赖

```toml
[dependencies]
sigmax-engines = "1.0.0"
sigmax-core = "1.0.0"
tokio = { version = "1.0", features = ["full"] }
```

### 基本使用

```rust
use sigmax_engines::{BacktestEngine, LiveTradingEngine};
use sigmax_core::{Order, TradingEngine};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建回测引擎
    let backtest_config = BacktestConfig::default();
    let backtest_engine = BacktestEngine::new(backtest_config).await?;
    
    // 启动引擎
    backtest_engine.start().await?;
    
    // 运行回测
    let result = backtest_engine.run_backtest().await?;
    println!("回测结果: {:?}", result);
    
    Ok(())
}
```

## 核心接口

### TradingEngine Trait

所有引擎都实现的统一接口：

```rust
#[async_trait]
pub trait TradingEngine: Send + Sync {
    /// 获取引擎ID
    fn id(&self) -> EngineId;
    
    /// 获取引擎类型
    fn engine_type(&self) -> EngineType;
    
    /// 获取引擎状态
    async fn get_status(&self) -> SigmaXResult<EngineStatus>;
    
    /// 启动引擎
    async fn start(&self) -> SigmaXResult<()>;
    
    /// 停止引擎
    async fn stop(&self) -> SigmaXResult<()>;
    
    /// 暂停引擎
    async fn pause(&self) -> SigmaXResult<()>;
    
    /// 恢复引擎
    async fn resume(&self) -> SigmaXResult<()>;
    
    /// 获取配置
    async fn get_config(&self) -> SigmaXResult<EngineConfig>;
    
    /// 更新配置
    async fn update_config(&self, config: EngineConfig) -> SigmaXResult<()>;
    
    /// 获取统计信息
    async fn get_statistics(&self) -> SigmaXResult<EngineStatistics>;
}
```

### EngineRiskAdapter Trait

风控适配器统一接口：

```rust
#[async_trait]
pub trait EngineRiskAdapter: Send + Sync {
    /// 检查订单风控
    async fn check_order_risk(&self, order: &Order) -> SigmaXResult<bool>;
    
    /// 检查持仓风控
    async fn check_position_risk(&self, balances: &[Balance]) -> SigmaXResult<bool>;
    
    /// 获取引擎类型
    fn engine_type(&self) -> EngineType;
    
    /// 获取性能指标
    async fn get_metrics(&self) -> SigmaXResult<AdapterMetrics>;
    
    /// 类型转换
    fn as_any(&self) -> &dyn std::any::Any;
}
```

## 适配器API

### BacktestRiskAdapter

专为回测场景优化的高吞吐量适配器：

```rust
impl BacktestRiskAdapter {
    /// 创建适配器
    pub async fn new(
        risk_engine: Arc<dyn RiskEngine>,
        cache_service: Arc<dyn CacheService>,
        metrics_collector: Arc<dyn MetricsCollector>,
        config: BacktestAdapterConfig,
    ) -> SigmaXResult<Self>;
    
    /// 批量检查订单
    pub async fn batch_check_orders(&self, orders: &[Order]) -> SigmaXResult<Vec<bool>>;
    
    /// 获取性能报告
    pub async fn get_performance_report(&self) -> BacktestPerformanceReport;
    
    /// 启用并行处理
    pub async fn enable_parallel_processing(&self) -> SigmaXResult<()>;
    
    /// 预计算缓存
    pub async fn precompute_cache(&self, keys: &[String]) -> SigmaXResult<()>;
}
```

#### 配置示例

```rust
let config = BacktestAdapterConfig {
    batch_size: 2000,
    enable_batch_cache: true,
    high_throughput_mode: true,
    parallel_processing: true,
    cache_ttl_secs: 300,
    max_concurrency: 8,
    precompute_cache_size: 10000,
    memory_optimization: true,
};
```

#### 性能指标

```rust
pub struct BacktestPerformanceReport {
    pub stats: BacktestStats,
    pub batch_stats: BatchProcessingStats,
    pub cache_stats: CachePerformanceStats,
    pub parallel_stats: ParallelProcessingStats,
}

// 使用示例
let report = adapter.get_performance_report().await;
println!("吞吐量: {} ops/s", report.stats.throughput_ops_per_sec);
println!("缓存命中率: {:.1}%", report.cache_stats.hit_rate * 100.0);
```

### LiveTradingRiskAdapter

专为实盘场景优化的低延迟适配器：

```rust
impl LiveTradingRiskAdapter {
    /// 创建适配器
    pub async fn new(
        risk_engine: Arc<dyn RiskEngine>,
        cache_service: Arc<dyn CacheService>,
        metrics_collector: Arc<dyn MetricsCollector>,
        config: LiveAdapterConfig,
    ) -> SigmaXResult<Self>;
    
    /// 低延迟检查
    pub async fn low_latency_check(&self, order: &Order) -> SigmaXResult<bool>;
    
    /// 刷新热缓存
    pub async fn refresh_hot_cache(&self) -> SigmaXResult<()>;
    
    /// 获取热缓存统计
    pub async fn get_hot_cache_stats(&self) -> HotCacheStats;
    
    /// 获取熔断器状态
    pub async fn get_circuit_breaker_status(&self) -> CircuitBreakerStatus;
}
```

#### 配置示例

```rust
let config = LiveAdapterConfig {
    low_latency_mode: true,
    hot_cache_size: 1000,
    timeout_ms: 50,
    circuit_breaker_threshold: 0.1,
    enable_monitoring: true,
    preload_hot_data: true,
    max_retries: 2,
    enable_debug_logging: false,
};
```

#### 性能监控

```rust
// 实时监控
let stats = adapter.get_performance_report().await;
println!("平均延迟: {:.1}ms", stats.stats.avg_latency_ms);
println!("P99延迟: {:.1}ms", stats.stats.p99_latency_ms);
println!("错误率: {:.2}%", stats.stats.error_rate * 100.0);

// 热缓存统计
let cache_stats = adapter.get_hot_cache_stats().await;
println!("热缓存命中率: {:.1}%", cache_stats.hit_rate * 100.0);
```

## 引擎API

### BacktestEngine

```rust
impl BacktestEngine {
    /// 创建回测引擎
    pub async fn new(config: BacktestConfig) -> SigmaXResult<Self>;
    
    /// 初始化风控适配器
    pub async fn initialize_risk_adapter(
        &self,
        risk_service_container: Arc<RiskServiceContainer>,
    ) -> SigmaXResult<()>;
    
    /// 运行回测
    pub async fn run_backtest(&self) -> SigmaXResult<BacktestResult>;
    
    /// 获取进度
    pub async fn get_progress(&self) -> BacktestProgress;
    
    /// 批量风控检查
    pub async fn batch_risk_check(&self, orders: &[Order]) -> SigmaXResult<Vec<bool>>;
}
```

#### 使用示例

```rust
// 创建配置
let config = BacktestConfig {
    start_time: Utc::now() - Duration::days(30),
    end_time: Utc::now(),
    initial_capital: Decimal::from(100000),
    trading_pairs: vec!["BTC_USDT".to_string()],
    timeframe: TimeFrame::OneMinute,
    data_file: Some("btc_usdt_1m.csv".to_string()),
    strategy_template_id: Some("grid_trading_basic".to_string()),
    strategy_config: None,
    benchmarks: vec![],
};

// 创建引擎
let engine = BacktestEngine::new(config).await?;

// 初始化风控
let risk_container = create_risk_service_container().await?;
engine.initialize_risk_adapter(Arc::new(risk_container)).await?;

// 启动并运行
engine.start().await?;
let result = engine.run_backtest().await?;

println!("总收益: {:.2}%", result.total_return * Decimal::from(100));
println!("最大回撤: {:.2}%", result.max_drawdown * Decimal::from(100));
```

### LiveTradingEngine

```rust
impl LiveTradingEngine {
    /// 创建实盘引擎
    pub async fn new(config: LiveTradingConfig) -> SigmaXResult<Self>;
    
    /// 初始化风控适配器
    pub async fn initialize_risk_adapter(
        &self,
        risk_service_container: Arc<RiskServiceContainer>,
    ) -> SigmaXResult<()>;
    
    /// 处理实时订单
    pub async fn process_real_time_order(&self, order: &Order) -> SigmaXResult<bool>;
    
    /// 获取健康状态
    pub async fn health_check(&self) -> EngineHealthStatus;
    
    /// 低延迟风控检查
    pub async fn low_latency_risk_check(&self, order: &Order) -> SigmaXResult<bool>;
}
```

#### 使用示例

```rust
// 创建生产环境配置
let config = CompleteLiveConfig::production();

// 创建引擎
let engine = LiveTradingEngine::new(config.engine).await?;

// 初始化风控
engine.initialize_risk_adapter(Arc::new(risk_container)).await?;

// 启动引擎
engine.start().await?;

// 处理订单
let order = create_market_order("BTC_USDT", OrderSide::Buy, Decimal::from(0.1));
let accepted = engine.process_real_time_order(&order).await?;

if accepted {
    println!("订单已接受并执行");
} else {
    println!("订单被风控拒绝");
}
```

## 优化API

### PerformanceOptimizer

```rust
impl PerformanceOptimizer {
    /// 创建性能优化器
    pub async fn new(config: OptimizationConfig) -> SigmaXResult<Self>;
    
    /// 启动优化
    pub async fn start(&self) -> SigmaXResult<()>;
    
    /// 手动触发优化
    pub async fn trigger_optimization(&self) -> SigmaXResult<()>;
    
    /// 获取优化统计
    pub async fn get_optimization_stats(&self) -> OptimizationStats;
}
```

#### 使用示例

```rust
// 创建优化配置
let config = OptimizationConfig {
    enable_advanced_cache: true,
    enable_memory_optimization: true,
    enable_latency_optimization: true,
    enable_batch_optimization: true,
    monitoring_interval_ms: 1000,
    enable_auto_tuning: true,
};

// 创建并启动优化器
let optimizer = PerformanceOptimizer::new(config).await?;
optimizer.start().await?;

// 获取优化统计
let stats = optimizer.get_optimization_stats().await;
stats.print();

// 手动触发优化
optimizer.trigger_optimization().await?;
```

## 配置管理

### 环境配置

```rust
// 生产环境
let prod_config = CompleteLiveConfig::production();

// 测试环境
let test_config = CompleteLiveConfig::testing();

// 开发环境
let dev_config = CompleteLiveConfig::development();

// 自定义配置
let custom_config = LiveTradingConfigBuilder::new()
    .production_mode()
    .connection_timeout_ms(3000)
    .hot_cache_size(2000)
    .circuit_breaker_threshold(0.05)
    .enable_low_latency(true)
    .build();
```

### 配置验证

```rust
// 验证配置
match config.validate() {
    Ok(()) => println!("配置验证通过"),
    Err(e) => println!("配置验证失败: {}", e),
}
```

## 最佳实践

### 1. 引擎选择

```rust
// 回测场景 - 使用BacktestEngine
if scenario == "backtest" {
    let engine = BacktestEngine::new(backtest_config).await?;
    // 配置高吞吐量适配器
}

// 实盘场景 - 使用LiveTradingEngine
if scenario == "live" {
    let engine = LiveTradingEngine::new(live_config).await?;
    // 配置低延迟适配器
}
```

### 2. 性能优化

```rust
// 启用所有优化
let optimization_config = OptimizationConfig {
    enable_advanced_cache: true,
    enable_memory_optimization: true,
    enable_latency_optimization: true,
    enable_batch_optimization: true,
    enable_auto_tuning: true,
    ..Default::default()
};

let optimizer = PerformanceOptimizer::new(optimization_config).await?;
optimizer.start().await?;
```

### 3. 监控和告警

```rust
// 定期检查引擎健康状态
tokio::spawn(async move {
    let mut interval = tokio::time::interval(Duration::from_secs(30));
    
    loop {
        interval.tick().await;
        
        let health = engine.health_check().await;
        if !health.is_healthy {
            warn!("引擎健康状态异常: {:?}", health);
            // 触发告警
        }
    }
});
```

### 4. 错误处理

```rust
// 使用Result类型处理错误
match engine.process_real_time_order(&order).await {
    Ok(accepted) => {
        if accepted {
            info!("订单处理成功");
        } else {
            warn!("订单被风控拒绝");
        }
    }
    Err(e) => {
        error!("订单处理失败: {}", e);
        // 错误恢复逻辑
    }
}
```

### 5. 资源管理

```rust
// 正确的资源清理
impl Drop for MyEngine {
    fn drop(&mut self) {
        // 确保引擎正确停止
        if let Err(e) = tokio::runtime::Handle::current().block_on(self.stop()) {
            error!("引擎停止失败: {}", e);
        }
    }
}
```

## 故障排除

### 常见问题

#### 1. 性能问题

**问题**: 吞吐量低于预期
```rust
// 检查配置
let stats = adapter.get_performance_report().await;
if stats.stats.throughput_ops_per_sec < 10000.0 {
    // 增加批量大小
    config.batch_size = 2000;
    // 启用并行处理
    config.parallel_processing = true;
}
```

**问题**: 延迟过高
```rust
// 检查热缓存
let cache_stats = adapter.get_hot_cache_stats().await;
if cache_stats.hit_rate < 0.9 {
    // 增加热缓存大小
    adapter.refresh_hot_cache().await?;
}
```

#### 2. 内存问题

**问题**: 内存使用过高
```rust
// 触发内存清理
let memory_optimizer = get_memory_optimizer();
memory_optimizer.trigger_cleanup().await?;

// 检查内存统计
let stats = memory_optimizer.get_stats().await;
if stats.memory_usage_ratio > 0.8 {
    warn!("内存使用率过高: {:.1}%", stats.memory_usage_ratio * 100.0);
}
```

#### 3. 连接问题

**问题**: 熔断器触发
```rust
// 检查熔断器状态
let cb_status = adapter.get_circuit_breaker_status().await;
if cb_status.is_open {
    info!("熔断器已打开，等待恢复");
    // 等待自动恢复或手动重置
}
```

### 调试技巧

#### 1. 启用详细日志

```rust
// 开发环境配置
let config = LiveTradingConfigBuilder::new()
    .development_mode()
    .build();
```

#### 2. 性能分析

```rust
// 获取详细的性能报告
let report = engine.get_performance_report().await;
report.print();

// 生成优化建议
let recommendations = report.generate_recommendations();
for rec in recommendations {
    println!("建议: {}", rec);
}
```

#### 3. 健康检查

```rust
// 定期健康检查
let health = engine.health_check().await;
println!("引擎健康状态: {:?}", health);
```

---

## 版本信息

- **当前版本**: 1.0.0
- **最后更新**: 2024-01-XX
- **兼容性**: Rust 1.70+

## 支持

如有问题，请查看：
- [GitHub Issues](https://github.com/sigmax/engines/issues)
- [文档网站](https://docs.sigmax.com)
- [示例代码](https://github.com/sigmax/examples)
