//! V2风控事件总线实现
//!
//! 提供异步事件发布和订阅功能

use async_trait::async_trait;
use sigmax_core::SigmaXResult;
use super::super::core::{RiskLevel, RuleViolation};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use std::sync::Arc;
use tokio::sync::broadcast;

/// 风控事件类型
#[derive(Debug, Clone)]
pub enum RiskEvent {
    /// 违规检测事件
    ViolationDetected {
        request_id: Uuid,
        risk_level: RiskLevel,
        violations: Vec<RuleViolation>,
        timestamp: DateTime<Utc>,
        metadata: std::collections::HashMap<String, serde_json::Value>,
    },
    /// 风险预警事件
    RiskWarning {
        warning_id: Uuid,
        message: String,
        risk_level: RiskLevel,
        source: String, // 预警来源（规则名称、系统组件等）
        timestamp: DateTime<Utc>,
        metadata: std::collections::HashMap<String, serde_json::Value>,
    },
    /// 性能指标事件
    PerformanceMetric {
        metric_id: Uuid,
        metric_name: String,
        value: f64,
        unit: String, // 单位（ms, %, count等）
        tags: std::collections::HashMap<String, String>, // 标签（用于分组和过滤）
        timestamp: DateTime<Utc>,
    },
    /// 规则状态变更事件
    RuleStatusChanged {
        rule_name: String,
        old_status: String,
        new_status: String,
        reason: Option<String>,
        timestamp: DateTime<Utc>,
    },
    /// 系统健康状态事件
    SystemHealth {
        component: String, // 组件名称（cache, database, rules等）
        status: HealthStatus,
        message: Option<String>,
        timestamp: DateTime<Utc>,
    },
    /// 配置变更事件
    ConfigurationChanged {
        config_key: String,
        old_value: Option<serde_json::Value>,
        new_value: serde_json::Value,
        changed_by: String, // 变更者
        timestamp: DateTime<Utc>,
    },
    /// 自定义事件
    Custom {
        event_type: String,
        data: serde_json::Value,
        timestamp: DateTime<Utc>,
    },
}

/// 健康状态枚举
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum HealthStatus {
    Healthy,
    Warning,
    Critical,
    Unknown,
}

impl std::fmt::Display for HealthStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            HealthStatus::Healthy => write!(f, "healthy"),
            HealthStatus::Warning => write!(f, "warning"),
            HealthStatus::Critical => write!(f, "critical"),
            HealthStatus::Unknown => write!(f, "unknown"),
        }
    }
}

impl RiskEvent {
    /// 获取事件ID
    pub fn event_id(&self) -> Uuid {
        match self {
            RiskEvent::ViolationDetected { request_id, .. } => *request_id,
            RiskEvent::RiskWarning { warning_id, .. } => *warning_id,
            RiskEvent::PerformanceMetric { metric_id, .. } => *metric_id,
            _ => Uuid::new_v4(), // 为其他事件生成临时ID
        }
    }

    /// 获取事件时间戳
    pub fn timestamp(&self) -> DateTime<Utc> {
        match self {
            RiskEvent::ViolationDetected { timestamp, .. } => *timestamp,
            RiskEvent::RiskWarning { timestamp, .. } => *timestamp,
            RiskEvent::PerformanceMetric { timestamp, .. } => *timestamp,
            RiskEvent::RuleStatusChanged { timestamp, .. } => *timestamp,
            RiskEvent::SystemHealth { timestamp, .. } => *timestamp,
            RiskEvent::ConfigurationChanged { timestamp, .. } => *timestamp,
            RiskEvent::Custom { timestamp, .. } => *timestamp,
        }
    }

    /// 获取事件类型名称
    pub fn event_type(&self) -> &str {
        match self {
            RiskEvent::ViolationDetected { .. } => "violation_detected",
            RiskEvent::RiskWarning { .. } => "risk_warning",
            RiskEvent::PerformanceMetric { .. } => "performance_metric",
            RiskEvent::RuleStatusChanged { .. } => "rule_status_changed",
            RiskEvent::SystemHealth { .. } => "system_health",
            RiskEvent::ConfigurationChanged { .. } => "configuration_changed",
            RiskEvent::Custom { event_type, .. } => event_type,
        }
    }

    /// 获取事件严重程度
    pub fn severity(&self) -> EventSeverity {
        match self {
            RiskEvent::ViolationDetected { risk_level, .. } => {
                match risk_level {
                    RiskLevel::None => EventSeverity::Info,
                    RiskLevel::Low => EventSeverity::Warning,
                    RiskLevel::Medium => EventSeverity::Error,
                    RiskLevel::High => EventSeverity::Critical,
                    RiskLevel::Critical => EventSeverity::Critical,
                }
            }
            RiskEvent::RiskWarning { risk_level, .. } => {
                match risk_level {
                    RiskLevel::None => EventSeverity::Info,
                    RiskLevel::Low => EventSeverity::Warning,
                    RiskLevel::Medium => EventSeverity::Error,
                    RiskLevel::High => EventSeverity::Critical,
                    RiskLevel::Critical => EventSeverity::Critical,
                }
            }
            RiskEvent::SystemHealth { status, .. } => {
                match status {
                    HealthStatus::Healthy => EventSeverity::Info,
                    HealthStatus::Warning => EventSeverity::Warning,
                    HealthStatus::Critical => EventSeverity::Critical,
                    HealthStatus::Unknown => EventSeverity::Warning,
                }
            }
            RiskEvent::PerformanceMetric { .. } => EventSeverity::Info,
            RiskEvent::RuleStatusChanged { .. } => EventSeverity::Info,
            RiskEvent::ConfigurationChanged { .. } => EventSeverity::Info,
            RiskEvent::Custom { .. } => EventSeverity::Info,
        }
    }

    /// 创建违规检测事件
    pub fn violation_detected(
        request_id: Uuid,
        risk_level: RiskLevel,
        violations: Vec<RuleViolation>,
    ) -> Self {
        Self::ViolationDetected {
            request_id,
            risk_level,
            violations,
            timestamp: Utc::now(),
            metadata: std::collections::HashMap::new(),
        }
    }

    /// 创建风险预警事件
    pub fn risk_warning(message: String, risk_level: RiskLevel, source: String) -> Self {
        Self::RiskWarning {
            warning_id: Uuid::new_v4(),
            message,
            risk_level,
            source,
            timestamp: Utc::now(),
            metadata: std::collections::HashMap::new(),
        }
    }

    /// 创建性能指标事件
    pub fn performance_metric(
        metric_name: String,
        value: f64,
        unit: String,
    ) -> Self {
        Self::PerformanceMetric {
            metric_id: Uuid::new_v4(),
            metric_name,
            value,
            unit,
            tags: std::collections::HashMap::new(),
            timestamp: Utc::now(),
        }
    }

    /// 创建系统健康事件
    pub fn system_health(component: String, status: HealthStatus, message: Option<String>) -> Self {
        Self::SystemHealth {
            component,
            status,
            message,
            timestamp: Utc::now(),
        }
    }
}

/// 事件严重程度
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum EventSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

/// 事件过滤器
pub type EventFilter = Arc<dyn Fn(&RiskEvent) -> bool + Send + Sync>;

/// 事件订阅者特征
#[async_trait]
pub trait EventSubscriber: Send + Sync {
    /// 处理事件
    async fn handle_event(&self, event: &RiskEvent) -> SigmaXResult<()>;

    /// 获取订阅者名称
    fn name(&self) -> &str {
        "unnamed_subscriber"
    }

    /// 事件过滤器（可选）
    fn filter(&self) -> Option<EventFilter> {
        None
    }

    /// 获取感兴趣的事件类型
    fn interested_event_types(&self) -> Vec<&str> {
        vec![] // 空表示订阅所有事件
    }

    /// 获取最小严重程度
    fn min_severity(&self) -> EventSeverity {
        EventSeverity::Info
    }
}

/// 订阅者包装器，包含过滤逻辑
struct SubscriberWrapper {
    subscriber: Arc<dyn EventSubscriber>,
    filter: Option<EventFilter>,
    interested_types: Vec<String>,
    min_severity: EventSeverity,
}

impl SubscriberWrapper {
    fn new(subscriber: Arc<dyn EventSubscriber>) -> Self {
        let filter = subscriber.filter();
        let interested_types = subscriber.interested_event_types()
            .into_iter()
            .map(|s| s.to_string())
            .collect();
        let min_severity = subscriber.min_severity();

        Self {
            subscriber,
            filter,
            interested_types,
            min_severity,
        }
    }

    /// 检查事件是否应该被处理
    fn should_handle(&self, event: &RiskEvent) -> bool {
        // 检查严重程度
        if event.severity() < self.min_severity {
            return false;
        }

        // 检查事件类型
        if !self.interested_types.is_empty() {
            let event_type = event.event_type();
            if !self.interested_types.iter().any(|t| t == event_type) {
                return false;
            }
        }

        // 检查自定义过滤器
        if let Some(ref filter) = self.filter {
            if !filter(event) {
                return false;
            }
        }

        true
    }

    async fn handle_event(&self, event: &RiskEvent) -> SigmaXResult<()> {
        if self.should_handle(event) {
            self.subscriber.handle_event(event).await
        } else {
            Ok(())
        }
    }
}

/// 风控事件总线
pub struct RiskEventBus {
    /// 事件广播通道
    sender: broadcast::Sender<RiskEvent>,
    /// 订阅者列表
    subscribers: Arc<tokio::sync::RwLock<Vec<SubscriberWrapper>>>,
    /// 事件统计
    stats: Arc<tokio::sync::RwLock<EventBusStats>>,
}

/// 事件总线统计信息
#[derive(Debug, Default)]
pub struct EventBusStats {
    pub total_events: u64,
    pub events_by_type: std::collections::HashMap<String, u64>,
    pub events_by_severity: std::collections::HashMap<String, u64>,
    pub failed_deliveries: u64,
    pub active_subscribers: usize,
}

impl RiskEventBus {
    /// 创建新的事件总线
    pub fn new(capacity: usize) -> Self {
        let (sender, _) = broadcast::channel(capacity);
        Self {
            sender,
            subscribers: Arc::new(tokio::sync::RwLock::new(Vec::new())),
            stats: Arc::new(tokio::sync::RwLock::new(EventBusStats::default())),
        }
    }

    /// 发送事件
    pub async fn emit(&self, event: RiskEvent) -> SigmaXResult<()> {
        // 更新统计信息
        self.update_stats(&event).await;

        // 广播事件
        if let Err(e) = self.sender.send(event.clone()) {
            tracing::warn!("事件广播失败: {}", e);
        }

        // 通知订阅者
        let subscribers = self.subscribers.read().await;
        let mut failed_deliveries = 0;

        for subscriber in subscribers.iter() {
            if let Err(e) = subscriber.handle_event(&event).await {
                tracing::error!("事件处理失败 [{}]: {}",
                    subscriber.subscriber.name(), e);
                failed_deliveries += 1;
            }
        }

        // 更新失败统计
        if failed_deliveries > 0 {
            let mut stats = self.stats.write().await;
            stats.failed_deliveries += failed_deliveries;
        }

        Ok(())
    }

    /// 添加订阅者
    pub async fn subscribe(&self, subscriber: Arc<dyn EventSubscriber>) {
        let wrapper = SubscriberWrapper::new(subscriber);
        let mut subscribers = self.subscribers.write().await;
        subscribers.push(wrapper);

        // 更新统计信息
        let mut stats = self.stats.write().await;
        stats.active_subscribers = subscribers.len();
    }

    /// 移除订阅者
    pub async fn unsubscribe(&self, subscriber_name: &str) -> bool {
        let mut subscribers = self.subscribers.write().await;
        let initial_len = subscribers.len();

        subscribers.retain(|wrapper| wrapper.subscriber.name() != subscriber_name);

        let removed = subscribers.len() < initial_len;
        if removed {
            let mut stats = self.stats.write().await;
            stats.active_subscribers = subscribers.len();
        }

        removed
    }

    /// 更新统计信息
    async fn update_stats(&self, event: &RiskEvent) {
        let mut stats = self.stats.write().await;
        stats.total_events += 1;

        // 按类型统计
        let event_type = event.event_type().to_string();
        *stats.events_by_type.entry(event_type).or_insert(0) += 1;

        // 按严重程度统计
        let severity = format!("{:?}", event.severity());
        *stats.events_by_severity.entry(severity).or_insert(0) += 1;
    }

    /// 创建事件接收器
    pub fn receiver(&self) -> broadcast::Receiver<RiskEvent> {
        self.sender.subscribe()
    }

    /// 获取订阅者数量
    pub async fn subscriber_count(&self) -> usize {
        let subscribers = self.subscribers.read().await;
        subscribers.len()
    }

    /// 获取事件总线统计信息
    pub async fn get_stats(&self) -> EventBusStats {
        let stats = self.stats.read().await;
        EventBusStats {
            total_events: stats.total_events,
            events_by_type: stats.events_by_type.clone(),
            events_by_severity: stats.events_by_severity.clone(),
            failed_deliveries: stats.failed_deliveries,
            active_subscribers: stats.active_subscribers,
        }
    }

    /// 重置统计信息
    pub async fn reset_stats(&self) {
        let mut stats = self.stats.write().await;
        *stats = EventBusStats::default();
        stats.active_subscribers = self.subscriber_count().await;
    }

    /// 获取订阅者列表
    pub async fn list_subscribers(&self) -> Vec<String> {
        let subscribers = self.subscribers.read().await;
        subscribers.iter()
            .map(|wrapper| wrapper.subscriber.name().to_string())
            .collect()
    }
}

impl Default for RiskEventBus {
    fn default() -> Self {
        Self::new(1000) // 默认容量1000个事件
    }
}