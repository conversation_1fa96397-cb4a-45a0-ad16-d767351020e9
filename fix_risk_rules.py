#!/usr/bin/env python3
"""
修复风险规则配置
调整过于严格的风险规则，使回测能够正常进行
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:8080"
RISK_API_BASE = f"{BASE_URL}/api/risk"

def get_risk_rules():
    """获取所有风险规则"""
    try:
        response = requests.get(f"{RISK_API_BASE}/rules")
        if response.status_code == 200:
            return response.json()['data']
        else:
            print(f"❌ 获取风险规则失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print(f"❌ 获取风险规则异常: {e}")
        return []

def update_risk_rule(rule_id, updates):
    """更新风险规则"""
    try:
        response = requests.put(f"{RISK_API_BASE}/rules/{rule_id}", json=updates)
        if response.status_code == 200:
            return True
        else:
            print(f"❌ 更新规则失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 更新规则异常: {e}")
        return False

def fix_position_limit_rule():
    """修复单个持仓大小限制规则"""
    print("🔧 修复单个持仓大小限制规则...")
    
    rules = get_risk_rules()
    position_rule = None
    
    for rule in rules:
        if rule['name'] == '单个持仓大小限制':
            position_rule = rule
            break
    
    if not position_rule:
        print("❌ 未找到单个持仓大小限制规则")
        return False
    
    print(f"找到规则: {position_rule['name']} (ID: {position_rule['id']})")
    print(f"当前参数: {position_rule['parameters']}")
    
    # 更新参数 - 将限制从25%提高到95%
    new_parameters = {
        "max_position_percent": 95.0,  # 从25%提高到95%
        "base_amount": "total_capital",
        "include_unrealized_pnl": True,
        "backtest_mode": True,  # 添加回测模式标识
        "allow_initial_cash_position": True  # 允许初始现金持仓
    }
    
    updates = {
        "parameters": new_parameters,
        "description": "限制单个交易对的最大持仓（回测优化版本）"
    }
    
    if update_risk_rule(position_rule['id'], updates):
        print("✅ 成功更新单个持仓大小限制规则")
        return True
    else:
        print("❌ 更新单个持仓大小限制规则失败")
        return False

def disable_strict_rules():
    """禁用过于严格的规则"""
    print("🔧 禁用过于严格的规则...")
    
    rules = get_risk_rules()
    strict_rule_names = [
        '最大回撤保护',
        '日交易频率限制', 
        '小时交易频率限制'
    ]
    
    success_count = 0
    for rule in rules:
        if rule['name'] in strict_rule_names and rule['enabled']:
            print(f"禁用规则: {rule['name']}")
            updates = {
                "enabled": False,
                "description": rule['description'] + " (临时禁用用于回测)"
            }
            if update_risk_rule(rule['id'], updates):
                success_count += 1
                print(f"✅ 成功禁用: {rule['name']}")
            else:
                print(f"❌ 禁用失败: {rule['name']}")
    
    print(f"✅ 成功禁用 {success_count} 个严格规则")
    return success_count > 0

def increase_order_limits():
    """增加订单限制"""
    print("🔧 增加订单限制...")
    
    rules = get_risk_rules()
    
    # 更新全局订单金额限制
    for rule in rules:
        if rule['rule_type'] == 'order_amount_limit':
            print(f"更新订单金额限制: {rule['name']}")
            new_parameters = {
                "max_amount": 50000.0,  # 从10000提高到50000
                "currency": "USDT",
                "check_mode": "absolute",
                "include_fees": True
            }
            updates = {
                "parameters": new_parameters,
                "description": rule['description'] + " (回测优化版本)"
            }
            if update_risk_rule(rule['id'], updates):
                print("✅ 成功更新订单金额限制")
            else:
                print("❌ 更新订单金额限制失败")
        
        # 更新最大杠杆限制
        elif rule['rule_type'] == 'max_leverage_limit':
            print(f"更新杠杆限制: {rule['name']}")
            new_parameters = {
                "max_leverage": 10.0,  # 增加杠杆限制
                "calculation_method": "gross_exposure",
                "include_pending_orders": False
            }
            updates = {
                "parameters": new_parameters,
                "description": rule['description'] + " (回测优化版本)"
            }
            if update_risk_rule(rule['id'], updates):
                print("✅ 成功更新杠杆限制")
            else:
                print("❌ 更新杠杆限制失败")

def verify_changes():
    """验证更改"""
    print("🔍 验证风险规则更改...")
    
    rules = get_risk_rules()
    
    print("\n📋 关键规则状态:")
    for rule in rules:
        if rule['name'] in ['单个持仓大小限制', '全局订单金额限制', '最大杠杆限制']:
            print(f"  {rule['name']}: 启用={rule['enabled']}")
            if 'max_position_percent' in rule['parameters']:
                print(f"    持仓限制: {rule['parameters']['max_position_percent']}%")
            if 'max_amount' in rule['parameters']:
                print(f"    订单限制: {rule['parameters']['max_amount']} USDT")
            if 'max_leverage' in rule['parameters']:
                print(f"    杠杆限制: {rule['parameters']['max_leverage']}x")
    
    print("\n📋 禁用的规则:")
    for rule in rules:
        if not rule['enabled'] and '回测' in rule['description']:
            print(f"  {rule['name']}: 已禁用")

def main():
    """主函数"""
    print("🔧 开始修复风险规则配置")
    print("🎯 调整过于严格的风险规则，使回测能够正常进行")
    print("=" * 70)
    
    # 1. 修复持仓限制规则
    if not fix_position_limit_rule():
        print("❌ 修复持仓限制规则失败，继续其他修复...")
    
    # 2. 禁用严格规则
    disable_strict_rules()
    
    # 3. 增加订单限制
    increase_order_limits()
    
    # 4. 验证更改
    verify_changes()
    
    print("\n✅ 风险规则修复完成!")
    print("💡 建议:")
    print("   - 重新运行回测测试")
    print("   - 检查策略是否能够正常生成交易")
    print("   - 如果仍有问题，检查服务器日志")

if __name__ == "__main__":
    main()
