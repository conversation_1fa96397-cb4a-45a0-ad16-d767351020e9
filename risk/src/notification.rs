//! 实时预警通知系统
//! 
//! 实现多渠道风险预警通知

use crate::alert_system::{RiskAlert, AlertSeverity};
use sigmax_core::{SigmaXResult, SigmaXError};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use tokio::sync::{mpsc, RwLock};
use std::sync::Arc;
use std::collections::HashMap;
use tracing::{info, warn, error, debug};
use reqwest::Client;

/// 通知渠道类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum NotificationChannel {
    /// 日志通知
    Log,
    /// Webhook通知
    Webhook,
    /// 邮件通知
    Email,
    /// 短信通知
    SMS,
    /// 企业微信
    WeChat,
    /// 钉钉
    DingTalk,
    /// Slack
    Slack,
    /// 自定义通知
    Custom(String),
}

/// 通知配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationConfig {
    /// 配置ID
    pub config_id: Uuid,
    /// 通知渠道
    pub channel: NotificationChannel,
    /// 是否启用
    pub enabled: bool,
    /// 最小严重程度（只有达到此级别才发送通知）
    pub min_severity: AlertSeverity,
    /// 通知目标配置
    pub target_config: NotificationTarget,
    /// 通知模板
    pub template: NotificationTemplate,
    /// 重试配置
    pub retry_config: RetryConfig,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// 通知目标配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NotificationTarget {
    /// Webhook配置
    Webhook {
        url: String,
        headers: HashMap<String, String>,
        timeout_seconds: u64,
    },
    /// 邮件配置
    Email {
        smtp_server: String,
        smtp_port: u16,
        username: String,
        password: String,
        from_address: String,
        to_addresses: Vec<String>,
    },
    /// 短信配置
    SMS {
        provider: String,
        api_key: String,
        phone_numbers: Vec<String>,
    },
    /// 企业微信配置
    WeChat {
        webhook_url: String,
        mentioned_list: Vec<String>,
    },
    /// 钉钉配置
    DingTalk {
        webhook_url: String,
        secret: Option<String>,
        at_mobiles: Vec<String>,
    },
    /// Slack配置
    Slack {
        webhook_url: String,
        channel: String,
        username: Option<String>,
    },
    /// 自定义配置
    Custom {
        config: HashMap<String, String>,
    },
}

/// 通知模板
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationTemplate {
    /// 标题模板
    pub title_template: String,
    /// 内容模板
    pub content_template: String,
    /// 是否包含建议动作
    pub include_recommendations: bool,
    /// 是否包含详细信息
    pub include_details: bool,
}

impl Default for NotificationTemplate {
    fn default() -> Self {
        Self {
            title_template: "🚨 SigmaX风险预警: {alert_type} - {severity}".to_string(),
            content_template: "预警时间: {triggered_at}\n预警类型: {alert_type}\n严重程度: {severity}\n当前值: {current_value}\n阈值: {threshold_value}\n超出百分比: {breach_percentage}%\n\n详细信息:\n{message}\n\n建议动作:\n{recommendations}".to_string(),
            include_recommendations: true,
            include_details: true,
        }
    }
}

/// 重试配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    /// 最大重试次数
    pub max_retries: u32,
    /// 重试间隔（秒）
    pub retry_interval_seconds: u64,
    /// 指数退避
    pub exponential_backoff: bool,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_retries: 3,
            retry_interval_seconds: 5,
            exponential_backoff: true,
        }
    }
}

/// 通知记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationRecord {
    /// 记录ID
    pub record_id: Uuid,
    /// 预警ID
    pub alert_id: Uuid,
    /// 通知渠道
    pub channel: NotificationChannel,
    /// 发送状态
    pub status: NotificationStatus,
    /// 发送时间
    pub sent_at: DateTime<Utc>,
    /// 重试次数
    pub retry_count: u32,
    /// 错误信息
    pub error_message: Option<String>,
    /// 响应信息
    pub response_info: Option<String>,
}

/// 通知状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NotificationStatus {
    /// 待发送
    Pending,
    /// 发送中
    Sending,
    /// 发送成功
    Success,
    /// 发送失败
    Failed,
    /// 重试中
    Retrying,
}

/// 实时通知系统
pub struct RealTimeNotificationSystem {
    /// 通知配置
    notification_configs: Arc<RwLock<HashMap<Uuid, NotificationConfig>>>,
    /// 通知记录
    notification_records: Arc<RwLock<Vec<NotificationRecord>>>,
    /// HTTP客户端
    http_client: Client,
    /// 通知队列发送器
    notification_sender: mpsc::UnboundedSender<(RiskAlert, Vec<NotificationConfig>)>,
    /// 通知队列接收器
    notification_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<(RiskAlert, Vec<NotificationConfig>)>>>>,
}

impl RealTimeNotificationSystem {
    /// 创建新的实时通知系统
    pub fn new() -> Self {
        let (notification_sender, notification_receiver) = mpsc::unbounded_channel();
        
        Self {
            notification_configs: Arc::new(RwLock::new(HashMap::new())),
            notification_records: Arc::new(RwLock::new(Vec::new())),
            http_client: Client::new(),
            notification_sender,
            notification_receiver: Arc::new(RwLock::new(Some(notification_receiver))),
        }
    }

    /// 添加通知配置
    pub async fn add_notification_config(&self, config: NotificationConfig) -> SigmaXResult<()> {
        let mut configs = self.notification_configs.write().await;
        configs.insert(config.config_id, config);
        info!("添加通知配置，总数: {}", configs.len());
        Ok(())
    }

    /// 移除通知配置
    pub async fn remove_notification_config(&self, config_id: Uuid) -> SigmaXResult<()> {
        let mut configs = self.notification_configs.write().await;
        configs.remove(&config_id);
        info!("移除通知配置: {}", config_id);
        Ok(())
    }

    /// 发送预警通知
    pub async fn send_alert_notification(&self, alert: RiskAlert) -> SigmaXResult<()> {
        // 获取适用的通知配置
        let applicable_configs = self.get_applicable_configs(&alert).await;
        
        if applicable_configs.is_empty() {
            debug!("没有适用的通知配置，跳过通知发送");
            return Ok(());
        }
        
        // 发送到通知队列
        if let Err(e) = self.notification_sender.send((alert, applicable_configs)) {
            error!("发送通知到队列失败: {}", e);
            return Err(SigmaXError::Internal(format!("通知队列发送失败: {}", e)));
        }
        
        Ok(())
    }

    /// 获取适用的通知配置
    async fn get_applicable_configs(&self, alert: &RiskAlert) -> Vec<NotificationConfig> {
        let configs = self.notification_configs.read().await;
        
        configs.values()
            .filter(|config| {
                config.enabled && alert.severity >= config.min_severity
            })
            .cloned()
            .collect()
    }

    /// 启动通知处理器
    pub async fn start_notification_processor(&self) -> SigmaXResult<()> {
        let mut receiver = {
            let mut receiver_guard = self.notification_receiver.write().await;
            receiver_guard.take()
                .ok_or_else(|| SigmaXError::Internal("通知处理器已经启动".to_string()))?
        };
        
        let notification_configs = self.notification_configs.clone();
        let notification_records = self.notification_records.clone();
        let http_client = self.http_client.clone();
        
        tokio::spawn(async move {
            info!("启动通知处理器");
            
            while let Some((alert, configs)) = receiver.recv().await {
                for config in configs {
                    let record_id = Uuid::new_v4();
                    
                    // 创建通知记录
                    let mut record = NotificationRecord {
                        record_id,
                        alert_id: alert.alert_id,
                        channel: config.channel.clone(),
                        status: NotificationStatus::Pending,
                        sent_at: Utc::now(),
                        retry_count: 0,
                        error_message: None,
                        response_info: None,
                    };
                    
                    // 发送通知
                    let result = Self::send_notification_with_retry(
                        &alert,
                        &config,
                        &http_client,
                        &mut record,
                    ).await;
                    
                    if let Err(e) = result {
                        error!("通知发送失败: {:?} - {}", config.channel, e);
                        record.status = NotificationStatus::Failed;
                        record.error_message = Some(e.to_string());
                    } else {
                        info!("通知发送成功: {:?}", config.channel);
                        record.status = NotificationStatus::Success;
                    }
                    
                    // 保存通知记录
                    {
                        let mut records = notification_records.write().await;
                        records.push(record);
                    }
                }
            }
            
            info!("通知处理器已停止");
        });
        
        Ok(())
    }

    /// 带重试的通知发送
    async fn send_notification_with_retry(
        alert: &RiskAlert,
        config: &NotificationConfig,
        http_client: &Client,
        record: &mut NotificationRecord,
    ) -> SigmaXResult<()> {
        let mut last_error = None;
        
        for attempt in 0..=config.retry_config.max_retries {
            record.retry_count = attempt;
            record.status = if attempt == 0 {
                NotificationStatus::Sending
            } else {
                NotificationStatus::Retrying
            };
            
            match Self::send_single_notification(alert, config, http_client).await {
                Ok(response_info) => {
                    record.response_info = Some(response_info);
                    return Ok(());
                }
                Err(e) => {
                    last_error = Some(e);
                    
                    if attempt < config.retry_config.max_retries {
                        let delay = if config.retry_config.exponential_backoff {
                            config.retry_config.retry_interval_seconds * (2_u64.pow(attempt))
                        } else {
                            config.retry_config.retry_interval_seconds
                        };
                        
                        warn!("通知发送失败，{}秒后重试 (第{}次): {}", delay, attempt + 1, last_error.as_ref().unwrap());
                        tokio::time::sleep(tokio::time::Duration::from_secs(delay)).await;
                    }
                }
            }
        }
        
        Err(last_error.unwrap_or_else(|| SigmaXError::Internal("未知错误".to_string())))
    }

    /// 发送单次通知
    async fn send_single_notification(
        alert: &RiskAlert,
        config: &NotificationConfig,
        http_client: &Client,
    ) -> SigmaXResult<String> {
        match &config.channel {
            NotificationChannel::Log => {
                Self::send_log_notification(alert, config).await
            }
            NotificationChannel::Webhook => {
                Self::send_webhook_notification(alert, config, http_client).await
            }
            NotificationChannel::WeChat => {
                Self::send_wechat_notification(alert, config, http_client).await
            }
            NotificationChannel::DingTalk => {
                Self::send_dingtalk_notification(alert, config, http_client).await
            }
            NotificationChannel::Slack => {
                Self::send_slack_notification(alert, config, http_client).await
            }
            NotificationChannel::Email => {
                Self::send_email_notification(alert, config).await
            }
            NotificationChannel::SMS => {
                Self::send_sms_notification(alert, config).await
            }
            NotificationChannel::Custom(name) => {
                warn!("自定义通知渠道 '{}' 暂未实现", name);
                Ok("自定义通知渠道暂未实现".to_string())
            }
        }
    }

    /// 发送日志通知
    async fn send_log_notification(alert: &RiskAlert, config: &NotificationConfig) -> SigmaXResult<String> {
        let title = Self::render_template(&config.template.title_template, alert);
        let content = Self::render_template(&config.template.content_template, alert);
        
        match alert.severity {
            AlertSeverity::Emergency => error!("{}\n{}", title, content),
            AlertSeverity::Critical => error!("{}\n{}", title, content),
            AlertSeverity::Warning => warn!("{}\n{}", title, content),
            AlertSeverity::Info => info!("{}\n{}", title, content),
        }
        
        Ok("日志通知已发送".to_string())
    }

    /// 发送Webhook通知
    async fn send_webhook_notification(
        alert: &RiskAlert,
        config: &NotificationConfig,
        http_client: &Client,
    ) -> SigmaXResult<String> {
        if let NotificationTarget::Webhook { url, headers, timeout_seconds } = &config.target_config {
            let title = Self::render_template(&config.template.title_template, alert);
            let content = Self::render_template(&config.template.content_template, alert);
            
            let payload = serde_json::json!({
                "alert_id": alert.alert_id,
                "alert_type": alert.alert_type,
                "severity": alert.severity,
                "title": title,
                "content": content,
                "current_value": alert.current_value,
                "threshold_value": alert.threshold_value,
                "breach_percentage": alert.breach_percentage,
                "triggered_at": alert.triggered_at,
                "recommended_actions": alert.recommended_actions,
            });
            
            let mut request = http_client
                .post(url)
                .json(&payload)
                .timeout(tokio::time::Duration::from_secs(*timeout_seconds));
            
            for (key, value) in headers {
                request = request.header(key, value);
            }
            
            let response = request.send().await
                .map_err(|e| SigmaXError::Network(format!("Webhook请求失败: {}", e)))?;
            
            let status = response.status();
            let response_text = response.text().await
                .map_err(|e| SigmaXError::Network(format!("读取响应失败: {}", e)))?;
            
            if status.is_success() {
                Ok(format!("Webhook通知发送成功: {}", status))
            } else {
                Err(SigmaXError::Network(format!("Webhook通知失败: {} - {}", status, response_text)))
            }
        } else {
            Err(SigmaXError::InvalidOperation("Webhook配置不匹配".to_string()))
        }
    }

    /// 发送企业微信通知
    async fn send_wechat_notification(
        alert: &RiskAlert,
        config: &NotificationConfig,
        http_client: &Client,
    ) -> SigmaXResult<String> {
        if let NotificationTarget::WeChat { webhook_url, mentioned_list } = &config.target_config {
            let content = Self::render_template(&config.template.content_template, alert);
            
            let payload = serde_json::json!({
                "msgtype": "text",
                "text": {
                    "content": content,
                    "mentioned_list": mentioned_list
                }
            });
            
            let response = http_client
                .post(webhook_url)
                .json(&payload)
                .send()
                .await
                .map_err(|e| SigmaXError::Network(format!("企业微信请求失败: {}", e)))?;
            
            let status = response.status();
            if status.is_success() {
                Ok("企业微信通知发送成功".to_string())
            } else {
                let error_text = response.text().await.unwrap_or_default();
                Err(SigmaXError::Network(format!("企业微信通知失败: {} - {}", status, error_text)))
            }
        } else {
            Err(SigmaXError::InvalidOperation("企业微信配置不匹配".to_string()))
        }
    }

    /// 发送钉钉通知
    async fn send_dingtalk_notification(
        alert: &RiskAlert,
        config: &NotificationConfig,
        http_client: &Client,
    ) -> SigmaXResult<String> {
        if let NotificationTarget::DingTalk { webhook_url, secret: _, at_mobiles } = &config.target_config {
            let content = Self::render_template(&config.template.content_template, alert);
            
            let payload = serde_json::json!({
                "msgtype": "text",
                "text": {
                    "content": content
                },
                "at": {
                    "atMobiles": at_mobiles,
                    "isAtAll": false
                }
            });
            
            let response = http_client
                .post(webhook_url)
                .json(&payload)
                .send()
                .await
                .map_err(|e| SigmaXError::Network(format!("钉钉请求失败: {}", e)))?;
            
            let status = response.status();
            if status.is_success() {
                Ok("钉钉通知发送成功".to_string())
            } else {
                let error_text = response.text().await.unwrap_or_default();
                Err(SigmaXError::Network(format!("钉钉通知失败: {} - {}", status, error_text)))
            }
        } else {
            Err(SigmaXError::InvalidOperation("钉钉配置不匹配".to_string()))
        }
    }

    /// 发送Slack通知
    async fn send_slack_notification(
        alert: &RiskAlert,
        config: &NotificationConfig,
        http_client: &Client,
    ) -> SigmaXResult<String> {
        if let NotificationTarget::Slack { webhook_url, channel, username } = &config.target_config {
            let title = Self::render_template(&config.template.title_template, alert);
            let content = Self::render_template(&config.template.content_template, alert);
            
            let mut payload = serde_json::json!({
                "channel": channel,
                "text": title,
                "attachments": [{
                    "color": match alert.severity {
                        AlertSeverity::Emergency => "danger",
                        AlertSeverity::Critical => "danger",
                        AlertSeverity::Warning => "warning",
                        AlertSeverity::Info => "good",
                    },
                    "text": content,
                    "ts": alert.triggered_at.timestamp()
                }]
            });
            
            if let Some(username) = username {
                payload["username"] = serde_json::Value::String(username.clone());
            }
            
            let response = http_client
                .post(webhook_url)
                .json(&payload)
                .send()
                .await
                .map_err(|e| SigmaXError::Network(format!("Slack请求失败: {}", e)))?;
            
            let status = response.status();
            if status.is_success() {
                Ok("Slack通知发送成功".to_string())
            } else {
                let error_text = response.text().await.unwrap_or_default();
                Err(SigmaXError::Network(format!("Slack通知失败: {} - {}", status, error_text)))
            }
        } else {
            Err(SigmaXError::InvalidOperation("Slack配置不匹配".to_string()))
        }
    }

    /// 发送邮件通知（简化实现）
    async fn send_email_notification(_alert: &RiskAlert, _config: &NotificationConfig) -> SigmaXResult<String> {
        // 这里需要集成邮件发送库，如lettre
        warn!("邮件通知功能暂未完全实现");
        Ok("邮件通知功能暂未实现".to_string())
    }

    /// 发送短信通知（简化实现）
    async fn send_sms_notification(_alert: &RiskAlert, _config: &NotificationConfig) -> SigmaXResult<String> {
        // 这里需要集成短信服务提供商API
        warn!("短信通知功能暂未完全实现");
        Ok("短信通知功能暂未实现".to_string())
    }

    /// 渲染模板
    fn render_template(template: &str, alert: &RiskAlert) -> String {
        template
            .replace("{alert_id}", &alert.alert_id.to_string())
            .replace("{alert_type}", &format!("{:?}", alert.alert_type))
            .replace("{severity}", &format!("{:?}", alert.severity))
            .replace("{message}", &alert.message)
            .replace("{current_value}", &format!("{:.4}", alert.current_value))
            .replace("{threshold_value}", &format!("{:.4}", alert.threshold_value))
            .replace("{breach_percentage}", &format!("{:.2}", alert.breach_percentage))
            .replace("{triggered_at}", &alert.triggered_at.format("%Y-%m-%d %H:%M:%S UTC").to_string())
            .replace("{recommendations}", &alert.recommended_actions.join("\n- "))
    }

    /// 获取通知记录
    pub async fn get_notification_records(&self, limit: Option<usize>) -> Vec<NotificationRecord> {
        let records = self.notification_records.read().await;
        let limit = limit.unwrap_or(records.len());
        
        records.iter()
            .rev()
            .take(limit)
            .cloned()
            .collect()
    }

    /// 获取通知统计
    pub async fn get_notification_statistics(&self) -> NotificationStatistics {
        let records = self.notification_records.read().await;
        
        let mut stats = NotificationStatistics::default();
        stats.total_notifications = records.len();
        
        for record in records.iter() {
            match record.status {
                NotificationStatus::Success => stats.successful_notifications += 1,
                NotificationStatus::Failed => stats.failed_notifications += 1,
                NotificationStatus::Pending | NotificationStatus::Sending | NotificationStatus::Retrying => {
                    stats.pending_notifications += 1;
                }
            }
        }
        
        stats
    }
}

/// 通知统计信息
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct NotificationStatistics {
    pub total_notifications: usize,
    pub successful_notifications: usize,
    pub failed_notifications: usize,
    pub pending_notifications: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::alert_system::AlertType;

    #[tokio::test]
    async fn test_notification_system() {
        let notification_system = RealTimeNotificationSystem::new();
        
        // 创建测试配置
        let config = NotificationConfig {
            config_id: Uuid::new_v4(),
            channel: NotificationChannel::Log,
            enabled: true,
            min_severity: AlertSeverity::Warning,
            target_config: NotificationTarget::Custom {
                config: HashMap::new(),
            },
            template: NotificationTemplate::default(),
            retry_config: RetryConfig::default(),
            created_at: Utc::now(),
        };
        
        notification_system.add_notification_config(config).await.unwrap();
        
        // 创建测试预警
        let alert = RiskAlert {
            alert_id: Uuid::new_v4(),
            alert_type: AlertType::VarBreach,
            severity: AlertSeverity::Critical,
            message: "测试预警消息".to_string(),
            current_value: 2500.0,
            threshold_value: 2000.0,
            breach_percentage: 25.0,
            triggered_at: Utc::now(),
            strategy_id: None,
            trading_pair: None,
            recommended_actions: vec!["减少风险敞口".to_string()],
            acknowledged: false,
            acknowledged_at: None,
        };
        
        // 启动通知处理器
        notification_system.start_notification_processor().await.unwrap();
        
        // 发送通知
        notification_system.send_alert_notification(alert).await.unwrap();
        
        // 等待处理
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        
        let stats = notification_system.get_notification_statistics().await;
        assert!(stats.total_notifications > 0);
    }
}
