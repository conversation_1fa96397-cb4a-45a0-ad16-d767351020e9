#!/usr/bin/env python3
"""
检查回测进度的脚本
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8080"
API_BASE = f"{BASE_URL}/api/v1"

def check_progress(engine_id):
    """检查回测进度"""
    print(f"🔍 Checking progress for engine {engine_id}...")
    
    response = requests.get(f"{API_BASE}/engines/{engine_id}/backtest/progress")
    if response.status_code == 200:
        data = response.json()
        print(f"Raw response: {json.dumps(data, indent=2)}")
        
        progress_data = data['data']
        print(f"\n📊 Progress Details:")
        print(f"Engine ID: {progress_data['engine_id']}")
        print(f"Status: {progress_data['status']}")
        
        progress = progress_data['progress']
        print(f"\n📈 Progress Info:")
        print(f"Current Candle: {progress['current_candle']}")
        print(f"Total Candles: {progress['total_candles']}")
        print(f"Percentage: {progress['percentage']:.2f}%")
        print(f"Current Time: {progress.get('current_time', 'N/A')}")
        
        stats = progress_data['current_stats']
        print(f"\n💰 Current Stats:")
        print(f"Total Trades: {stats['total_trades']}")
        print(f"Current Balance: {stats['current_balance']}")
        print(f"Unrealized PnL: {stats['unrealized_pnl']}")
        print(f"Max Drawdown: {stats['max_drawdown']}")
        
    else:
        print(f"❌ Failed to get progress: {response.status_code}")
        print(f"Error: {response.text}")

def check_engine_status(engine_id):
    """检查引擎状态"""
    print(f"\n🔍 Checking engine status for {engine_id}...")
    
    response = requests.get(f"{API_BASE}/engines/{engine_id}")
    if response.status_code == 200:
        data = response.json()
        engine_data = data['data']
        print(f"Engine Status: {engine_data.get('status', 'Unknown')}")
        print(f"Engine Type: {engine_data.get('engine_type', 'Unknown')}")
        print(f"Created At: {engine_data.get('created_at', 'Unknown')}")
    else:
        print(f"❌ Failed to get engine status: {response.status_code}")

if __name__ == "__main__":
    engine_id = "97e864f3-17e3-41e0-b879-0bcbd72fbd35"
    
    check_engine_status(engine_id)
    check_progress(engine_id)
