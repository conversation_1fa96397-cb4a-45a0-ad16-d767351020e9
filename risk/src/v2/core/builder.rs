//! V2风控核心构建器
//!
//! 提供便捷的RiskCore构建方法

use super::{RiskCore, ExecutionStats};
use crate::v2::data::RiskRepository;
use crate::v2::events::RiskEventBus;
use sigmax_core::SigmaXResult;
use std::sync::Arc;

/// RiskCore构建器
pub struct RiskCoreBuilder {
    repository: Option<Arc<dyn RiskRepository>>,
    event_bus: Option<Arc<RiskEventBus>>,
}

impl RiskCoreBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            repository: None,
            event_bus: None,
        }
    }

    /// 设置数据仓库
    pub fn with_repository(mut self, repository: Arc<dyn RiskRepository>) -> Self {
        self.repository = Some(repository);
        self
    }

    /// 使用SQL数据仓库
    pub fn with_sql_repository(self) -> Self {
        // TODO: 实现SQL仓库
        self
    }

    /// 使用内存缓存
    pub fn with_memory_cache(self) -> Self {
        // TODO: 实现内存缓存
        self
    }

    /// 设置事件总线
    pub fn with_event_bus(mut self) -> Self {
        self.event_bus = Some(Arc::new(RiskEventBus::default()));
        self
    }

    /// 添加默认规则
    pub fn with_default_rules(self) -> Self {
        // TODO: 添加默认规则
        self
    }

    /// 构建RiskCore实例
    pub async fn build(self) -> SigmaXResult<RiskCore> {
        let repository = self.repository.ok_or_else(|| {
            sigmax_core::SigmaXError::Config("Repository is required".to_string())
        })?;

        Ok(RiskCore::new(repository, self.event_bus))
    }
}

impl Default for RiskCoreBuilder {
    fn default() -> Self {
        Self::new()
    }
}