//! V2风控规则基础定义
//!
//! 定义可插拔风控规则的核心接口

use async_trait::async_trait;
use sigmax_core::SigmaXResult;
use super::super::core::{RiskRequest, RuleViolation, RiskLevel};

/// 风控规则特征
#[async_trait]
pub trait RiskRule: Send + Sync {
    /// 规则名称
    fn name(&self) -> &str;

    /// 规则优先级
    fn priority(&self) -> RulePriority {
        RulePriority::Normal
    }

    /// 规则状态
    fn status(&self) -> RuleStatus {
        RuleStatus::Enabled
    }

    /// 规则依赖
    fn dependencies(&self) -> Vec<String> {
        Vec::new()
    }

    /// 执行规则检查
    async fn evaluate(&self, request: &RiskRequest) -> SigmaXResult<RuleResult>;

    /// 规则描述
    fn description(&self) -> Option<&str> {
        None
    }

    /// 规则版本
    fn version(&self) -> &str {
        "1.0.0"
    }
}

/// 规则执行结果
#[derive(Debug, Clone)]
pub struct RuleResult {
    /// 是否通过
    pub passed: bool,
    /// 风险等级
    pub risk_level: RiskLevel,
    /// 违规信息（如果有）
    pub violation: Option<RuleViolation>,
    /// 执行时间（微秒）
    pub duration_us: u64,
    /// 额外信息
    pub metadata: std::collections::HashMap<String, serde_json::Value>,
}

impl RuleResult {
    /// 创建通过的结果
    pub fn pass() -> Self {
        Self {
            passed: true,
            risk_level: RiskLevel::None,
            violation: None,
            duration_us: 0,
            metadata: std::collections::HashMap::new(),
        }
    }

    /// 创建失败的结果
    pub fn fail(violation: RuleViolation) -> Self {
        let risk_level = match violation.violation_type {
            super::super::core::ViolationType::ExceedsLimit => RiskLevel::High,
            super::super::core::ViolationType::FrequencyLimit => RiskLevel::Medium,
            _ => RiskLevel::Low,
        };

        Self {
            passed: false,
            risk_level,
            violation: Some(violation),
            duration_us: 0,
            metadata: std::collections::HashMap::new(),
        }
    }

    /// 设置执行时间
    pub fn with_duration(mut self, duration_us: u64) -> Self {
        self.duration_us = duration_us;
        self
    }

    /// 添加元数据
    pub fn with_metadata(mut self, key: String, value: serde_json::Value) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

/// 规则状态
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum RuleStatus {
    /// 启用
    Enabled,
    /// 禁用
    Disabled,
    /// 维护中
    Maintenance,
}

/// 规则优先级
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum RulePriority {
    /// 低优先级
    Low = 1,
    /// 普通优先级
    Normal = 2,
    /// 高优先级
    High = 3,
    /// 关键优先级
    Critical = 4,
}