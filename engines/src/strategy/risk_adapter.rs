//! 策略风控适配器 - 策略专用优化
//! 
//! 设计原则：
//! - 策略专用：针对不同策略类型的专门风控检查
//! - 切换评估：策略切换时的风险评估和建议
//! - 性能分析：策略执行的风险性能分析
//! - 智能建议：基于策略历史表现的风险建议

use async_trait::async_trait;
use sigmax_core::{EngineType, Order, Balance, SigmaXResult, EngineRiskAdapter};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

use crate::risk::{
    adapters::{AdapterBase, AdapterConfig, AdapterMetrics},
    RiskEngine, CacheService, MetricsCollector
};

/// 策略风控适配器配置
#[derive(Debug, Clone)]
pub struct StrategyAdapterConfig {
    /// 启用策略专用检查
    pub enable_strategy_specific_checks: bool,
    /// 启用策略切换评估
    pub enable_switch_evaluation: bool,
    /// 启用性能分析
    pub enable_performance_analysis: bool,
    /// 启用智能建议
    pub enable_smart_suggestions: bool,
    /// 策略历史数据保留天数
    pub history_retention_days: u32,
    /// 风险评估窗口 (小时)
    pub risk_evaluation_window_hours: u32,
    /// 最大策略并发数
    pub max_concurrent_strategies: usize,
    /// 策略切换冷却时间 (分钟)
    pub switch_cooldown_minutes: u32,
}

impl Default for StrategyAdapterConfig {
    fn default() -> Self {
        Self {
            enable_strategy_specific_checks: true,
            enable_switch_evaluation: true,
            enable_performance_analysis: true,
            enable_smart_suggestions: true,
            history_retention_days: 30,
            risk_evaluation_window_hours: 24,
            max_concurrent_strategies: 5,
            switch_cooldown_minutes: 15,
        }
    }
}

/// 策略类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum StrategyType {
    /// 网格策略
    Grid,
    /// DCA策略
    DCA,
    /// 动量策略
    Momentum,
    /// 均值回归策略
    MeanReversion,
    /// 套利策略
    Arbitrage,
    /// 自定义策略
    Custom(String),
}

impl std::fmt::Display for StrategyType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            StrategyType::Grid => write!(f, "Grid"),
            StrategyType::DCA => write!(f, "DCA"),
            StrategyType::Momentum => write!(f, "Momentum"),
            StrategyType::MeanReversion => write!(f, "MeanReversion"),
            StrategyType::Arbitrage => write!(f, "Arbitrage"),
            StrategyType::Custom(name) => write!(f, "Custom({})", name),
        }
    }
}

/// 策略风控检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyRiskResult {
    /// 基本检查结果
    pub passed: bool,
    /// 策略类型
    pub strategy_type: StrategyType,
    /// 风险分数 (0.0-1.0)
    pub risk_score: f64,
    /// 策略专用检查详情
    pub strategy_checks: Vec<StrategyCheckDetail>,
    /// 性能分析
    pub performance_analysis: Option<StrategyPerformanceAnalysis>,
    /// 智能建议
    pub suggestions: Vec<StrategySuggestion>,
    /// 检查时间戳
    pub timestamp: DateTime<Utc>,
}

/// 策略检查详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyCheckDetail {
    /// 检查名称
    pub check_name: String,
    /// 检查结果
    pub passed: bool,
    /// 详细信息
    pub message: String,
    /// 风险级别
    pub risk_level: StrategyRiskLevel,
    /// 建议操作
    pub recommended_action: Option<String>,
}

/// 策略风险级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StrategyRiskLevel {
    /// 安全
    Safe,
    /// 注意
    Caution,
    /// 警告
    Warning,
    /// 危险
    Danger,
}

/// 策略性能分析
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategyPerformanceAnalysis {
    /// 总收益率
    pub total_return: f64,
    /// 夏普比率
    pub sharpe_ratio: f64,
    /// 最大回撤
    pub max_drawdown: f64,
    /// 胜率
    pub win_rate: f64,
    /// 平均持仓时间 (小时)
    pub avg_holding_time_hours: f64,
    /// 风险调整收益
    pub risk_adjusted_return: f64,
}

/// 策略建议
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategySuggestion {
    /// 建议类型
    pub suggestion_type: SuggestionType,
    /// 建议内容
    pub message: String,
    /// 优先级
    pub priority: SuggestionPriority,
    /// 预期影响
    pub expected_impact: String,
}

/// 建议类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SuggestionType {
    /// 参数调整
    ParameterAdjustment,
    /// 策略切换
    StrategySwitch,
    /// 风险控制
    RiskControl,
    /// 性能优化
    PerformanceOptimization,
}

/// 建议优先级
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SuggestionPriority {
    Low,
    Medium,
    High,
    Critical,
}

/// 策略历史记录
#[derive(Debug, Clone)]
struct StrategyHistoryRecord {
    strategy_type: StrategyType,
    timestamp: DateTime<Utc>,
    performance: StrategyPerformanceAnalysis,
    risk_events: Vec<String>,
}

/// 策略风控适配器
pub struct StrategyRiskAdapter {
    /// 适配器基础功能
    base: AdapterBase,
    /// 策略特定配置
    config: StrategyAdapterConfig,
    /// 策略历史记录
    strategy_history: Arc<RwLock<HashMap<String, Vec<StrategyHistoryRecord>>>>,
    /// 当前活跃策略
    active_strategies: Arc<RwLock<HashMap<String, StrategyType>>>,
    /// 策略切换历史
    switch_history: Arc<RwLock<Vec<StrategySwitchRecord>>>,
    /// 策略性能缓存
    performance_cache: Arc<RwLock<HashMap<String, (StrategyPerformanceAnalysis, Instant)>>>,
}

/// 策略切换记录
#[derive(Debug, Clone)]
struct StrategySwitchRecord {
    from_strategy: StrategyType,
    to_strategy: StrategyType,
    timestamp: DateTime<Utc>,
    reason: String,
    success: bool,
}

impl StrategyRiskAdapter {
    /// 创建新的策略风控适配器
    pub async fn new(
        risk_engine: Arc<dyn RiskEngine>,
        cache_service: Arc<dyn CacheService>,
        metrics_collector: Arc<dyn MetricsCollector>,
        config: StrategyAdapterConfig,
    ) -> SigmaXResult<Self> {
        info!("Creating StrategyRiskAdapter with strategy-specific optimization");
        
        let base_config = AdapterConfig {
            enable_cache: true,
            cache_ttl: Duration::from_secs(600), // 10分钟缓存
            enable_metrics: true,
            timeout: Duration::from_millis(2000), // 2秒超时
            retry_attempts: 2,
        };
        
        let base = AdapterBase::new(
            risk_engine,
            cache_service,
            metrics_collector,
            EngineType::Live, // 策略适配器使用Live引擎类型
            base_config,
        );
        
        let adapter = Self {
            base,
            config: config.clone(),
            strategy_history: Arc::new(RwLock::new(HashMap::new())),
            active_strategies: Arc::new(RwLock::new(HashMap::new())),
            switch_history: Arc::new(RwLock::new(Vec::new())),
            performance_cache: Arc::new(RwLock::new(HashMap::new())),
        };
        
        // 启动后台任务
        adapter.start_background_tasks().await;
        
        info!("StrategyRiskAdapter created successfully");
        Ok(adapter)
    }
    
    /// 策略专用风控检查
    pub async fn check_strategy_risk(
        &self,
        order: &Order,
        strategy_type: &StrategyType,
        strategy_id: &str,
    ) -> SigmaXResult<StrategyRiskResult> {
        let start_time = Instant::now();
        
        debug!("Starting strategy risk check: strategy_type={}, strategy_id={}", strategy_type, strategy_id);
        
        // 执行基础风控检查
        let base_result = self.base.risk_engine().check_order_risk(order, Some(&strategy_type.to_string())).await?;
        
        // 执行策略专用检查
        let mut strategy_checks = Vec::new();
        
        if self.config.enable_strategy_specific_checks {
            strategy_checks.extend(self.perform_strategy_specific_checks(order, strategy_type, strategy_id).await);
        }
        
        // 性能分析
        let performance_analysis = if self.config.enable_performance_analysis {
            self.analyze_strategy_performance(strategy_id, strategy_type).await
        } else {
            None
        };
        
        // 生成智能建议
        let suggestions = if self.config.enable_smart_suggestions {
            self.generate_smart_suggestions(strategy_type, strategy_id, &strategy_checks, &performance_analysis).await
        } else {
            Vec::new()
        };
        
        // 计算综合风险分数
        let risk_score = self.calculate_strategy_risk_score(&base_result, &strategy_checks, &performance_analysis);
        
        let result = StrategyRiskResult {
            passed: base_result.passed && strategy_checks.iter().all(|c| c.passed),
            strategy_type: strategy_type.clone(),
            risk_score,
            strategy_checks,
            performance_analysis,
            suggestions,
            timestamp: Utc::now(),
        };
        
        // 记录策略检查指标
        self.record_strategy_metrics(strategy_type, start_time.elapsed(), &result).await;
        
        debug!("Strategy risk check completed: passed={}, score={:.2}", result.passed, result.risk_score);
        
        Ok(result)
    }
    
    /// 策略切换风险评估
    pub async fn evaluate_strategy_switch(
        &self,
        from_strategy: &StrategyType,
        to_strategy: &StrategyType,
        strategy_id: &str,
    ) -> SigmaXResult<StrategySwitchEvaluation> {
        info!("Evaluating strategy switch: {} -> {} for {}", from_strategy, to_strategy, strategy_id);
        
        // 检查切换冷却时间
        if !self.check_switch_cooldown(strategy_id).await {
            return Ok(StrategySwitchEvaluation {
                recommended: false,
                risk_score: 0.8,
                reason: "策略切换冷却时间未到".to_string(),
                suggestions: vec![
                    StrategySuggestion {
                        suggestion_type: SuggestionType::RiskControl,
                        message: format!("建议等待{}分钟后再进行策略切换", self.config.switch_cooldown_minutes),
                        priority: SuggestionPriority::Medium,
                        expected_impact: "避免频繁切换导致的风险".to_string(),
                    }
                ],
            });
        }
        
        // 分析当前策略性能
        let current_performance = self.analyze_strategy_performance(strategy_id, from_strategy).await;
        
        // 分析目标策略历史性能
        let target_performance = self.get_strategy_historical_performance(to_strategy).await;
        
        // 评估切换风险
        let switch_risk = self.calculate_switch_risk(from_strategy, to_strategy, &current_performance, &target_performance);
        
        // 生成切换建议
        let suggestions = self.generate_switch_suggestions(from_strategy, to_strategy, &current_performance, &target_performance);
        
        Ok(StrategySwitchEvaluation {
            recommended: switch_risk < 0.5,
            risk_score: switch_risk,
            reason: self.generate_switch_reason(switch_risk, &current_performance, &target_performance),
            suggestions,
        })
    }
    
    /// 执行策略专用检查
    async fn perform_strategy_specific_checks(
        &self,
        order: &Order,
        strategy_type: &StrategyType,
        strategy_id: &str,
    ) -> Vec<StrategyCheckDetail> {
        let mut checks = Vec::new();
        
        match strategy_type {
            StrategyType::Grid => {
                checks.extend(self.check_grid_strategy_risks(order, strategy_id).await);
            }
            StrategyType::DCA => {
                checks.extend(self.check_dca_strategy_risks(order, strategy_id).await);
            }
            StrategyType::Momentum => {
                checks.extend(self.check_momentum_strategy_risks(order, strategy_id).await);
            }
            StrategyType::MeanReversion => {
                checks.extend(self.check_mean_reversion_strategy_risks(order, strategy_id).await);
            }
            StrategyType::Arbitrage => {
                checks.extend(self.check_arbitrage_strategy_risks(order, strategy_id).await);
            }
            StrategyType::Custom(_) => {
                checks.extend(self.check_custom_strategy_risks(order, strategy_id).await);
            }
        }
        
        checks
    }
    
    /// 网格策略风险检查
    async fn check_grid_strategy_risks(&self, order: &Order, _strategy_id: &str) -> Vec<StrategyCheckDetail> {
        let mut checks = Vec::new();
        
        // 检查网格间距
        checks.push(StrategyCheckDetail {
            check_name: "grid_spacing".to_string(),
            passed: true, // 简化实现
            message: "网格间距检查通过".to_string(),
            risk_level: StrategyRiskLevel::Safe,
            recommended_action: None,
        });
        
        // 检查网格数量
        checks.push(StrategyCheckDetail {
            check_name: "grid_count".to_string(),
            passed: true,
            message: "网格数量合理".to_string(),
            risk_level: StrategyRiskLevel::Safe,
            recommended_action: None,
        });
        
        checks
    }
    
    /// DCA策略风险检查
    async fn check_dca_strategy_risks(&self, order: &Order, _strategy_id: &str) -> Vec<StrategyCheckDetail> {
        let mut checks = Vec::new();
        
        // 检查投资间隔
        checks.push(StrategyCheckDetail {
            check_name: "dca_interval".to_string(),
            passed: true,
            message: "DCA投资间隔合理".to_string(),
            risk_level: StrategyRiskLevel::Safe,
            recommended_action: None,
        });
        
        checks
    }
    
    /// 动量策略风险检查
    async fn check_momentum_strategy_risks(&self, order: &Order, _strategy_id: &str) -> Vec<StrategyCheckDetail> {
        let mut checks = Vec::new();
        
        // 检查动量指标
        checks.push(StrategyCheckDetail {
            check_name: "momentum_signal".to_string(),
            passed: true,
            message: "动量信号强度适中".to_string(),
            risk_level: StrategyRiskLevel::Safe,
            recommended_action: None,
        });
        
        checks
    }
    
    /// 均值回归策略风险检查
    async fn check_mean_reversion_strategy_risks(&self, order: &Order, _strategy_id: &str) -> Vec<StrategyCheckDetail> {
        let mut checks = Vec::new();
        
        // 检查偏离程度
        checks.push(StrategyCheckDetail {
            check_name: "mean_deviation".to_string(),
            passed: true,
            message: "价格偏离均值程度合理".to_string(),
            risk_level: StrategyRiskLevel::Safe,
            recommended_action: None,
        });
        
        checks
    }
    
    /// 套利策略风险检查
    async fn check_arbitrage_strategy_risks(&self, order: &Order, _strategy_id: &str) -> Vec<StrategyCheckDetail> {
        let mut checks = Vec::new();
        
        // 检查价差
        checks.push(StrategyCheckDetail {
            check_name: "price_spread".to_string(),
            passed: true,
            message: "套利价差足够".to_string(),
            risk_level: StrategyRiskLevel::Safe,
            recommended_action: None,
        });
        
        checks
    }
    
    /// 自定义策略风险检查
    async fn check_custom_strategy_risks(&self, order: &Order, _strategy_id: &str) -> Vec<StrategyCheckDetail> {
        let mut checks = Vec::new();
        
        // 通用检查
        checks.push(StrategyCheckDetail {
            check_name: "custom_strategy_check".to_string(),
            passed: true,
            message: "自定义策略检查通过".to_string(),
            risk_level: StrategyRiskLevel::Safe,
            recommended_action: None,
        });
        
        checks
    }
    
    /// 分析策略性能
    async fn analyze_strategy_performance(
        &self,
        strategy_id: &str,
        strategy_type: &StrategyType,
    ) -> Option<StrategyPerformanceAnalysis> {
        // 检查缓存
        {
            let cache = self.performance_cache.read().await;
            if let Some((analysis, timestamp)) = cache.get(strategy_id) {
                if timestamp.elapsed() < Duration::from_secs(3600) { // 1小时缓存
                    return Some(analysis.clone());
                }
            }
        }
        
        // 模拟性能分析 (实际实现中会从历史数据计算)
        let analysis = StrategyPerformanceAnalysis {
            total_return: 0.15, // 15%收益
            sharpe_ratio: 1.2,
            max_drawdown: 0.08, // 8%最大回撤
            win_rate: 0.65, // 65%胜率
            avg_holding_time_hours: 24.0,
            risk_adjusted_return: 0.12,
        };
        
        // 缓存结果
        {
            let mut cache = self.performance_cache.write().await;
            cache.insert(strategy_id.to_string(), (analysis.clone(), Instant::now()));
        }
        
        Some(analysis)
    }
    
    /// 生成智能建议
    async fn generate_smart_suggestions(
        &self,
        strategy_type: &StrategyType,
        strategy_id: &str,
        checks: &[StrategyCheckDetail],
        performance: &Option<StrategyPerformanceAnalysis>,
    ) -> Vec<StrategySuggestion> {
        let mut suggestions = Vec::new();
        
        // 基于检查结果的建议
        for check in checks {
            if !check.passed {
                suggestions.push(StrategySuggestion {
                    suggestion_type: SuggestionType::RiskControl,
                    message: format!("建议关注{}检查失败的问题", check.check_name),
                    priority: match check.risk_level {
                        StrategyRiskLevel::Danger => SuggestionPriority::Critical,
                        StrategyRiskLevel::Warning => SuggestionPriority::High,
                        StrategyRiskLevel::Caution => SuggestionPriority::Medium,
                        StrategyRiskLevel::Safe => SuggestionPriority::Low,
                    },
                    expected_impact: "降低策略风险".to_string(),
                });
            }
        }
        
        // 基于性能分析的建议
        if let Some(perf) = performance {
            if perf.sharpe_ratio < 1.0 {
                suggestions.push(StrategySuggestion {
                    suggestion_type: SuggestionType::PerformanceOptimization,
                    message: "夏普比率偏低，建议优化风险收益比".to_string(),
                    priority: SuggestionPriority::Medium,
                    expected_impact: "提高风险调整收益".to_string(),
                });
            }
            
            if perf.max_drawdown > 0.15 {
                suggestions.push(StrategySuggestion {
                    suggestion_type: SuggestionType::RiskControl,
                    message: "最大回撤过大，建议加强风险控制".to_string(),
                    priority: SuggestionPriority::High,
                    expected_impact: "减少最大回撤".to_string(),
                });
            }
        }
        
        suggestions
    }
    
    /// 计算策略风险分数
    fn calculate_strategy_risk_score(
        &self,
        base_result: &sigmax_core::RiskCheckResult,
        strategy_checks: &[StrategyCheckDetail],
        performance: &Option<StrategyPerformanceAnalysis>,
    ) -> f64 {
        let mut risk_score = base_result.risk_score.unwrap_or(0.0);

        // 策略检查风险
        let failed_checks = strategy_checks.iter().filter(|c| !c.passed).count();
        risk_score += failed_checks as f64 * 0.1;

        // 性能风险
        if let Some(perf) = performance {
            if perf.sharpe_ratio < 0.5 {
                risk_score += 0.2;
            }
            if perf.max_drawdown > 0.2 {
                risk_score += 0.3;
            }
        }

        risk_score.min(1.0)
    }
    
    /// 检查策略切换冷却时间
    async fn check_switch_cooldown(&self, strategy_id: &str) -> bool {
        let switch_history = self.switch_history.read().await;
        let cooldown_duration = Duration::from_secs(self.config.switch_cooldown_minutes as u64 * 60);
        
        // 查找最近的切换记录
        for record in switch_history.iter().rev() {
            if Utc::now().signed_duration_since(record.timestamp).to_std().unwrap_or(Duration::MAX) < cooldown_duration {
                return false;
            }
        }
        
        true
    }
    
    /// 获取策略历史性能
    async fn get_strategy_historical_performance(&self, strategy_type: &StrategyType) -> Option<StrategyPerformanceAnalysis> {
        // 模拟历史性能数据
        Some(StrategyPerformanceAnalysis {
            total_return: 0.12,
            sharpe_ratio: 1.1,
            max_drawdown: 0.06,
            win_rate: 0.68,
            avg_holding_time_hours: 18.0,
            risk_adjusted_return: 0.10,
        })
    }
    
    /// 计算切换风险
    fn calculate_switch_risk(
        &self,
        _from_strategy: &StrategyType,
        _to_strategy: &StrategyType,
        current_perf: &Option<StrategyPerformanceAnalysis>,
        target_perf: &Option<StrategyPerformanceAnalysis>,
    ) -> f64 {
        // 简化的切换风险计算
        match (current_perf, target_perf) {
            (Some(current), Some(target)) => {
                if target.sharpe_ratio > current.sharpe_ratio {
                    0.3 // 低风险
                } else {
                    0.7 // 高风险
                }
            }
            _ => 0.5, // 中等风险
        }
    }
    
    /// 生成切换建议
    fn generate_switch_suggestions(
        &self,
        from_strategy: &StrategyType,
        to_strategy: &StrategyType,
        _current_perf: &Option<StrategyPerformanceAnalysis>,
        _target_perf: &Option<StrategyPerformanceAnalysis>,
    ) -> Vec<StrategySuggestion> {
        vec![
            StrategySuggestion {
                suggestion_type: SuggestionType::StrategySwitch,
                message: format!("从{}切换到{}的建议", from_strategy, to_strategy),
                priority: SuggestionPriority::Medium,
                expected_impact: "优化策略性能".to_string(),
            }
        ]
    }
    
    /// 生成切换原因
    fn generate_switch_reason(
        &self,
        risk_score: f64,
        _current_perf: &Option<StrategyPerformanceAnalysis>,
        _target_perf: &Option<StrategyPerformanceAnalysis>,
    ) -> String {
        if risk_score < 0.3 {
            "目标策略表现更优，建议切换".to_string()
        } else if risk_score < 0.7 {
            "切换风险适中，可以考虑".to_string()
        } else {
            "切换风险较高，建议谨慎".to_string()
        }
    }
    
    /// 记录策略指标
    async fn record_strategy_metrics(
        &self,
        strategy_type: &StrategyType,
        duration: Duration,
        result: &StrategyRiskResult,
    ) {
        if self.base.config().enable_metrics {
            self.base.metrics_collector().record_risk_check(result.passed).await;
            self.base.metrics_collector().record_latency(&format!("strategy_{}", strategy_type), duration).await;
        }
    }
    
    /// 启动后台任务
    async fn start_background_tasks(&self) {
        let strategy_history = self.strategy_history.clone();
        let performance_cache = self.performance_cache.clone();
        let retention_days = self.config.history_retention_days;
        
        // 历史数据清理任务
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(86400)); // 每天清理一次
            loop {
                interval.tick().await;
                
                let cutoff_time = Utc::now() - chrono::Duration::days(retention_days as i64);
                let mut history = strategy_history.write().await;
                
                for records in history.values_mut() {
                    records.retain(|record| record.timestamp > cutoff_time);
                }
                
                debug!("Cleaned strategy history older than {} days", retention_days);
            }
        });
        
        // 性能缓存清理任务
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(3600)); // 每小时清理一次
            loop {
                interval.tick().await;
                
                let mut cache = performance_cache.write().await;
                cache.retain(|_, (_, timestamp)| timestamp.elapsed() < Duration::from_secs(3600));
                
                debug!("Cleaned performance cache, remaining entries: {}", cache.len());
            }
        });
    }
}

/// 策略切换评估结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrategySwitchEvaluation {
    /// 是否推荐切换
    pub recommended: bool,
    /// 切换风险分数
    pub risk_score: f64,
    /// 切换原因
    pub reason: String,
    /// 建议
    pub suggestions: Vec<StrategySuggestion>,
}

#[async_trait]
impl EngineRiskAdapter for StrategyRiskAdapter {
    async fn check_order_risk(&self, order: &Order) -> SigmaXResult<bool> {
        // 默认使用Grid策略检查
        let result = self.check_strategy_risk(order, &StrategyType::Grid, "default").await?;
        Ok(result.passed)
    }
    
    async fn check_position_risk(&self, balances: &[Balance]) -> SigmaXResult<bool> {
        // 策略适配器主要关注订单风控，持仓检查使用基础实现
        let result = self.base.risk_engine().check_position_risk(balances, Some("strategy")).await?;
        Ok(result.passed)
    }
    
    fn engine_type(&self) -> EngineType {
        EngineType::Live
    }
    
    async fn get_metrics(&self) -> SigmaXResult<AdapterMetrics> {
        Ok(AdapterMetrics {
            engine_type: EngineType::Live,
            cache_hit_rate: 0.75, // 策略缓存命中率
            avg_latency_ms: 80.0, // 策略检查平均延迟
            throughput_rps: 200.0, // 策略检查吞吐量
            error_rate: 0.005, // 策略检查错误率
        })
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_strategy_type_display() {
        assert_eq!(StrategyType::Grid.to_string(), "Grid");
        assert_eq!(StrategyType::Custom("MyStrategy".to_string()).to_string(), "Custom(MyStrategy)");
    }
    
    #[test]
    fn test_strategy_adapter_config_default() {
        let config = StrategyAdapterConfig::default();
        assert!(config.enable_strategy_specific_checks);
        assert!(config.enable_switch_evaluation);
        assert_eq!(config.history_retention_days, 30);
        assert_eq!(config.max_concurrent_strategies, 5);
    }
}
