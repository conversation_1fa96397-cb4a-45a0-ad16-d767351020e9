//! 自动响应规则引擎
//! 
//! 定义风险响应规则、触发条件和规则匹配逻辑

use crate::alert_system::{RiskAlert, AlertSeverity, AlertType};
use crate::response_actions::AutoResponseAction;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc, Timelike};
use uuid::Uuid;
use std::collections::HashMap;

/// 自动响应规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoResponseRule {
    /// 规则ID
    pub rule_id: Uuid,
    /// 规则名称
    pub name: String,
    /// 触发条件
    pub trigger_condition: TriggerCondition,
    /// 响应动作
    pub response_actions: Vec<AutoResponseAction>,
    /// 是否启用
    pub enabled: bool,
    /// 冷却期（秒）- 避免重复执行
    pub cooldown_seconds: u64,
    /// 最大执行次数（防止无限循环）
    pub max_executions: Option<u32>,
    /// 当前执行次数
    pub execution_count: u32,
    /// 最后执行时间
    pub last_executed: Option<DateTime<Utc>>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// 触发条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TriggerCondition {
    /// 预警类型匹配
    pub alert_types: Vec<AlertType>,
    /// 最小严重程度
    pub min_severity: AlertSeverity,
    /// 阈值条件
    pub threshold_conditions: Vec<ThresholdCondition>,
    /// 时间条件
    pub time_conditions: Option<TimeCondition>,
}

/// 阈值条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThresholdCondition {
    /// 指标名称
    pub metric_name: String,
    /// 比较操作符
    pub operator: ComparisonOperator,
    /// 阈值
    pub threshold: f64,
}

/// 比较操作符
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComparisonOperator {
    GreaterThan,
    LessThan,
    Equal,
    GreaterThanOrEqual,
    LessThanOrEqual,
}

/// 时间条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeCondition {
    /// 允许执行的时间段
    pub allowed_hours: Vec<u32>,
    /// 禁止执行的日期
    pub blackout_dates: Vec<String>,
}

impl AutoResponseRule {
    /// 创建新的响应规则
    pub fn new(
        name: String,
        trigger_condition: TriggerCondition,
        response_actions: Vec<AutoResponseAction>,
    ) -> Self {
        Self {
            rule_id: Uuid::new_v4(),
            name,
            trigger_condition,
            response_actions,
            enabled: true,
            cooldown_seconds: 60,
            max_executions: None,
            execution_count: 0,
            last_executed: None,
            created_at: Utc::now(),
        }
    }

    /// 检查规则是否可以执行
    pub fn can_execute(&self) -> bool {
        if !self.enabled {
            return false;
        }

        // 检查执行次数限制
        if let Some(max_executions) = self.max_executions {
            if self.execution_count >= max_executions {
                return false;
            }
        }

        // 检查冷却期
        if let Some(last_executed) = self.last_executed {
            let now = Utc::now();
            let cooldown_duration = chrono::Duration::seconds(self.cooldown_seconds as i64);
            if now - last_executed < cooldown_duration {
                return false;
            }
        }

        true
    }

    /// 检查规则是否匹配给定的预警
    pub fn matches_alert(&self, alert: &RiskAlert) -> bool {
        self.trigger_condition.matches_alert(alert)
    }

    /// 记录执行
    pub fn record_execution(&mut self) {
        self.execution_count += 1;
        self.last_executed = Some(Utc::now());
    }

    /// 重置执行计数
    pub fn reset_execution_count(&mut self) {
        self.execution_count = 0;
        self.last_executed = None;
    }
}

impl TriggerCondition {
    /// 检查触发条件是否匹配预警
    pub fn matches_alert(&self, alert: &RiskAlert) -> bool {
        // 检查预警类型
        if !self.alert_types.is_empty() && !self.alert_types.contains(&alert.alert_type) {
            return false;
        }

        // 检查严重程度
        if !self.severity_matches(&alert.severity) {
            return false;
        }

        // 检查阈值条件
        if !self.check_threshold_conditions(alert) {
            return false;
        }

        // 检查时间条件
        if let Some(time_condition) = &self.time_conditions {
            if !time_condition.check_time_condition() {
                return false;
            }
        }

        true
    }

    /// 检查严重程度是否匹配
    fn severity_matches(&self, alert_severity: &AlertSeverity) -> bool {
        match (&self.min_severity, alert_severity) {
            (AlertSeverity::Info, _) => true,
            (AlertSeverity::Warning, AlertSeverity::Warning | AlertSeverity::Critical | AlertSeverity::Emergency) => true,
            (AlertSeverity::Critical, AlertSeverity::Critical | AlertSeverity::Emergency) => true,
            (AlertSeverity::Emergency, AlertSeverity::Emergency) => true,
            _ => false,
        }
    }

    /// 检查阈值条件
    fn check_threshold_conditions(&self, alert: &RiskAlert) -> bool {
        if self.threshold_conditions.is_empty() {
            return true;
        }

        // 创建指标映射
        let mut metrics = HashMap::new();
        metrics.insert("current_value".to_string(), alert.current_value);
        metrics.insert("threshold_value".to_string(), alert.threshold_value);
        metrics.insert("breach_percentage".to_string(), alert.breach_percentage);

        // 检查所有阈值条件
        self.threshold_conditions.iter().all(|condition| {
            if let Some(&value) = metrics.get(&condition.metric_name) {
                condition.operator.compare(value, condition.threshold)
            } else {
                false
            }
        })
    }
}

impl ComparisonOperator {
    /// 执行比较操作
    pub fn compare(&self, left: f64, right: f64) -> bool {
        match self {
            ComparisonOperator::GreaterThan => left > right,
            ComparisonOperator::LessThan => left < right,
            ComparisonOperator::Equal => (left - right).abs() < f64::EPSILON,
            ComparisonOperator::GreaterThanOrEqual => left >= right,
            ComparisonOperator::LessThanOrEqual => left <= right,
        }
    }
}

impl TimeCondition {
    /// 检查时间条件
    pub fn check_time_condition(&self) -> bool {
        let now = Utc::now();
        
        // 检查允许的小时
        if !self.allowed_hours.is_empty() {
            let current_hour = now.hour();
            if !self.allowed_hours.contains(&current_hour) {
                return false;
            }
        }
        
        // 检查黑名单日期
        let current_date = now.format("%Y-%m-%d").to_string();
        if self.blackout_dates.contains(&current_date) {
            return false;
        }
        
        true
    }

    /// 创建工作时间条件（9-17点）
    pub fn business_hours() -> Self {
        Self {
            allowed_hours: (9..=17).collect(),
            blackout_dates: vec![],
        }
    }

    /// 创建24小时条件
    pub fn always_active() -> Self {
        Self {
            allowed_hours: vec![],
            blackout_dates: vec![],
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_rule_creation() {
        let trigger = TriggerCondition {
            alert_types: vec![AlertType::VarBreach],
            min_severity: AlertSeverity::Warning,
            threshold_conditions: vec![],
            time_conditions: None,
        };

        let rule = AutoResponseRule::new(
            "Test Rule".to_string(),
            trigger,
            vec![],
        );

        assert_eq!(rule.name, "Test Rule");
        assert!(rule.enabled);
        assert_eq!(rule.execution_count, 0);
    }

    #[test]
    fn test_comparison_operator() {
        assert!(ComparisonOperator::GreaterThan.compare(5.0, 3.0));
        assert!(!ComparisonOperator::GreaterThan.compare(3.0, 5.0));
        assert!(ComparisonOperator::Equal.compare(5.0, 5.0));
    }

    #[test]
    fn test_time_condition() {
        let business_hours = TimeCondition::business_hours();
        assert_eq!(business_hours.allowed_hours, (9..=17).collect::<Vec<_>>());
        
        let always_active = TimeCondition::always_active();
        assert!(always_active.allowed_hours.is_empty());
    }
}
