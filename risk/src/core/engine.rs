//! 统一风险检查引擎

use async_trait::async_trait;
use std::sync::Arc;
use tracing::{debug, info, warn};
use sigmax_core::{Order, Balance, SigmaXResult};

use super::types::{
    RiskCheckRequest, RiskCheckResult, RiskCheckType, RiskLevel,
    RiskRule, RiskMetrics, PortfolioInfo
};
use sigmax_core::config::risk::{RiskManagementConfig, RiskConfigParameters};

/// 风险检查引擎特征
#[async_trait]
pub trait RiskEngine: Send + Sync {
    /// 执行风险检查
    async fn check_risk(&self, request: RiskCheckRequest) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查订单风险
    async fn check_order_risk(&self, order: &Order, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查持仓风险
    async fn check_position_risk(&self, balances: &[Balance], strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;
    
    /// 检查投资组合风险
    async fn check_portfolio_risk(&self, portfolio: &PortfolioInfo, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult>;
    
    /// 获取实时风险指标
    async fn get_risk_metrics(&self) -> SigmaXResult<RiskMetrics>;
    
    /// 更新风险参数
    async fn update_risk_parameters(&self, parameters: RiskConfigParameters) -> SigmaXResult<()>;
}

/// 规则管理器特征
#[async_trait]
pub trait RuleManager: Send + Sync {
    /// 创建规则
    async fn create_rule(&self, rule: RiskRule) -> SigmaXResult<uuid::Uuid>;
    
    /// 更新规则
    async fn update_rule(&self, rule: RiskRule) -> SigmaXResult<()>;
    
    /// 删除规则
    async fn delete_rule(&self, rule_id: uuid::Uuid) -> SigmaXResult<()>;
    
    /// 获取规则
    async fn get_rule(&self, rule_id: uuid::Uuid) -> SigmaXResult<Option<RiskRule>>;
    
    /// 列出规则
    async fn list_rules(&self, strategy_type: Option<&str>) -> SigmaXResult<Vec<RiskRule>>;
    
    /// 启用/禁用规则
    async fn toggle_rule(&self, rule_id: uuid::Uuid, enabled: bool) -> SigmaXResult<()>;
}

/// 风险监控器特征
#[async_trait]
pub trait RiskMonitor: Send + Sync {
    /// 获取实时风险指标
    async fn get_real_time_metrics(&self) -> SigmaXResult<RiskMetrics>;
    
    /// 获取风险趋势
    async fn get_risk_trends(&self, period: chrono::Duration) -> SigmaXResult<Vec<RiskMetrics>>;
    
    /// 订阅风险预警
    async fn subscribe_alerts(&self) -> SigmaXResult<tokio::sync::mpsc::Receiver<super::types::RiskAlert>>;
    
    /// 获取预警历史
    async fn get_alert_history(&self, limit: usize) -> SigmaXResult<Vec<super::types::RiskAlert>>;
}

/// 统一风险管理引擎实现
pub struct UnifiedRiskEngine {
    /// 规则管理器
    rule_manager: Arc<dyn RuleManager>,
    /// 风险监控器
    monitor: Arc<dyn RiskMonitor>,
    /// 风险参数
    parameters: Arc<tokio::sync::RwLock<RiskConfigParameters>>,
}

impl UnifiedRiskEngine {
    /// 创建新的风险引擎
    pub fn new(
        rule_manager: Arc<dyn RuleManager>,
        monitor: Arc<dyn RiskMonitor>,
    ) -> Self {
        Self {
            rule_manager,
            monitor,
            parameters: Arc::new(tokio::sync::RwLock::new(RiskConfigParameters::default())),
        }
    }
    
    /// 执行规则检查
    async fn execute_rules(&self, request: &RiskCheckRequest) -> SigmaXResult<RiskCheckResult> {
        let start_time = std::time::Instant::now();
        
        // 获取适用的规则
        let rules = self.rule_manager.list_rules(request.strategy_type.as_deref()).await?;
        let enabled_rules: Vec<_> = rules.into_iter().filter(|r| r.enabled).collect();
        
        debug!("执行风险检查，适用规则数量: {}", enabled_rules.len());
        
        let mut warnings = Vec::new();
        let mut suggestions = Vec::new();
        let mut violated_rules = Vec::new();
        let mut max_risk_score = 0.0;
        let mut max_risk_level = RiskLevel::Low;
        
        // 执行每个规则
        for rule in enabled_rules {
            match self.execute_single_rule(&rule, request).await {
                Ok(result) => {
                    if !result.passed {
                        violated_rules.push(rule.name.clone());
                        if result.risk_score > max_risk_score {
                            max_risk_score = result.risk_score;
                            max_risk_level = result.risk_level.clone();
                        }
                    }
                    warnings.extend(result.warnings);
                    suggestions.extend(result.suggestions);
                }
                Err(e) => {
                    warn!("规则 {} 执行失败: {}", rule.name, e);
                    violated_rules.push(format!("规则执行错误: {}", rule.name));
                }
            }
        }
        
        let execution_time = start_time.elapsed().as_millis() as u64;
        let passed = violated_rules.is_empty();
        
        let now = chrono::Utc::now();
        Ok(RiskCheckResult {
            passed,
            risk_level: max_risk_level,
            risk_score: max_risk_score,
            check_type: request.check_type.clone(),
            message: if passed {
                Some("风险检查通过".to_string())
            } else {
                Some(format!("风险检查失败，违反 {} 个规则", violated_rules.len()))
            },
            warnings,
            suggestions,
            violated_rules,
            checked_at: now,
            timestamp: now,
            execution_time_ms: execution_time,
        })
    }
    
    /// 执行单个规则
    async fn execute_single_rule(&self, rule: &RiskRule, request: &RiskCheckRequest) -> SigmaXResult<RiskCheckResult> {
        // 这里应该根据规则类型执行具体的检查逻辑
        // 暂时返回通过的结果
        Ok(RiskCheckResult::passed(request.check_type.clone()))
    }
}

#[async_trait]
impl RiskEngine for UnifiedRiskEngine {
    async fn check_risk(&self, request: RiskCheckRequest) -> SigmaXResult<RiskCheckResult> {
        info!("执行统一风险检查: {:?}", request.check_type);
        self.execute_rules(&request).await
    }
    
    async fn check_order_risk(&self, order: &Order, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        let request = RiskCheckRequest {
            check_type: RiskCheckType::Order,
            order: Some(order.clone()),
            balances: None,
            portfolio: None,
            strategy_type: strategy_type.map(|s| s.to_string()),
            parameters: std::collections::HashMap::new(),
        };
        
        self.check_risk(request).await
    }
    
    async fn check_position_risk(&self, balances: &[Balance], strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        let request = RiskCheckRequest {
            check_type: RiskCheckType::Position,
            order: None,
            balances: Some(balances.to_vec()),
            portfolio: None,
            strategy_type: strategy_type.map(|s| s.to_string()),
            parameters: std::collections::HashMap::new(),
        };
        
        self.check_risk(request).await
    }
    
    async fn check_portfolio_risk(&self, portfolio: &PortfolioInfo, strategy_type: Option<&str>) -> SigmaXResult<RiskCheckResult> {
        let request = RiskCheckRequest {
            check_type: RiskCheckType::Portfolio,
            order: None,
            balances: None,
            portfolio: Some(portfolio.clone()),
            strategy_type: strategy_type.map(|s| s.to_string()),
            parameters: std::collections::HashMap::new(),
        };
        
        self.check_risk(request).await
    }
    
    async fn get_risk_metrics(&self) -> SigmaXResult<RiskMetrics> {
        self.monitor.get_real_time_metrics().await
    }
    
    async fn update_risk_parameters(&self, parameters: RiskConfigParameters) -> SigmaXResult<()> {
        let mut params = self.parameters.write().await;
        *params = parameters;
        info!("风险参数已更新");
        Ok(())
    }
}
