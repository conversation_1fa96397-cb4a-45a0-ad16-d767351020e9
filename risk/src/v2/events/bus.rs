//! V2风控事件总线实现
//!
//! 提供异步事件发布和订阅功能

use async_trait::async_trait;
use sigmax_core::SigmaXResult;
use super::super::core::{RiskLevel, RuleViolation};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use std::sync::Arc;
use tokio::sync::broadcast;

/// 风控事件类型
#[derive(Debug, Clone)]
pub enum RiskEvent {
    /// 违规检测事件
    ViolationDetected {
        request_id: Uuid,
        risk_level: RiskLevel,
        violations: Vec<RuleViolation>,
        timestamp: DateTime<Utc>,
    },
    /// 风险预警事件
    RiskWarning {
        message: String,
        risk_level: RiskLevel,
        timestamp: DateTime<Utc>,
    },
    /// 性能指标事件
    PerformanceMetric {
        metric_name: String,
        value: f64,
        timestamp: DateTime<Utc>,
    },
}

/// 事件订阅者特征
#[async_trait]
pub trait EventSubscriber: Send + Sync {
    /// 处理事件
    async fn handle_event(&self, event: &RiskEvent) -> SigmaXResult<()>;
}

/// 风控事件总线
pub struct RiskEventBus {
    /// 事件广播通道
    sender: broadcast::Sender<RiskEvent>,
    /// 订阅者列表
    subscribers: Arc<tokio::sync::RwLock<Vec<Arc<dyn EventSubscriber>>>>,
}

impl RiskEventBus {
    /// 创建新的事件总线
    pub fn new(capacity: usize) -> Self {
        let (sender, _) = broadcast::channel(capacity);
        Self {
            sender,
            subscribers: Arc::new(tokio::sync::RwLock::new(Vec::new())),
        }
    }

    /// 发送事件
    pub async fn emit(&self, event: RiskEvent) -> SigmaXResult<()> {
        // 广播事件
        if let Err(e) = self.sender.send(event.clone()) {
            tracing::warn!("事件广播失败: {}", e);
        }

        // 通知订阅者
        let subscribers = self.subscribers.read().await;
        for subscriber in subscribers.iter() {
            if let Err(e) = subscriber.handle_event(&event).await {
                tracing::error!("事件处理失败: {}", e);
            }
        }

        Ok(())
    }

    /// 添加订阅者
    pub async fn subscribe(&self, subscriber: Arc<dyn EventSubscriber>) {
        let mut subscribers = self.subscribers.write().await;
        subscribers.push(subscriber);
    }

    /// 创建事件接收器
    pub fn receiver(&self) -> broadcast::Receiver<RiskEvent> {
        self.sender.subscribe()
    }

    /// 获取订阅者数量
    pub async fn subscriber_count(&self) -> usize {
        let subscribers = self.subscribers.read().await;
        subscribers.len()
    }
}

impl Default for RiskEventBus {
    fn default() -> Self {
        Self::new(1000) // 默认容量1000个事件
    }
}