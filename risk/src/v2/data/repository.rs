//! V2风控数据仓库接口
//!
//! 定义统一的数据访问接口

use async_trait::async_trait;
use sigmax_core::SigmaXResult;
use super::super::core::RiskResult;
use rust_decimal::Decimal;
use serde::{Serialize, Deserialize};

/// 交易限制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingLimits {
    /// 最大订单价值
    pub max_order_value: Decimal,
    /// 最大持仓比例
    pub max_position_ratio: Decimal,
    /// 最大日损失比例
    pub max_daily_loss: Decimal,
}

/// 风控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskConfig {
    /// 配置键
    pub key: String,
    /// 配置值
    pub value: serde_json::Value,
    /// 配置描述
    pub description: Option<String>,
}

/// 风控数据仓库特征
#[async_trait]
pub trait RiskRepository: Send + Sync {
    /// 获取交易限制
    async fn get_trading_limits(&self, strategy_id: &str) -> SigmaXResult<TradingLimits>;

    /// 获取风控配置
    async fn get_risk_config(&self, key: &str) -> SigmaXResult<Option<RiskConfig>>;

    /// 保存风控检查结果
    async fn save_risk_check_result(&self, result: &RiskResult) -> SigmaXResult<()>;
}