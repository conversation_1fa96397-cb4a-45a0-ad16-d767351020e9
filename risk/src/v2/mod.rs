//! SigmaX Risk Management V2 - 微内核架构
//!
//! 新一代风控系统，采用微内核 + 可插拔规则的架构设计
//!
//! ## 设计原则
//! - **简单优于复杂**: 核心代码控制在1000行以内
//! - **性能优于灵活性**: 零成本抽象，编译时优化
//! - **单一职责**: 每个组件只负责一个核心功能
//!
//! ## 架构层次
//! - **核心层**: 微内核引擎，负责规则调度和结果聚合
//! - **规则层**: 可插拔的风控规则实现
//! - **数据层**: 统一的数据访问接口，支持缓存和批量操作
//! - **事件层**: 异步事件总线，解耦系统组件
//! - **集成层**: 与现有系统的兼容性桥接

// ============================================================================
// 核心模块
// ============================================================================
pub mod core;
pub mod rules;
pub mod data;
pub mod events;
pub mod integration;

// ============================================================================
// 重新导出主要接口
// ============================================================================

// 核心接口
pub use core::{
    RiskCore, RiskCoreBuilder, RiskRequest, RiskResult, RiskLevel,
    RiskRequestType, RiskContext, RuleViolation, PerformanceMetrics
};

// 规则接口
pub use rules::{
    RiskRule, RuleResult, RuleStatus, RulePriority
};

// 数据接口
pub use data::{
    RiskRepository, RiskCache, TradingLimits, RiskConfig
};

// 事件接口
pub use events::{
    RiskEventBus, RiskEvent, EventSubscriber
};

// 集成接口
pub use integration::{
    CompatRiskManager, V2Bridge
};

// ============================================================================
// 便捷构造函数
// ============================================================================

use sigmax_core::SigmaXResult;
use std::sync::Arc;

/// 创建默认的风控系统实例
pub async fn create_default_risk_system() -> SigmaXResult<RiskCore> {
    RiskCoreBuilder::new()
        .with_default_rules()
        .with_sql_repository()
        .with_memory_cache()
        .with_event_bus()
        .build()
        .await
}

/// 创建兼容现有系统的风控管理器
pub async fn create_compat_manager() -> SigmaXResult<CompatRiskManager> {
    let core = create_default_risk_system().await?;
    Ok(CompatRiskManager::new(Arc::new(core)))
}
