//! 收益风险比率指标计算模块
//! 
//! 实现各种收益风险比率，包括夏普比率、索提诺比率等

use sigmax_core::SigmaXResult;
use rust_decimal::{Decimal, MathematicalOps};
use std::collections::VecDeque;
use serde::{Serialize, Deserialize};

/// 收益风险比率指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RatioMetrics {
    /// 夏普比率
    pub sharpe_ratio: Decimal,
    /// 索提诺比率
    pub sortino_ratio: Decimal,
    /// 卡尔马比率
    pub calmar_ratio: Decimal,
    /// 信息比率
    pub information_ratio: Decimal,
    /// 特雷诺比率
    pub treynor_ratio: Decimal,
    /// 尾部比率
    pub tail_ratio: Decimal,
    /// 收益痛苦比率
    pub gain_pain_ratio: Decimal,
    /// 斯特林比率
    pub sterling_ratio: Decimal,
    /// 伯克比率
    pub burke_ratio: Decimal,
    /// 欧米茄比率
    pub omega_ratio: Decimal,
    /// 卡帕比率
    pub kappa_ratio: Decimal,
    /// 马丁比率
    pub martin_ratio: Decimal,
}

/// 比率指标计算器
pub struct RatioCalculator<'a> {
    returns_history: &'a VecDeque<Decimal>,
    benchmark_returns: &'a VecDeque<Decimal>,
    risk_free_rate: Decimal,
    max_drawdown: Decimal,
    downside_volatility: Decimal,
    volatility: Decimal,
}

impl<'a> RatioCalculator<'a> {
    pub fn new(
        returns_history: &'a VecDeque<Decimal>,
        benchmark_returns: &'a VecDeque<Decimal>,
        risk_free_rate: Decimal,
        max_drawdown: Decimal,
        downside_volatility: Decimal,
        volatility: Decimal,
    ) -> Self {
        Self {
            returns_history,
            benchmark_returns,
            risk_free_rate,
            max_drawdown,
            downside_volatility,
            volatility,
        }
    }

    /// 计算所有比率指标
    pub fn calculate(&self) -> SigmaXResult<RatioMetrics> {
        Ok(RatioMetrics {
            sharpe_ratio: self.calculate_sharpe_ratio()?,
            sortino_ratio: self.calculate_sortino_ratio()?,
            calmar_ratio: self.calculate_calmar_ratio()?,
            information_ratio: self.calculate_information_ratio()?,
            treynor_ratio: self.calculate_treynor_ratio()?,
            tail_ratio: self.calculate_tail_ratio()?,
            gain_pain_ratio: self.calculate_gain_pain_ratio()?,
            sterling_ratio: self.calculate_sterling_ratio()?,
            burke_ratio: self.calculate_burke_ratio()?,
            omega_ratio: self.calculate_omega_ratio()?,
            kappa_ratio: self.calculate_kappa_ratio()?,
            martin_ratio: self.calculate_martin_ratio()?,
        })
    }

    /// 计算夏普比率
    pub fn calculate_sharpe_ratio(&self) -> SigmaXResult<Decimal> {
        if self.returns_history.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let mean_return = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let excess_return = mean_return - self.risk_free_rate / Decimal::from(252);
        
        if self.volatility == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        let annualized_excess = excess_return * Decimal::from(252);
        let annualized_vol = self.volatility * Decimal::from(252).sqrt().unwrap_or(Decimal::from(16));
        
        Ok(annualized_excess / annualized_vol)
    }

    /// 计算索提诺比率
    pub fn calculate_sortino_ratio(&self) -> SigmaXResult<Decimal> {
        if self.returns_history.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let mean_return = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let excess_return = mean_return - self.risk_free_rate / Decimal::from(252);
        
        if self.downside_volatility == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        let annualized_excess = excess_return * Decimal::from(252);
        let annualized_downside_vol = self.downside_volatility * Decimal::from(252).sqrt().unwrap_or(Decimal::from(16));
        
        Ok(annualized_excess / annualized_downside_vol)
    }

    /// 计算卡尔马比率
    pub fn calculate_calmar_ratio(&self) -> SigmaXResult<Decimal> {
        let mean_return = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let annualized_return = mean_return * Decimal::from(252);
        
        if self.max_drawdown == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        Ok(annualized_return / self.max_drawdown)
    }

    /// 计算信息比率
    pub fn calculate_information_ratio(&self) -> SigmaXResult<Decimal> {
        if self.benchmark_returns.is_empty() || self.returns_history.len() != self.benchmark_returns.len() {
            return Ok(Decimal::ZERO);
        }

        let excess_returns: Vec<Decimal> = self.returns_history
            .iter()
            .zip(self.benchmark_returns.iter())
            .map(|(r, b)| r - b)
            .collect();

        if excess_returns.is_empty() {
            return Ok(Decimal::ZERO);
        }

        let mean_excess = excess_returns.iter().sum::<Decimal>() / Decimal::from(excess_returns.len());
        let tracking_error = self.calculate_tracking_error(&excess_returns)?;
        
        if tracking_error == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        Ok(mean_excess * Decimal::from(252) / tracking_error)
    }

    /// 计算特雷诺比率
    pub fn calculate_treynor_ratio(&self) -> SigmaXResult<Decimal> {
        let beta = self.calculate_beta()?;
        
        if beta == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }

        let mean_return = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let excess_return = mean_return - self.risk_free_rate / Decimal::from(252);
        let annualized_excess = excess_return * Decimal::from(252);
        
        Ok(annualized_excess / beta)
    }

    /// 计算尾部比率
    pub fn calculate_tail_ratio(&self) -> SigmaXResult<Decimal> {
        if self.returns_history.len() < 20 {
            return Ok(Decimal::ZERO);
        }

        let mut sorted_returns: Vec<Decimal> = self.returns_history.iter().cloned().collect();
        sorted_returns.sort();
        
        let top_10_percent = sorted_returns.len() / 10;
        let bottom_10_percent = sorted_returns.len() / 10;
        
        if top_10_percent == 0 || bottom_10_percent == 0 {
            return Ok(Decimal::ZERO);
        }
        
        let top_returns: Decimal = sorted_returns[sorted_returns.len() - top_10_percent..].iter().sum();
        let bottom_returns: Decimal = sorted_returns[..bottom_10_percent].iter().sum();
        
        if bottom_returns == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        Ok(top_returns.abs() / bottom_returns.abs())
    }

    /// 计算收益痛苦比率
    pub fn calculate_gain_pain_ratio(&self) -> SigmaXResult<Decimal> {
        let positive_returns: Decimal = self.returns_history
            .iter()
            .filter(|&&r| r > Decimal::ZERO)
            .sum();
            
        let negative_returns: Decimal = self.returns_history
            .iter()
            .filter(|&&r| r < Decimal::ZERO)
            .sum();
        
        if negative_returns == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        Ok(positive_returns / negative_returns.abs())
    }

    /// 计算斯特林比率
    pub fn calculate_sterling_ratio(&self) -> SigmaXResult<Decimal> {
        let mean_return = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let annualized_return = mean_return * Decimal::from(252);
        
        // 使用平均回撤而不是最大回撤
        let average_drawdown = self.calculate_average_drawdown()?;
        
        if average_drawdown == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        Ok(annualized_return / average_drawdown)
    }

    /// 计算伯克比率
    pub fn calculate_burke_ratio(&self) -> SigmaXResult<Decimal> {
        let mean_return = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let annualized_return = mean_return * Decimal::from(252);
        
        // 使用回撤的平方根
        let sqrt_drawdown = self.max_drawdown.sqrt().unwrap_or(Decimal::ZERO);
        
        if sqrt_drawdown == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        Ok(annualized_return / sqrt_drawdown)
    }

    /// 计算欧米茄比率
    pub fn calculate_omega_ratio(&self) -> SigmaXResult<Decimal> {
        let threshold = self.risk_free_rate / Decimal::from(252);
        
        let gains: Decimal = self.returns_history
            .iter()
            .filter(|&&r| r > threshold)
            .map(|&r| r - threshold)
            .sum();
            
        let losses: Decimal = self.returns_history
            .iter()
            .filter(|&&r| r < threshold)
            .map(|&r| threshold - r)
            .sum();
        
        if losses == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        Ok(gains / losses)
    }

    /// 计算卡帕比率
    pub fn calculate_kappa_ratio(&self) -> SigmaXResult<Decimal> {
        // 简化版本的卡帕比率，使用下行偏差
        let mean_return = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let excess_return = mean_return - self.risk_free_rate / Decimal::from(252);
        
        if self.downside_volatility == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        Ok(excess_return / self.downside_volatility)
    }

    /// 计算马丁比率
    pub fn calculate_martin_ratio(&self) -> SigmaXResult<Decimal> {
        let mean_return = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let annualized_return = mean_return * Decimal::from(252);
        
        // 使用溃疡指数作为分母
        let ulcer_index = self.calculate_ulcer_index()?;
        
        if ulcer_index == Decimal::ZERO {
            return Ok(Decimal::ZERO);
        }
        
        Ok(annualized_return / ulcer_index)
    }

    // 辅助方法
    fn calculate_tracking_error(&self, excess_returns: &[Decimal]) -> SigmaXResult<Decimal> {
        if excess_returns.len() < 2 {
            return Ok(Decimal::ZERO);
        }

        let mean_excess = excess_returns.iter().sum::<Decimal>() / Decimal::from(excess_returns.len());
        
        let variance = excess_returns
            .iter()
            .map(|r| (r - mean_excess).powi(2))
            .sum::<Decimal>() / Decimal::from(excess_returns.len() - 1);
        
        let tracking_error = variance.sqrt().unwrap_or(Decimal::ZERO);
        
        Ok(tracking_error * Decimal::from(252).sqrt().unwrap_or(Decimal::from(16)))
    }

    fn calculate_beta(&self) -> SigmaXResult<Decimal> {
        if self.benchmark_returns.is_empty() || self.returns_history.len() != self.benchmark_returns.len() {
            return Ok(Decimal::ONE);
        }

        let portfolio_mean = self.returns_history.iter().sum::<Decimal>() / Decimal::from(self.returns_history.len());
        let benchmark_mean = self.benchmark_returns.iter().sum::<Decimal>() / Decimal::from(self.benchmark_returns.len());

        let covariance = self.returns_history
            .iter()
            .zip(self.benchmark_returns.iter())
            .map(|(p, b)| (p - portfolio_mean) * (b - benchmark_mean))
            .sum::<Decimal>() / Decimal::from(self.returns_history.len() - 1);

        let benchmark_variance = self.benchmark_returns
            .iter()
            .map(|b| (b - benchmark_mean).powi(2))
            .sum::<Decimal>() / Decimal::from(self.benchmark_returns.len() - 1);

        if benchmark_variance == Decimal::ZERO {
            return Ok(Decimal::ONE);
        }

        Ok(covariance / benchmark_variance)
    }

    fn calculate_average_drawdown(&self) -> SigmaXResult<Decimal> {
        // 简化实现，返回最大回撤的一半作为平均回撤的估计
        Ok(self.max_drawdown / Decimal::from(2))
    }

    fn calculate_ulcer_index(&self) -> SigmaXResult<Decimal> {
        // 简化实现，返回最大回撤的平方根
        Ok(self.max_drawdown.sqrt().unwrap_or(Decimal::ZERO))
    }
}
