//! 风控配置热更新机制
//!
//! 提供风控配置的实时更新功能，无需重启系统即可生效

use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{RwLock, broadcast, mpsc};
use tokio::time::{interval, Instant};
use tracing::{info, warn, error, debug};
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

use sigmax_core::{RiskManagementConfig, SigmaXResult, SigmaXError, ConfigCache};
use sigmax_database::repositories::traits::RiskRepository;
use crate::config_mapper::{RiskConfigMapper, ConfigDiffReport};

/// 配置变更事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConfigChangeEvent {
    /// 配置已更新
    ConfigUpdated {
        old_config: RiskManagementConfig,
        new_config: RiskManagementConfig,
        diff_report: ConfigDiffReport,
        timestamp: DateTime<Utc>,
    },
    /// 配置重载请求
    ReloadRequested {
        requester: String,
        timestamp: DateTime<Utc>,
    },
    /// 配置验证失败
    ValidationFailed {
        config: RiskManagementConfig,
        error: String,
        timestamp: DateTime<Utc>,
    },
}

/// 配置热更新管理器
pub struct ConfigHotReloadManager {
    /// 当前配置
    current_config: Arc<RwLock<RiskManagementConfig>>,
    /// 风控仓储
    repository: Arc<dyn RiskRepository>,
    /// 配置缓存
    cache: Arc<ConfigCache>,
    /// 配置变更事件广播器
    event_sender: broadcast::Sender<ConfigChangeEvent>,
    /// 手动重载请求接收器
    reload_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<String>>>>,
    /// 手动重载请求发送器
    reload_sender: mpsc::UnboundedSender<String>,
    /// 自动检查间隔
    check_interval: Duration,
    /// 最后检查时间
    last_check: Arc<RwLock<Instant>>,
    /// 是否启用自动检查
    auto_check_enabled: bool,
}

impl ConfigHotReloadManager {
    /// 创建新的配置热更新管理器
    pub fn new(
        initial_config: RiskManagementConfig,
        repository: Arc<dyn RiskRepository>,
        cache: Arc<ConfigCache>,
        check_interval: Duration,
    ) -> Self {
        let (event_sender, _) = broadcast::channel(100);
        let (reload_sender, reload_receiver) = mpsc::unbounded_channel();

        Self {
            current_config: Arc::new(RwLock::new(initial_config)),
            repository,
            cache,
            event_sender,
            reload_receiver: Arc::new(RwLock::new(Some(reload_receiver))),
            reload_sender,
            check_interval,
            last_check: Arc::new(RwLock::new(Instant::now())),
            auto_check_enabled: true,
        }
    }

    /// 获取配置变更事件订阅器
    pub fn subscribe_events(&self) -> broadcast::Receiver<ConfigChangeEvent> {
        self.event_sender.subscribe()
    }

    /// 获取手动重载请求发送器
    pub fn get_reload_sender(&self) -> mpsc::UnboundedSender<String> {
        self.reload_sender.clone()
    }

    /// 获取当前配置
    pub async fn get_current_config(&self) -> RiskManagementConfig {
        let config = self.current_config.read().await;
        config.clone()
    }

    /// 手动触发配置重载
    pub async fn reload_config(&self, requester: &str) -> SigmaXResult<bool> {
        info!("🔄 手动触发配置重载，请求者: {}", requester);

        // 发送重载请求事件
        let _ = self.event_sender.send(ConfigChangeEvent::ReloadRequested {
            requester: requester.to_string(),
            timestamp: Utc::now(),
        });

        // 执行配置重载
        self.check_and_update_config().await
    }

    /// 检查并更新配置
    async fn check_and_update_config(&self) -> SigmaXResult<bool> {
        debug!("检查配置更新...");

        // 从数据库加载最新配置
        let new_config = match self.load_latest_config().await {
            Ok(config) => config,
            Err(e) => {
                warn!("加载最新配置失败: {}", e);
                return Ok(false);
            }
        };

        // 获取当前配置
        let current_config = {
            let config = self.current_config.read().await;
            config.clone()
        };

        // 检查配置是否有变化
        if self.configs_equal(&current_config, &new_config) {
            debug!("配置无变化，跳过更新");
            return Ok(false);
        }

        // 验证新配置
        if let Err(e) = self.validate_config(&new_config).await {
            error!("新配置验证失败: {}", e);
            
            let _ = self.event_sender.send(ConfigChangeEvent::ValidationFailed {
                config: new_config,
                error: e.to_string(),
                timestamp: Utc::now(),
            });
            
            return Err(e);
        }

        // 生成配置差异报告
        let strategy_old = RiskConfigMapper::extract_strategy_config(&current_config);
        let strategy_new = RiskConfigMapper::extract_strategy_config(&new_config);
        let diff_report = RiskConfigMapper::create_diff_report(&strategy_old, &strategy_new);

        // 更新配置
        {
            let mut config = self.current_config.write().await;
            *config = new_config.clone();
        }

        // 更新最后检查时间
        {
            let mut last_check = self.last_check.write().await;
            *last_check = Instant::now();
        }

        // 发送配置更新事件
        let _ = self.event_sender.send(ConfigChangeEvent::ConfigUpdated {
            old_config: current_config,
            new_config,
            diff_report,
            timestamp: Utc::now(),
        });

        info!("✅ 配置热更新成功");
        Ok(true)
    }

    /// 从数据库加载最新配置
    async fn load_latest_config(&self) -> SigmaXResult<RiskManagementConfig> {
        // 直接从数据库加载最新配置
        match self.repository.get_risk_config().await {
            Ok(config) => {
                // 更新缓存
                let _ = self.cache.get_or_load_risk_config(|| {
                    async { Ok(config.clone()) }
                }).await;
                Ok(config)
            }
            Err(e) => {
                warn!("从数据库加载配置失败: {}", e);
                Err(e)
            }
        }
    }

    /// 验证配置的有效性
    async fn validate_config(&self, config: &RiskManagementConfig) -> SigmaXResult<()> {
        // 提取策略配置并验证
        let strategy_config = RiskConfigMapper::extract_strategy_config(config);
        RiskConfigMapper::validate_strategy_config(&strategy_config)?;

        // 基础配置验证
        if config.risk_parameters.basic.max_order_amount <= rust_decimal::Decimal::ZERO {
            return Err(SigmaXError::Config("最大订单金额必须大于0".to_string()));
        }

        if config.risk_parameters.basic.max_total_position <= rust_decimal::Decimal::ZERO {
            return Err(SigmaXError::Config("最大总持仓必须大于0".to_string()));
        }

        if config.risk_parameters.trading.max_daily_trades == 0 {
            return Err(SigmaXError::Config("每日最大交易次数必须大于0".to_string()));
        }

        if config.risk_parameters.trading.max_hourly_trades == 0 {
            return Err(SigmaXError::Config("每小时最大交易次数必须大于0".to_string()));
        }

        Ok(())
    }

    /// 比较两个配置是否相等
    fn configs_equal(&self, config1: &RiskManagementConfig, config2: &RiskManagementConfig) -> bool {
        // 简化的配置比较，主要比较关键字段
        config1.risk_parameters.basic.max_order_amount == config2.risk_parameters.basic.max_order_amount &&
        config1.risk_parameters.basic.max_total_position == config2.risk_parameters.basic.max_total_position &&
        config1.risk_parameters.basic.position_size_limit_percent == config2.risk_parameters.basic.position_size_limit_percent &&
        config1.risk_parameters.basic.max_drawdown_percent == config2.risk_parameters.basic.max_drawdown_percent &&
        config1.risk_parameters.trading.max_daily_trades == config2.risk_parameters.trading.max_daily_trades &&
        config1.risk_parameters.trading.max_hourly_trades == config2.risk_parameters.trading.max_hourly_trades &&
        config1.risk_parameters.market.volatility_threshold == config2.risk_parameters.market.volatility_threshold &&
        config1.risk_parameters.advanced.emergency_measures.emergency_stop_threshold == config2.risk_parameters.advanced.emergency_measures.emergency_stop_threshold
    }

    /// 启动自动配置检查任务
    pub async fn start_auto_check(&self) -> SigmaXResult<()> {
        if !self.auto_check_enabled {
            return Ok(());
        }

        info!("🚀 启动配置自动检查任务，检查间隔: {:?}", self.check_interval);

        let current_config = self.current_config.clone();
        let repository = self.repository.clone();
        let cache = self.cache.clone();
        let event_sender = self.event_sender.clone();
        let last_check = self.last_check.clone();
        let check_interval = self.check_interval;

        // 获取重载接收器
        let reload_receiver = {
            let mut receiver_guard = self.reload_receiver.write().await;
            receiver_guard.take()
        };

        if let Some(mut reload_receiver) = reload_receiver {
            tokio::spawn(async move {
                let mut interval_timer = interval(check_interval);
                
                loop {
                    tokio::select! {
                        // 定时检查
                        _ = interval_timer.tick() => {
                            debug!("执行定时配置检查");
                            
                            let manager = ConfigHotReloadManager {
                                current_config: current_config.clone(),
                                repository: repository.clone(),
                                cache: cache.clone(),
                                event_sender: event_sender.clone(),
                                reload_receiver: Arc::new(RwLock::new(None)),
                                reload_sender: mpsc::unbounded_channel().0,
                                check_interval,
                                last_check: last_check.clone(),
                                auto_check_enabled: true,
                            };
                            
                            if let Err(e) = manager.check_and_update_config().await {
                                error!("定时配置检查失败: {}", e);
                            }
                        }
                        
                        // 手动重载请求
                        Some(requester) = reload_receiver.recv() => {
                            info!("收到手动重载请求，请求者: {}", requester);
                            
                            let manager = ConfigHotReloadManager {
                                current_config: current_config.clone(),
                                repository: repository.clone(),
                                cache: cache.clone(),
                                event_sender: event_sender.clone(),
                                reload_receiver: Arc::new(RwLock::new(None)),
                                reload_sender: mpsc::unbounded_channel().0,
                                check_interval,
                                last_check: last_check.clone(),
                                auto_check_enabled: true,
                            };
                            
                            if let Err(e) = manager.check_and_update_config().await {
                                error!("手动配置重载失败: {}", e);
                            }
                        }
                    }
                }
            });
        }

        Ok(())
    }

    /// 停止自动配置检查
    pub fn stop_auto_check(&mut self) {
        self.auto_check_enabled = false;
        info!("⏹️ 停止配置自动检查任务");
    }

    /// 获取配置统计信息
    pub async fn get_stats(&self) -> ConfigStats {
        let last_check = {
            let check = self.last_check.read().await;
            *check
        };

        ConfigStats {
            last_check_time: last_check,
            check_interval: self.check_interval,
            auto_check_enabled: self.auto_check_enabled,
            event_subscribers: self.event_sender.receiver_count(),
        }
    }
}

/// 配置统计信息
#[derive(Debug, Clone)]
pub struct ConfigStats {
    pub last_check_time: Instant,
    pub check_interval: Duration,
    pub auto_check_enabled: bool,
    pub event_subscribers: usize,
}
