//! V2风控系统核心类型定义
//!
//! 设计原则：
//! - 类型安全：使用强类型防止编程错误
//! - 零成本抽象：类型在编译时优化
//! - 清晰语义：类型名称反映业务含义

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use sigmax_core::{Order, Balance, TradingPair};

// ============================================================================
// 核心请求和响应类型
// ============================================================================

/// 统一的风控请求类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskRequest {
    /// 请求唯一标识
    pub request_id: Uuid,
    /// 请求类型
    pub request_type: RiskRequestType,
    /// 风控上下文
    pub context: RiskContext,
    /// 请求时间戳
    pub timestamp: DateTime<Utc>,
}

/// 风控请求类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum RiskRequestType {
    /// 订单风控检查
    OrderCheck {
        order: Order,
        strategy_type: Option<String>,
    },
    /// 持仓风控检查
    PositionCheck {
        balances: Vec<Balance>,
        strategy_type: Option<String>,
    },
    /// 投资组合风控检查
    PortfolioCheck {
        portfolio_id: Uuid,
    },
    /// 批量检查
    BatchCheck {
        requests: Vec<RiskRequestType>,
    },
}

/// 风控上下文信息
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct RiskContext {
    /// 策略ID
    pub strategy_id: Option<Uuid>,
    /// 交易对
    pub trading_pair: Option<TradingPair>,
    /// 交易所ID
    pub exchange_id: Option<String>,
    /// 用户自定义上下文
    pub custom_data: HashMap<String, serde_json::Value>,
}

/// 风控检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskResult {
    /// 请求ID（用于关联）
    pub request_id: Uuid,
    /// 是否通过风控检查
    pub passed: bool,
    /// 风险等级
    pub risk_level: RiskLevel,
    /// 规则违规信息
    pub violations: Vec<RuleViolation>,
    /// 性能指标
    pub metrics: PerformanceMetrics,
    /// 建议信息
    pub suggestions: Vec<String>,
    /// 检查时间戳
    pub timestamp: DateTime<Utc>,
}

// ============================================================================
// 风险等级和违规类型
// ============================================================================

/// 风险等级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, PartialOrd, Eq, Ord)]
pub enum RiskLevel {
    /// 无风险
    None = 0,
    /// 低风险
    Low = 1,
    /// 中等风险
    Medium = 2,
    /// 高风险
    High = 3,
    /// 极高风险（阻断交易）
    Critical = 4,
}

/// 规则违规信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RuleViolation {
    /// 违规规则名称
    pub rule_name: String,
    /// 违规类型
    pub violation_type: ViolationType,
    /// 违规描述
    pub message: String,
    /// 当前值
    pub current_value: Option<f64>,
    /// 阈值
    pub threshold_value: Option<f64>,
    /// 建议动作
    pub suggested_action: Option<String>,
}

/// 违规类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ViolationType {
    /// 超出限制
    ExceedsLimit,
    /// 低于最小值
    BelowMinimum,
    /// 时间窗口违规
    TimeWindow,
    /// 频率限制
    FrequencyLimit,
    /// 自定义违规
    Custom(String),
}

// ============================================================================
// 性能和监控类型
// ============================================================================

/// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct PerformanceMetrics {
    /// 总执行时间（微秒）
    pub total_duration_us: u64,
    /// 规则执行时间分布
    pub rule_durations: HashMap<String, u64>,
    /// 数据库查询时间
    pub db_query_duration_us: u64,
    /// 缓存命中率
    pub cache_hit_rate: f64,
    /// 执行的规则数量
    pub rules_executed: usize,
}

// ============================================================================
// 便捷构造方法
// ============================================================================

impl RiskRequest {
    /// 从订单创建风控请求
    pub fn from_order(order: Order, strategy_type: Option<String>) -> Self {
        Self {
            request_id: Uuid::new_v4(),
            request_type: RiskRequestType::OrderCheck {
                order,
                strategy_type,
            },
            context: RiskContext::default(),
            timestamp: Utc::now(),
        }
    }

    /// 从持仓创建风控请求
    pub fn from_balances(balances: Vec<Balance>, strategy_type: Option<String>) -> Self {
        Self {
            request_id: Uuid::new_v4(),
            request_type: RiskRequestType::PositionCheck {
                balances,
                strategy_type,
            },
            context: RiskContext::default(),
            timestamp: Utc::now(),
        }
    }
}

impl RiskResult {
    /// 创建通过的结果
    pub fn pass(request_id: Uuid) -> Self {
        Self {
            request_id,
            passed: true,
            risk_level: RiskLevel::None,
            violations: Vec::new(),
            metrics: PerformanceMetrics::default(),
            suggestions: Vec::new(),
            timestamp: Utc::now(),
        }
    }

    /// 创建失败的结果
    pub fn fail(request_id: Uuid, violations: Vec<RuleViolation>) -> Self {
        let risk_level = violations.iter()
            .map(|v| match v.violation_type {
                ViolationType::ExceedsLimit => RiskLevel::High,
                ViolationType::FrequencyLimit => RiskLevel::Medium,
                _ => RiskLevel::Low,
            })
            .max()
            .unwrap_or(RiskLevel::Low);

        Self {
            request_id,
            passed: false,
            risk_level,
            violations,
            metrics: PerformanceMetrics::default(),
            suggestions: Vec::new(),
            timestamp: Utc::now(),
        }
    }
}

impl RiskLevel {
    /// 是否应该阻断交易
    pub fn should_block(&self) -> bool {
        matches!(self, RiskLevel::Critical)
    }

    /// 是否需要警告
    pub fn should_warn(&self) -> bool {
        matches!(self, RiskLevel::Medium | RiskLevel::High | RiskLevel::Critical)
    }
}
