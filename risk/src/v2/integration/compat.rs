//! V2风控系统兼容性适配器
//!
//! 提供与现有RiskManager trait的兼容性

use async_trait::async_trait;
use sigmax_core::{SigmaXResult, Order, Balance, RiskManager as CoreRiskManager};
use std::sync::Arc;
use super::super::core::{RiskManager, UnifiedRiskManager, RiskContext};

/// 兼容性风控管理器
///
/// 将V2统一风控管理器适配到现有的RiskManager trait
pub struct CompatRiskManager {
    unified_manager: Arc<dyn RiskManager>,
}

impl CompatRiskManager {
    /// 创建新的兼容性管理器
    pub fn new(unified_manager: Arc<dyn RiskManager>) -> Self {
        Self { unified_manager }
    }

    /// 从UnifiedRiskManager创建
    pub fn from_unified(manager: UnifiedRiskManager) -> Self {
        Self::new(Arc::new(manager))
    }
}

#[async_trait]
impl CoreRiskManager for CompatRiskManager {
    async fn check_order_risk(&self, order: &Order) -> SigmaXResult<bool> {
        let context = RiskContext::default();
        let result = self.unified_manager.check_order_risk(order, &context).await?;
        Ok(result.passed)
    }

    async fn check_position_risk(&self, balances: &[Balance]) -> SigmaXResult<bool> {
        let context = RiskContext::default();
        let result = self.unified_manager.check_position_risk(balances, &context).await?;
        Ok(result.passed)
    }

    async fn get_max_order_size(&self, _trading_pair: &sigmax_core::TradingPair) -> SigmaXResult<rust_decimal::Decimal> {
        // 返回默认的最大订单大小
        Ok(rust_decimal::Decimal::from(10000))
    }
}

/// V2桥接器
///
/// 提供V2系统与其他系统的桥接功能
pub struct V2Bridge;

impl V2Bridge {
    /// 将传统风控管理器包装为统一接口
    pub fn wrap_legacy<T: CoreRiskManager + 'static>(
        legacy_manager: Arc<T>
    ) -> Arc<dyn RiskManager> {
        use super::super::core::manager::CompatibilityAdapter;
        Arc::new(CompatibilityAdapter::new(legacy_manager))
    }

    /// 将统一风控管理器适配为传统接口
    pub fn adapt_to_legacy(
        unified_manager: Arc<dyn RiskManager>
    ) -> Arc<dyn CoreRiskManager> {
        Arc::new(CompatRiskManager::new(unified_manager))
    }
}