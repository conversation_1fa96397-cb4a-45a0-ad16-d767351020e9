//! 统一风控服务集成
//! 
//! 将新的统一风控规则引擎集成到现有的引擎架构中

use std::sync::Arc;
use async_trait::async_trait;
use sigmax_core::{SigmaXResult, SigmaXError, Order, Balance, ServiceContainer};
use sigmax_risk::{RiskCheckResult};
// use sigmax_database::DatabaseManager;
use tracing::{info, debug, warn, error};

/// 统一风控服务（简化版，用于回测）
///
/// 作为现有风控系统和新统一风控引擎之间的桥梁
pub struct UnifiedRiskService {
    /// 是否启用统一风控（用于渐进式迁移）
    enabled: bool,
    /// 是否为回测模式
    is_backtest: bool,
}

impl UnifiedRiskService {
    // 移除了需要DatabaseManager的构造函数，统一使用模拟模式

    /// 创建模拟的统一风控服务（不依赖数据库）
    pub async fn new_mock(
        is_backtest: bool,
        enabled: bool,
        _service_container: Arc<dyn ServiceContainer>,
    ) -> SigmaXResult<Self> {
        info!("🚀 初始化统一风控服务（模拟模式） - 回测模式: {}, 启用状态: {}", is_backtest, enabled);

        info!("✅ 统一风控服务初始化成功（简化模式）");

        Ok(Self {
            enabled,
            is_backtest,
        })
    }

    /// 从服务容器创建统一风控服务
    pub async fn from_service_container(
        service_container: Arc<dyn ServiceContainer>,
        is_backtest: bool,
    ) -> SigmaXResult<Self> {
        // 暂时创建一个模拟的统一风控服务，不依赖数据库
        // 在实际部署时，应该通过service_container注入数据库连接
        Self::new_mock(is_backtest, true, service_container).await
    }
    
    /// 检查订单风险（统一接口）
    pub async fn check_order_risk(
        &self,
        order: &Order,
        strategy_type: Option<&str>,
    ) -> SigmaXResult<RiskCheckResult> {
        if !self.enabled {
            debug!("统一风控未启用，跳过检查");
            return Ok(RiskCheckResult::passed());
        }
        
        debug!("执行统一风控检查 - 订单: {:?}, 策略: {:?}", order.id, strategy_type);

        // 简化版：直接通过所有检查
        let result = RiskCheckResult::passed();

        debug!("统一风控检查通过（简化模式）");

        Ok(result)
    }
    
    /// 检查持仓风险
    pub async fn check_position_risk(
        &self,
        balances: &[Balance],
        strategy_type: Option<&str>,
    ) -> SigmaXResult<RiskCheckResult> {
        if !self.enabled {
            debug!("统一风控未启用，跳过持仓检查");
            return Ok(RiskCheckResult::passed());
        }
        
        debug!("执行统一持仓风控检查 - 资产数量: {}, 策略: {:?}", balances.len(), strategy_type);

        // 简化版：直接通过所有检查
        let result = RiskCheckResult::passed();

        debug!("统一持仓风控检查通过（简化模式）");

        Ok(result)
    }
    
    /// 重新加载风控规则
    pub async fn reload_rules(&self) -> SigmaXResult<()> {
        info!("🔄 重新加载统一风控规则（简化模式）");
        info!("✅ 统一风控规则重新加载完成");
        Ok(())
    }
    
    /// 启用/禁用统一风控
    pub fn set_enabled(&mut self, enabled: bool) {
        info!("{}统一风控服务", if enabled { "启用" } else { "禁用" });
        self.enabled = enabled;
    }
    
    /// 检查服务是否启用
    pub fn is_enabled(&self) -> bool {
        self.enabled
    }
    
    /// 获取执行统计
    pub async fn get_execution_stats(&self) -> serde_json::Value {
        serde_json::json!({
            "total_checks": 0,
            "passed_checks": 0,
            "failed_checks": 0,
            "success_rate": 100.0,
            "avg_execution_time_ms": 0.0,
            "is_backtest": self.is_backtest,
            "enabled": self.enabled
        })
    }
    
    /// 获取统一风控引擎的引用（用于高级操作）
    /// 简化版：返回None，因为没有真实的引擎
    pub fn engine(&self) -> Option<()> {
        None
    }
}

/// 统一风控服务构建器（模拟模式）
#[derive(Default)]
pub struct UnifiedRiskServiceBuilder {
    is_backtest: bool,
    enabled: bool,
}

impl UnifiedRiskServiceBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            is_backtest: false,
            enabled: true,
        }
    }

    /// 设置回测模式
    pub fn with_backtest_mode(mut self, is_backtest: bool) -> Self {
        self.is_backtest = is_backtest;
        self
    }

    /// 设置启用状态
    pub fn with_enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// 构建统一风控服务（模拟模式）
    pub async fn build(self, service_container: Arc<dyn ServiceContainer>) -> SigmaXResult<UnifiedRiskService> {
        UnifiedRiskService::new_mock(self.is_backtest, self.enabled, service_container).await
    }
}

// 移除了重复的Default实现，使用derive(Default)

/// 兼容性适配器：将统一风控结果转换为现有的风控检查结果
pub fn convert_to_legacy_result(unified_result: &RiskCheckResult) -> bool {
    unified_result.passed
}

/// 风控检查增强器：为现有的风控检查添加统一风控支持
pub struct RiskCheckEnhancer {
    unified_service: Option<Arc<UnifiedRiskService>>,
}

impl RiskCheckEnhancer {
    /// 创建新的风控检查增强器
    pub fn new(unified_service: Option<Arc<UnifiedRiskService>>) -> Self {
        Self { unified_service }
    }
    
    /// 增强的订单风控检查
    /// 
    /// 同时执行传统风控和统一风控，确保向后兼容
    pub async fn enhanced_order_check(
        &self,
        order: &Order,
        strategy_type: Option<&str>,
        legacy_check: impl std::future::Future<Output = SigmaXResult<bool>>,
    ) -> SigmaXResult<bool> {
        // 执行传统风控检查
        let legacy_result = legacy_check.await?;
        
        // 如果传统风控失败，直接返回
        if !legacy_result {
            debug!("传统风控检查失败，跳过统一风控检查");
            return Ok(false);
        }
        
        // 执行统一风控检查
        if let Some(unified_service) = &self.unified_service {
            let unified_result = unified_service.check_order_risk(order, strategy_type).await?;
            
            // 返回两者的逻辑与结果
            Ok(legacy_result && unified_result.passed)
        } else {
            // 如果没有统一风控服务，返回传统结果
            Ok(legacy_result)
        }
    }
}