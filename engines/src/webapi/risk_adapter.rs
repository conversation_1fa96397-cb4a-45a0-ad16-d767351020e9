//! WebAPI风控适配器 - 详细结果展示优化
//! 
//! 设计原则：
//! - 详细结果：提供完整的风控检查详情和建议
//! - 审计日志：记录所有风控检查的详细审计信息
//! - 查询缓存：优化API查询性能的缓存策略
//! - 用户友好：提供易于理解的错误信息和建议

use async_trait::async_trait;
use sigmax_core::{EngineType, Order, Balance, SigmaXResult, EngineRiskAdapter};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use num_traits::ToPrimitive;

use crate::risk::{
    adapters::{AdapterBase, AdapterConfig, AdapterMetrics},
    RiskEngine, CacheService, MetricsCollector
};

/// WebAPI风控适配器配置
#[derive(Debug, Clone)]
pub struct WebApiAdapterConfig {
    /// 详细结果模式
    pub detailed_results: bool,
    /// 启用审计日志
    pub enable_audit_logging: bool,
    /// 查询缓存TTL (秒)
    pub query_cache_ttl_secs: u64,
    /// 最大审计日志条目数
    pub max_audit_entries: usize,
    /// 启用用户友好错误信息
    pub user_friendly_errors: bool,
    /// 启用风控建议
    pub enable_risk_suggestions: bool,
    /// API响应超时 (毫秒)
    pub api_timeout_ms: u64,
    /// 启用请求限流
    pub enable_rate_limiting: bool,
    /// 每分钟最大请求数
    pub max_requests_per_minute: u32,
}

impl Default for WebApiAdapterConfig {
    fn default() -> Self {
        Self {
            detailed_results: true,
            enable_audit_logging: true,
            query_cache_ttl_secs: 120, // 2分钟
            max_audit_entries: 10000,
            user_friendly_errors: true,
            enable_risk_suggestions: true,
            api_timeout_ms: 3000, // 3秒
            enable_rate_limiting: true,
            max_requests_per_minute: 1000,
        }
    }
}

/// 详细风控检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetailedRiskResult {
    /// 基本检查结果
    pub passed: bool,
    /// 风险分数 (0.0-1.0)
    pub risk_score: f64,
    /// 检查详情
    pub details: Vec<RiskCheckDetail>,
    /// 用户友好的消息
    pub user_message: Option<String>,
    /// 风控建议
    pub suggestions: Vec<String>,
    /// 检查时间戳
    pub timestamp: DateTime<Utc>,
    /// 检查耗时 (毫秒)
    pub duration_ms: f64,
    /// 缓存状态
    pub from_cache: bool,
}

/// 风控检查详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskCheckDetail {
    /// 规则名称
    pub rule_name: String,
    /// 检查结果
    pub passed: bool,
    /// 详细信息
    pub message: String,
    /// 风险级别
    pub risk_level: RiskLevel,
    /// 相关数据
    pub data: HashMap<String, serde_json::Value>,
}

/// 风险级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

/// 审计日志条目
#[derive(Debug, Clone, Serialize)]
pub struct AuditLogEntry {
    /// 条目ID
    pub id: String,
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 检查类型
    pub check_type: String,
    /// 用户ID (如果有)
    pub user_id: Option<String>,
    /// 会话ID (如果有)
    pub session_id: Option<String>,
    /// 订单ID (如果是订单检查)
    pub order_id: Option<String>,
    /// 检查结果
    pub result: bool,
    /// 风险分数
    pub risk_score: f64,
    /// 详细信息
    pub details: String,
    /// 客户端IP (如果有)
    pub client_ip: Option<String>,
}

/// WebAPI风控适配器
pub struct WebApiRiskAdapter {
    /// 适配器基础功能
    base: AdapterBase,
    /// WebAPI特定配置
    config: WebApiAdapterConfig,
    /// 审计日志存储
    audit_logs: Arc<RwLock<Vec<AuditLogEntry>>>,
    /// 查询缓存
    query_cache: Arc<RwLock<HashMap<String, (DetailedRiskResult, Instant)>>>,
    /// 请求计数器 (用于限流)
    request_counter: Arc<RwLock<HashMap<String, (u32, Instant)>>>,
}

impl WebApiRiskAdapter {
    /// 创建新的WebAPI风控适配器
    pub async fn new(
        risk_engine: Arc<dyn RiskEngine>,
        cache_service: Arc<dyn CacheService>,
        metrics_collector: Arc<dyn MetricsCollector>,
        config: WebApiAdapterConfig,
    ) -> SigmaXResult<Self> {
        info!("Creating WebApiRiskAdapter with detailed results optimization");
        
        let base_config = AdapterConfig {
            enable_cache: true,
            cache_ttl: Duration::from_secs(config.query_cache_ttl_secs),
            enable_metrics: true,
            timeout: Duration::from_millis(config.api_timeout_ms),
            retry_attempts: 1, // WebAPI模式减少重试
        };
        
        let base = AdapterBase::new(
            risk_engine,
            cache_service,
            metrics_collector,
            EngineType::Live, // WebAPI使用Live引擎类型
            base_config,
        );
        
        let adapter = Self {
            base,
            config: config.clone(),
            audit_logs: Arc::new(RwLock::new(Vec::new())),
            query_cache: Arc::new(RwLock::new(HashMap::new())),
            request_counter: Arc::new(RwLock::new(HashMap::new())),
        };
        
        // 启动后台清理任务
        adapter.start_cleanup_tasks().await;
        
        info!("WebApiRiskAdapter created successfully");
        Ok(adapter)
    }
    
    /// 详细订单风控检查
    pub async fn detailed_check_order_risk(
        &self,
        order: &Order,
        user_id: Option<&str>,
        session_id: Option<&str>,
        client_ip: Option<&str>,
    ) -> SigmaXResult<DetailedRiskResult> {
        let start_time = Instant::now();
        
        // 检查限流
        if self.config.enable_rate_limiting {
            self.check_rate_limit(user_id.unwrap_or("anonymous")).await?;
        }
        
        // 检查查询缓存
        let cache_key = self.generate_query_cache_key("order", order, user_id);
        if let Some(cached_result) = self.get_query_cache(&cache_key).await {
            return Ok(cached_result);
        }
        
        debug!("Starting detailed order risk check for order: {:?}", order.id);
        
        // 执行风控检查
        let risk_result = self.base.risk_engine().check_order_risk(order, Some("webapi")).await?;
        
        // 构建详细结果
        let detailed_result = self.build_detailed_result(
            risk_result,
            "order",
            start_time,
            false,
        ).await;
        
        // 缓存结果
        self.set_query_cache(cache_key, detailed_result.clone()).await;
        
        // 记录审计日志
        if self.config.enable_audit_logging {
            self.log_audit_entry(
                "order_check",
                user_id,
                session_id,
                Some(&order.id.to_string()),
                &detailed_result,
                client_ip,
            ).await;
        }
        
        // 记录指标
        self.record_webapi_metrics("order", start_time.elapsed(), &detailed_result).await;
        
        debug!("Detailed order risk check completed: passed={}, score={:.2}", 
               detailed_result.passed, detailed_result.risk_score);
        
        Ok(detailed_result)
    }
    
    /// 详细持仓风控检查
    pub async fn detailed_check_position_risk(
        &self,
        balances: &[Balance],
        user_id: Option<&str>,
        session_id: Option<&str>,
        client_ip: Option<&str>,
    ) -> SigmaXResult<DetailedRiskResult> {
        let start_time = Instant::now();
        
        // 检查限流
        if self.config.enable_rate_limiting {
            self.check_rate_limit(user_id.unwrap_or("anonymous")).await?;
        }
        
        // 检查查询缓存
        let cache_key = self.generate_position_cache_key(balances, user_id);
        if let Some(cached_result) = self.get_query_cache(&cache_key).await {
            return Ok(cached_result);
        }
        
        debug!("Starting detailed position risk check for {} balances", balances.len());
        
        // 执行风控检查
        let risk_result = self.base.risk_engine().check_position_risk(balances, Some("webapi")).await?;
        
        // 构建详细结果
        let detailed_result = self.build_detailed_result(
            risk_result,
            "position",
            start_time,
            false,
        ).await;
        
        // 缓存结果
        self.set_query_cache(cache_key, detailed_result.clone()).await;
        
        // 记录审计日志
        if self.config.enable_audit_logging {
            self.log_audit_entry(
                "position_check",
                user_id,
                session_id,
                None,
                &detailed_result,
                client_ip,
            ).await;
        }
        
        // 记录指标
        self.record_webapi_metrics("position", start_time.elapsed(), &detailed_result).await;
        
        debug!("Detailed position risk check completed: passed={}, score={:.2}", 
               detailed_result.passed, detailed_result.risk_score);
        
        Ok(detailed_result)
    }
    
    /// 获取审计日志
    pub async fn get_audit_logs(
        &self,
        user_id: Option<&str>,
        limit: Option<usize>,
        offset: Option<usize>,
    ) -> Vec<AuditLogEntry> {
        let logs = self.audit_logs.read().await;
        
        let filtered_logs: Vec<_> = if let Some(uid) = user_id {
            logs.iter()
                .filter(|log| log.user_id.as_deref() == Some(uid))
                .cloned()
                .collect()
        } else {
            logs.clone()
        };
        
        let start = offset.unwrap_or(0);
        let end = start + limit.unwrap_or(100);
        
        filtered_logs.into_iter()
            .skip(start)
            .take(end - start)
            .collect()
    }
    
    /// 构建详细结果
    async fn build_detailed_result(
        &self,
        risk_result: sigmax_core::RiskCheckResult,
        check_type: &str,
        start_time: Instant,
        from_cache: bool,
    ) -> DetailedRiskResult {
        let mut details = Vec::new();
        let mut suggestions = Vec::new();
        
        // 构建检查详情
        for rule_result in &risk_result.rule_results {
            let risk_level = if rule_result.passed {
                RiskLevel::Low
            } else {
                RiskLevel::High
            };
            
            let detail = RiskCheckDetail {
                rule_name: rule_result.rule_name.clone(),
                passed: rule_result.passed,
                message: rule_result.message.clone().unwrap_or_else(|| {
                    if rule_result.passed {
                        "检查通过".to_string()
                    } else {
                        "检查失败".to_string()
                    }
                }),
                risk_level,
                data: HashMap::new(),
            };
            
            details.push(detail);
            
            // 生成建议
            if !rule_result.passed && self.config.enable_risk_suggestions {
                suggestions.push(self.generate_suggestion(&rule_result.rule_name));
            }
        }
        
        // 生成用户友好消息
        let user_message = if self.config.user_friendly_errors {
            Some(self.generate_user_message(&risk_result, check_type))
        } else {
            None
        };
        
        DetailedRiskResult {
            passed: risk_result.passed,
            risk_score: risk_result.risk_score,
            details,
            user_message,
            suggestions,
            timestamp: Utc::now(),
            duration_ms: start_time.elapsed().as_millis() as f64,
            from_cache,
        }
    }
    
    /// 生成建议
    fn generate_suggestion(&self, rule_name: &str) -> String {
        match rule_name {
            "max_order_value" => "建议减少订单金额或分批下单".to_string(),
            "max_position_ratio" => "建议减少持仓比例或增加资金".to_string(),
            "max_daily_loss" => "建议暂停交易或调整策略".to_string(),
            _ => "建议检查风控参数设置".to_string(),
        }
    }
    
    /// 生成用户友好消息
    fn generate_user_message(&self, risk_result: &sigmax_core::RiskCheckResult, check_type: &str) -> String {
        if risk_result.passed {
            match check_type {
                "order" => "订单风控检查通过，可以正常交易".to_string(),
                "position" => "持仓风控检查通过，当前持仓安全".to_string(),
                _ => "风控检查通过".to_string(),
            }
        } else {
            match check_type {
                "order" => format!("订单被风控拒绝：{}", 
                    risk_result.failed_rules.first().map(|r| r.failure_reason.as_str()).unwrap_or("未知原因")),
                "position" => format!("持仓存在风险：{}", 
                    risk_result.failed_rules.first().map(|r| r.failure_reason.as_str()).unwrap_or("未知原因")),
                _ => format!("风控检查失败：{}", 
                    risk_result.failed_rules.first().map(|r| r.failure_reason.as_str()).unwrap_or("未知原因")),
            }
        }
    }
    
    /// 检查限流
    async fn check_rate_limit(&self, user_id: &str) -> SigmaXResult<()> {
        let mut counter = self.request_counter.write().await;
        let now = Instant::now();
        
        // 清理过期的计数器
        counter.retain(|_, (_, timestamp)| now.duration_since(*timestamp) < Duration::from_secs(60));
        
        // 检查当前用户的请求数
        let (count, _) = counter.entry(user_id.to_string()).or_insert((0, now));
        
        if *count >= self.config.max_requests_per_minute {
            return Err(sigmax_core::SigmaXError::RateLimitExceeded(
                format!("User {} exceeded rate limit", user_id)
            ));
        }
        
        *count += 1;
        Ok(())
    }
    
    /// 生成查询缓存键
    fn generate_query_cache_key(&self, check_type: &str, order: &Order, user_id: Option<&str>) -> String {
        format!("webapi:{}:{}:{}:{}", 
                check_type,
                order.id,
                user_id.unwrap_or("anonymous"),
                order.quantity.to_f64().unwrap_or(0.0))
    }
    
    /// 生成持仓缓存键
    fn generate_position_cache_key(&self, balances: &[Balance], user_id: Option<&str>) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        for balance in balances {
            balance.asset.hash(&mut hasher);
            balance.free.to_string().hash(&mut hasher);
        }
        
        format!("webapi:position:{}:{}", 
                user_id.unwrap_or("anonymous"),
                hasher.finish())
    }
    
    /// 获取查询缓存
    async fn get_query_cache(&self, key: &str) -> Option<DetailedRiskResult> {
        let cache = self.query_cache.read().await;
        if let Some((result, timestamp)) = cache.get(key) {
            if timestamp.elapsed() < Duration::from_secs(self.config.query_cache_ttl_secs) {
                let mut cached_result = result.clone();
                cached_result.from_cache = true;
                return Some(cached_result);
            }
        }
        None
    }
    
    /// 设置查询缓存
    async fn set_query_cache(&self, key: String, result: DetailedRiskResult) {
        let mut cache = self.query_cache.write().await;
        cache.insert(key, (result, Instant::now()));
    }
    
    /// 记录审计日志
    async fn log_audit_entry(
        &self,
        check_type: &str,
        user_id: Option<&str>,
        session_id: Option<&str>,
        order_id: Option<&str>,
        result: &DetailedRiskResult,
        client_ip: Option<&str>,
    ) {
        let entry = AuditLogEntry {
            id: format!("audit_{}_{}", check_type, chrono::Utc::now().timestamp_nanos()),
            timestamp: Utc::now(),
            check_type: check_type.to_string(),
            user_id: user_id.map(|s| s.to_string()),
            session_id: session_id.map(|s| s.to_string()),
            order_id: order_id.map(|s| s.to_string()),
            result: result.passed,
            risk_score: result.risk_score,
            details: serde_json::to_string(&result.details).unwrap_or_default(),
            client_ip: client_ip.map(|s| s.to_string()),
        };
        
        let mut logs = self.audit_logs.write().await;
        logs.push(entry);
        
        // 限制日志数量
        if logs.len() > self.config.max_audit_entries {
            logs.remove(0);
        }
    }
    
    /// 记录WebAPI指标
    async fn record_webapi_metrics(
        &self,
        check_type: &str,
        duration: Duration,
        result: &DetailedRiskResult,
    ) {
        if self.base.config().enable_metrics {
            self.base.metrics_collector().record_risk_check(result.passed).await;
            self.base.metrics_collector().record_latency(&format!("webapi_{}", check_type), duration).await;
            
            if result.from_cache {
                self.base.metrics_collector().record_cache_hit().await;
            }
        }
    }
    
    /// 启动后台清理任务
    async fn start_cleanup_tasks(&self) {
        let query_cache = self.query_cache.clone();
        let request_counter = self.request_counter.clone();
        let audit_logs = self.audit_logs.clone();
        let max_audit_entries = self.config.max_audit_entries;
        
        // 查询缓存清理任务
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(300)); // 每5分钟清理一次
            loop {
                interval.tick().await;
                
                let mut cache = query_cache.write().await;
                let ttl = Duration::from_secs(120); // 2分钟TTL
                cache.retain(|_, (_, timestamp)| timestamp.elapsed() < ttl);
                
                debug!("Cleaned query cache, remaining entries: {}", cache.len());
            }
        });
        
        // 请求计数器清理任务
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60)); // 每分钟清理一次
            loop {
                interval.tick().await;
                
                let mut counter = request_counter.write().await;
                let now = Instant::now();
                counter.retain(|_, (_, timestamp)| now.duration_since(*timestamp) < Duration::from_secs(60));
                
                debug!("Cleaned request counter, remaining entries: {}", counter.len());
            }
        });
        
        // 审计日志清理任务
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(3600)); // 每小时清理一次
            loop {
                interval.tick().await;
                
                let mut logs = audit_logs.write().await;
                if logs.len() > max_audit_entries {
                    let remove_count = logs.len() - max_audit_entries;
                    logs.drain(0..remove_count);
                    debug!("Cleaned {} audit log entries", remove_count);
                }
            }
        });
    }
}

#[async_trait]
impl EngineRiskAdapter for WebApiRiskAdapter {
    async fn check_order_risk(&self, order: &Order) -> SigmaXResult<bool> {
        let result = self.detailed_check_order_risk(order, None, None, None).await?;
        Ok(result.passed)
    }
    
    async fn check_position_risk(&self, balances: &[Balance]) -> SigmaXResult<bool> {
        let result = self.detailed_check_position_risk(balances, None, None, None).await?;
        Ok(result.passed)
    }
    
    fn engine_type(&self) -> EngineType {
        EngineType::Live
    }
    
    async fn get_metrics(&self) -> SigmaXResult<AdapterMetrics> {
        Ok(AdapterMetrics {
            engine_type: EngineType::Live,
            cache_hit_rate: 0.85, // WebAPI缓存命中率
            avg_latency_ms: 150.0, // WebAPI平均延迟
            throughput_rps: 100.0, // WebAPI吞吐量
            error_rate: 0.01, // WebAPI错误率
        })
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_webapi_adapter_config_default() {
        let config = WebApiAdapterConfig::default();
        assert!(config.detailed_results);
        assert!(config.enable_audit_logging);
        assert_eq!(config.query_cache_ttl_secs, 120);
        assert!(config.user_friendly_errors);
    }
    
    #[test]
    fn test_risk_level_serialization() {
        let level = RiskLevel::High;
        let serialized = serde_json::to_string(&level).unwrap();
        assert_eq!(serialized, "\"High\"");
    }
}
