//! V2风控系统核心模块
//!
//! 包含微内核引擎、类型定义、构建器和统一接口

pub mod types;
pub mod risk_core;
pub mod builder;
pub mod manager;

// 重新导出核心类型
pub use types::{
    RiskRequest, RiskResult, RiskLevel, RiskRequestType, RiskContext,
    RuleViolation, ViolationType, PerformanceMetrics
};

pub use risk_core::{RiskCore, ExecutionStats};
pub use builder::RiskCoreBuilder;
pub use manager::{RiskManager, UnifiedRiskManager};
