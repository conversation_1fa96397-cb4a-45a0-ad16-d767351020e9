//! 相关性风险模型
//! 
//! 实现多资产相关性分析和风险集中度评估

use sigmax_core::{SigmaXResult, SigmaXError, TradingPair};
use rust_decimal::Decimal;
use std::collections::{HashMap, VecDeque};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

/// 相关性风险指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorrelationRiskMetrics {
    /// 投资组合整体相关性风险评分 (0-1)
    pub portfolio_correlation_risk: f64,
    /// 最大相关性系数
    pub max_correlation: f64,
    /// 平均相关性系数
    pub average_correlation: f64,
    /// 风险集中度指标
    pub concentration_risk: f64,
    /// 分散化效益
    pub diversification_benefit: f64,
    /// 相关性矩阵
    pub correlation_matrix: HashMap<String, HashMap<String, f64>>,
    /// 计算时间戳
    pub calculated_at: DateTime<Utc>,
}

/// 资产收益率数据
#[derive(Debug, Clone)]
pub struct AssetReturns {
    /// 交易对
    pub trading_pair: TradingPair,
    /// 历史收益率
    pub returns: VecDeque<Decimal>,
    /// 投资组合权重
    pub weight: f64,
}

/// 相关性风险计算器
pub struct CorrelationRiskCalculator {
    /// 各资产的收益率数据
    asset_returns: HashMap<String, AssetReturns>,
    /// 最大历史长度
    max_history_length: usize,
    /// 相关性计算的最小数据点数
    min_data_points: usize,
}

impl CorrelationRiskCalculator {
    /// 创建新的相关性风险计算器
    pub fn new(max_history_length: usize, min_data_points: usize) -> Self {
        Self {
            asset_returns: HashMap::new(),
            max_history_length,
            min_data_points: min_data_points.max(10), // 至少需要10个数据点
        }
    }

    /// 添加资产
    pub fn add_asset(&mut self, trading_pair: TradingPair, weight: f64) {
        let asset_key = format!("{}_{}", trading_pair.base, trading_pair.quote);
        self.asset_returns.insert(asset_key, AssetReturns {
            trading_pair,
            returns: VecDeque::new(),
            weight,
        });
    }

    /// 更新资产权重
    pub fn update_asset_weight(&mut self, trading_pair: &TradingPair, weight: f64) {
        let asset_key = format!("{}_{}", trading_pair.base, trading_pair.quote);
        if let Some(asset) = self.asset_returns.get_mut(&asset_key) {
            asset.weight = weight;
        }
    }

    /// 添加资产收益率数据
    pub fn add_asset_return(&mut self, trading_pair: &TradingPair, return_rate: Decimal) -> SigmaXResult<()> {
        let asset_key = format!("{}_{}", trading_pair.base, trading_pair.quote);
        
        let asset = self.asset_returns.get_mut(&asset_key)
            .ok_or_else(|| SigmaXError::InvalidOperation(
                format!("资产 {} 未注册", asset_key)
            ))?;
        
        asset.returns.push_back(return_rate);
        
        // 保持历史数据在指定长度内
        if asset.returns.len() > self.max_history_length {
            asset.returns.pop_front();
        }
        
        Ok(())
    }

    /// 计算相关性风险指标
    pub fn calculate_correlation_risk(&self) -> SigmaXResult<CorrelationRiskMetrics> {
        if self.asset_returns.len() < 2 {
            return Err(SigmaXError::InvalidOperation(
                "需要至少2个资产来计算相关性风险".to_string()
            ));
        }

        // 检查数据充足性
        for (asset_key, asset) in &self.asset_returns {
            if asset.returns.len() < self.min_data_points {
                return Err(SigmaXError::InvalidOperation(
                    format!("资产 {} 的历史数据不足，需要至少 {} 个数据点", 
                           asset_key, self.min_data_points)
                ));
            }
        }

        let correlation_matrix = self.calculate_correlation_matrix()?;
        let portfolio_correlation_risk = self.calculate_portfolio_correlation_risk(&correlation_matrix)?;
        let (max_correlation, average_correlation) = self.calculate_correlation_statistics(&correlation_matrix);
        let concentration_risk = self.calculate_concentration_risk()?;
        let diversification_benefit = self.calculate_diversification_benefit(&correlation_matrix)?;

        Ok(CorrelationRiskMetrics {
            portfolio_correlation_risk,
            max_correlation,
            average_correlation,
            concentration_risk,
            diversification_benefit,
            correlation_matrix,
            calculated_at: Utc::now(),
        })
    }

    /// 计算相关性矩阵
    fn calculate_correlation_matrix(&self) -> SigmaXResult<HashMap<String, HashMap<String, f64>>> {
        let mut matrix = HashMap::new();
        let asset_keys: Vec<String> = self.asset_returns.keys().cloned().collect();

        for asset1 in &asset_keys {
            let mut row = HashMap::new();
            
            for asset2 in &asset_keys {
                let correlation = if asset1 == asset2 {
                    1.0 // 自相关为1
                } else {
                    self.calculate_pearson_correlation(asset1, asset2)?
                };
                
                row.insert(asset2.clone(), correlation);
            }
            
            matrix.insert(asset1.clone(), row);
        }

        Ok(matrix)
    }

    /// 计算皮尔逊相关系数
    fn calculate_pearson_correlation(&self, asset1: &str, asset2: &str) -> SigmaXResult<f64> {
        let returns1 = &self.asset_returns[asset1].returns;
        let returns2 = &self.asset_returns[asset2].returns;
        
        let min_length = returns1.len().min(returns2.len());
        if min_length < self.min_data_points {
            return Ok(0.0);
        }

        // 取相同长度的数据
        let data1: Vec<f64> = returns1.iter()
            .rev()
            .take(min_length)
            .map(|r| r.to_string().parse().unwrap_or(0.0))
            .collect();
        
        let data2: Vec<f64> = returns2.iter()
            .rev()
            .take(min_length)
            .map(|r| r.to_string().parse().unwrap_or(0.0))
            .collect();

        // 计算均值
        let mean1 = data1.iter().sum::<f64>() / data1.len() as f64;
        let mean2 = data2.iter().sum::<f64>() / data2.len() as f64;

        // 计算协方差和方差
        let mut covariance = 0.0;
        let mut variance1 = 0.0;
        let mut variance2 = 0.0;

        for i in 0..data1.len() {
            let diff1 = data1[i] - mean1;
            let diff2 = data2[i] - mean2;
            
            covariance += diff1 * diff2;
            variance1 += diff1 * diff1;
            variance2 += diff2 * diff2;
        }

        let n = data1.len() as f64;
        covariance /= n - 1.0;
        variance1 /= n - 1.0;
        variance2 /= n - 1.0;

        // 计算相关系数
        let std1 = variance1.sqrt();
        let std2 = variance2.sqrt();

        if std1 == 0.0 || std2 == 0.0 {
            Ok(0.0)
        } else {
            let correlation = covariance / (std1 * std2);
            Ok(correlation.max(-1.0).min(1.0)) // 限制在[-1, 1]范围内
        }
    }

    /// 计算投资组合相关性风险
    fn calculate_portfolio_correlation_risk(&self, correlation_matrix: &HashMap<String, HashMap<String, f64>>) -> SigmaXResult<f64> {
        let mut weighted_correlation_sum = 0.0;
        let mut total_weight_product = 0.0;

        let asset_keys: Vec<String> = self.asset_returns.keys().cloned().collect();

        for asset1 in &asset_keys {
            for asset2 in &asset_keys {
                if asset1 != asset2 {
                    let weight1 = self.asset_returns[asset1].weight;
                    let weight2 = self.asset_returns[asset2].weight;
                    let correlation = correlation_matrix[asset1][asset2].abs(); // 使用绝对值
                    
                    weighted_correlation_sum += weight1 * weight2 * correlation;
                    total_weight_product += weight1 * weight2;
                }
            }
        }

        let portfolio_correlation_risk = if total_weight_product > 0.0 {
            weighted_correlation_sum / total_weight_product
        } else {
            0.0
        };

        Ok(portfolio_correlation_risk)
    }

    /// 计算相关性统计信息
    fn calculate_correlation_statistics(&self, correlation_matrix: &HashMap<String, HashMap<String, f64>>) -> (f64, f64) {
        let mut correlations = Vec::new();

        for (asset1, row) in correlation_matrix {
            for (asset2, &correlation) in row {
                if asset1 != asset2 {
                    correlations.push(correlation.abs());
                }
            }
        }

        if correlations.is_empty() {
            return (0.0, 0.0);
        }

        let max_correlation = correlations.iter().fold(0.0f64, |a, &b| a.max(b));
        let average_correlation = correlations.iter().sum::<f64>() / correlations.len() as f64;

        (max_correlation, average_correlation)
    }

    /// 计算风险集中度
    fn calculate_concentration_risk(&self) -> SigmaXResult<f64> {
        // 使用赫芬达尔指数 (Herfindahl Index) 计算集中度
        let mut herfindahl_index = 0.0;
        
        for asset in self.asset_returns.values() {
            herfindahl_index += asset.weight * asset.weight;
        }

        // 标准化到[0,1]范围
        let n = self.asset_returns.len() as f64;
        let min_herfindahl = 1.0 / n; // 完全分散时的最小值
        let max_herfindahl = 1.0;     // 完全集中时的最大值

        let normalized_concentration = if max_herfindahl > min_herfindahl {
            (herfindahl_index - min_herfindahl) / (max_herfindahl - min_herfindahl)
        } else {
            0.0
        };

        Ok(normalized_concentration.max(0.0).min(1.0))
    }

    /// 计算分散化效益
    fn calculate_diversification_benefit(&self, correlation_matrix: &HashMap<String, HashMap<String, f64>>) -> SigmaXResult<f64> {
        // 分散化效益 = 1 - 投资组合相关性风险
        let portfolio_correlation_risk = self.calculate_portfolio_correlation_risk(correlation_matrix)?;
        Ok(1.0 - portfolio_correlation_risk)
    }

    /// 获取资产数量
    pub fn get_asset_count(&self) -> usize {
        self.asset_returns.len()
    }

    /// 获取资产列表
    pub fn get_asset_list(&self) -> Vec<TradingPair> {
        self.asset_returns.values()
            .map(|asset| asset.trading_pair.clone())
            .collect()
    }

    /// 检查数据充足性
    pub fn check_data_sufficiency(&self) -> HashMap<String, bool> {
        self.asset_returns.iter()
            .map(|(key, asset)| {
                (key.clone(), asset.returns.len() >= self.min_data_points)
            })
            .collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rust_decimal_macros::dec;

    #[test]
    fn test_correlation_calculation() {
        let mut calculator = CorrelationRiskCalculator::new(100, 20);
        
        let btc_usdt = TradingPair::new("BTC".to_string(), "USDT".to_string());
        let eth_usdt = TradingPair::new("ETH".to_string(), "USDT".to_string());
        
        calculator.add_asset(btc_usdt.clone(), 0.6);
        calculator.add_asset(eth_usdt.clone(), 0.4);
        
        // 添加相关的测试数据
        for i in 0..30 {
            let btc_return = dec!(0.01) + Decimal::from(i) * dec!(0.001);
            let eth_return = dec!(0.008) + Decimal::from(i) * dec!(0.0008); // 与BTC正相关
            
            calculator.add_asset_return(&btc_usdt, btc_return).unwrap();
            calculator.add_asset_return(&eth_usdt, eth_return).unwrap();
        }
        
        let metrics = calculator.calculate_correlation_risk().unwrap();
        
        assert!(metrics.portfolio_correlation_risk >= 0.0);
        assert!(metrics.portfolio_correlation_risk <= 1.0);
        assert!(metrics.max_correlation >= 0.0);
        assert!(metrics.average_correlation >= 0.0);
        assert!(metrics.concentration_risk >= 0.0);
        assert!(metrics.concentration_risk <= 1.0);
    }

    #[test]
    fn test_pearson_correlation() {
        let mut calculator = CorrelationRiskCalculator::new(100, 10);
        
        let btc_usdt = TradingPair::new("BTC".to_string(), "USDT".to_string());
        let eth_usdt = TradingPair::new("ETH".to_string(), "USDT".to_string());
        
        calculator.add_asset(btc_usdt.clone(), 0.5);
        calculator.add_asset(eth_usdt.clone(), 0.5);
        
        // 添加完全正相关的数据
        for i in 0..15 {
            let return_rate = Decimal::from(i) * dec!(0.001);
            calculator.add_asset_return(&btc_usdt, return_rate).unwrap();
            calculator.add_asset_return(&eth_usdt, return_rate).unwrap(); // 相同的收益率
        }
        
        let correlation = calculator.calculate_pearson_correlation("BTC_USDT", "ETH_USDT").unwrap();
        
        // 完全正相关应该接近1.0
        assert!(correlation > 0.9);
    }
}
