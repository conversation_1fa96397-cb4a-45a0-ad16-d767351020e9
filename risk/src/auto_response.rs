//! 自动风险响应系统
//!
//! 整合各个响应模块，提供统一的自动风险响应服务

use crate::alert_system::RiskAlert;
use crate::response_executor::ResponseExecutor;
use sigmax_core::{SigmaXResult, SigmaXError};
use tokio::sync::{mpsc, RwLock};
use std::sync::Arc;
use std::collections::HashMap;
use uuid::Uuid;
use tracing::{info, warn, error};

// 重新导出模块中的类型，保持向后兼容性
pub use crate::response_actions::{AutoResponseAction, LogLevel, ActionRiskLevel};
pub use crate::response_rules::{AutoResponseRule, TriggerCondition, ThresholdCondition, TimeCondition, ComparisonOperator};
pub use crate::response_executor::{ResponseExecutionRecord, ExecutionStatus, ActionResult, ResponseStatistics};
pub use crate::response_interfaces::{StrategyController, OrderManager, NotificationSender, StrategyStatus, NotificationChannel};



/// 自动风险响应系统
///
/// 整合规则管理、执行器和接口，提供完整的自动响应服务
pub struct AutoRiskResponseSystem {
    /// 响应规则存储
    response_rules: Arc<RwLock<HashMap<Uuid, AutoResponseRule>>>,
    /// 响应执行器
    executor: Arc<ResponseExecutor>,
    /// 响应队列发送器
    response_sender: mpsc::UnboundedSender<RiskAlert>,
    /// 响应队列接收器
    response_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<RiskAlert>>>>,
}



impl AutoRiskResponseSystem {
    /// 创建新的自动风险响应系统
    pub fn new() -> Self {
        let (response_sender, response_receiver) = mpsc::unbounded_channel();

        Self {
            response_rules: Arc::new(RwLock::new(HashMap::new())),
            executor: Arc::new(ResponseExecutor::new()),
            response_sender,
            response_receiver: Arc::new(RwLock::new(Some(response_receiver))),
        }
    }

    /// 设置策略控制器
    pub fn set_strategy_controller(&mut self, controller: Arc<dyn StrategyController>) {
        // 获取可变引用需要使用 Arc::get_mut
        if let Some(executor) = Arc::get_mut(&mut self.executor) {
            executor.set_strategy_controller(controller);
        } else {
            warn!("无法设置策略控制器：执行器正在被其他地方使用");
        }
    }

    /// 设置订单管理器
    pub fn set_order_manager(&mut self, manager: Arc<dyn OrderManager>) {
        if let Some(executor) = Arc::get_mut(&mut self.executor) {
            executor.set_order_manager(manager);
        } else {
            warn!("无法设置订单管理器：执行器正在被其他地方使用");
        }
    }

    /// 设置通知发送器
    pub fn set_notification_sender(&mut self, sender: Arc<dyn NotificationSender>) {
        if let Some(executor) = Arc::get_mut(&mut self.executor) {
            executor.set_notification_sender(sender);
        } else {
            warn!("无法设置通知发送器：执行器正在被其他地方使用");
        }
    }

    /// 添加响应规则
    pub async fn add_response_rule(&self, rule: AutoResponseRule) -> SigmaXResult<()> {
        let mut rules = self.response_rules.write().await;
        rules.insert(rule.rule_id, rule);
        info!("添加自动响应规则，总数: {}", rules.len());
        Ok(())
    }

    /// 移除响应规则
    pub async fn remove_response_rule(&self, rule_id: Uuid) -> SigmaXResult<()> {
        let mut rules = self.response_rules.write().await;
        rules.remove(&rule_id);
        info!("移除自动响应规则: {}", rule_id);
        Ok(())
    }

    /// 处理风险预警
    pub async fn handle_risk_alert(&self, alert: RiskAlert) -> SigmaXResult<()> {
        // 发送到响应队列
        if let Err(e) = self.response_sender.send(alert) {
            error!("发送预警到响应队列失败: {}", e);
            return Err(SigmaXError::Internal(format!("响应队列发送失败: {}", e)));
        }
        
        Ok(())
    }

    /// 启动响应处理器
    pub async fn start_response_processor(&self) -> SigmaXResult<()> {
        let mut receiver = {
            let mut receiver_guard = self.response_receiver.write().await;
            receiver_guard.take()
                .ok_or_else(|| SigmaXError::Internal("响应处理器已经启动".to_string()))?
        };

        let response_rules = self.response_rules.clone();
        let executor = self.executor.clone();
        
        tokio::spawn(async move {
            info!("启动自动风险响应处理器");

            while let Some(alert) = receiver.recv().await {
                let applicable_rules = Self::get_applicable_rules(&alert, &response_rules).await;

                for mut rule in applicable_rules {
                    // 检查规则是否可以执行
                    if !rule.can_execute() {
                        continue;
                    }

                    // 使用执行器执行响应
                    match executor.execute_response(&rule, &alert).await {
                        Ok(record) => {
                            info!("自动响应执行完成: 规则 {} - 状态: {:?}", rule.rule_id, record.execution_status);

                            // 更新规则执行计数
                            rule.record_execution();

                            // 更新规则存储中的执行计数
                            if let Ok(mut rules) = response_rules.try_write() {
                                if let Some(stored_rule) = rules.get_mut(&rule.rule_id) {
                                    stored_rule.record_execution();
                                }
                            }
                        }
                        Err(e) => {
                            error!("自动响应执行失败: 规则 {} - {}", rule.rule_id, e);
                        }
                    }

                }
            }
            
            info!("自动风险响应处理器已停止");
        });
        
        Ok(())
    }

    /// 获取适用的响应规则
    async fn get_applicable_rules(
        alert: &RiskAlert,
        response_rules: &Arc<RwLock<HashMap<Uuid, AutoResponseRule>>>,
    ) -> Vec<AutoResponseRule> {
        let rules = response_rules.read().await;

        rules.values()
            .filter(|rule| {
                // 使用规则自身的方法检查是否可以执行
                rule.can_execute() && rule.matches_alert(alert)
            })
            .cloned()
            .collect()
    }

    /// 获取所有响应规则
    pub async fn get_all_rules(&self) -> Vec<AutoResponseRule> {
        let rules = self.response_rules.read().await;
        rules.values().cloned().collect()
    }

    /// 获取特定规则
    pub async fn get_rule(&self, rule_id: Uuid) -> Option<AutoResponseRule> {
        let rules = self.response_rules.read().await;
        rules.get(&rule_id).cloned()
    }

    /// 更新规则
    pub async fn update_rule(&self, rule: AutoResponseRule) -> SigmaXResult<()> {
        let mut rules = self.response_rules.write().await;
        rules.insert(rule.rule_id, rule);
        Ok(())
    }

    /// 启用/禁用规则
    pub async fn toggle_rule(&self, rule_id: Uuid, enabled: bool) -> SigmaXResult<()> {
        let mut rules = self.response_rules.write().await;
        if let Some(rule) = rules.get_mut(&rule_id) {
            rule.enabled = enabled;
            info!("规则 {} 已{}", rule_id, if enabled { "启用" } else { "禁用" });
            Ok(())
        } else {
            Err(SigmaXError::NotFound(format!("规则 {} 不存在", rule_id)))
        }
    }

    /// 获取执行统计
    pub async fn get_response_statistics(&self) -> ResponseStatistics {
        self.executor.get_statistics().await
    }

    /// 获取执行记录
    pub async fn get_execution_records(&self) -> Vec<ResponseExecutionRecord> {
        self.executor.get_execution_records().await
    }

    /// 清理旧的执行记录
    pub async fn cleanup_old_records(&self, max_records: usize) {
        self.executor.cleanup_old_records(max_records).await;
    }


}

impl Default for AutoRiskResponseSystem {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::alert_system::{AlertType, AlertSeverity};
    use std::collections::HashMap;

    // 模拟策略控制器
    struct MockStrategyController;

    #[async_trait::async_trait]
    impl StrategyController for MockStrategyController {
        async fn pause_strategy(&self, _strategy_id: sigmax_core::StrategyId, _duration_minutes: Option<u32>) -> SigmaXResult<()> {
            Ok(())
        }

        async fn stop_strategy(&self, _strategy_id: sigmax_core::StrategyId) -> SigmaXResult<()> {
            Ok(())
        }

        async fn adjust_risk_parameters(&self, _strategy_id: sigmax_core::StrategyId, _adjustments: HashMap<String, f64>) -> SigmaXResult<()> {
            Ok(())
        }

        async fn reduce_position_size(&self, _strategy_id: sigmax_core::StrategyId, _reduction_percentage: f64) -> SigmaXResult<()> {
            Ok(())
        }

        async fn get_strategy_status(&self, _strategy_id: sigmax_core::StrategyId) -> SigmaXResult<StrategyStatus> {
            Ok(StrategyStatus::Running)
        }

        async fn resume_strategy(&self, _strategy_id: sigmax_core::StrategyId) -> SigmaXResult<()> {
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_auto_response_system_creation() {
        let response_system = AutoRiskResponseSystem::new();

        // 测试基本功能
        let rules = response_system.get_all_rules().await;
        assert!(rules.is_empty());

        let stats = response_system.get_response_statistics().await;
        assert_eq!(stats.total_executions, 0);
    }

    #[tokio::test]
    async fn test_rule_management() {
        let response_system = AutoRiskResponseSystem::new();

        // 创建测试规则
        let rule = AutoResponseRule::new(
            "测试规则".to_string(),
            TriggerCondition {
                alert_types: vec![AlertType::VarBreach],
                min_severity: AlertSeverity::Warning,
                threshold_conditions: vec![],
                time_conditions: None,
            },
            vec![AutoResponseAction::LogEvent {
                message: "测试日志".to_string(),
                level: LogLevel::Info,
            }],
        );

        let rule_id = rule.rule_id;

        // 添加规则
        response_system.add_response_rule(rule).await.unwrap();

        // 验证规则已添加
        let rules = response_system.get_all_rules().await;
        assert_eq!(rules.len(), 1);

        // 获取特定规则
        let retrieved_rule = response_system.get_rule(rule_id).await;
        assert!(retrieved_rule.is_some());

        // 禁用规则
        response_system.toggle_rule(rule_id, false).await.unwrap();
        let disabled_rule = response_system.get_rule(rule_id).await.unwrap();
        assert!(!disabled_rule.enabled);

        // 删除规则
        response_system.remove_response_rule(rule_id).await.unwrap();
        let rules_after_removal = response_system.get_all_rules().await;
        assert!(rules_after_removal.is_empty());
    }
}
